name: one_drop
description: A new Flutter project.

publish_to: "none" # Remove this line if you wish to publish to pub.dev

version: 1.0.10+23

environment:
  sdk: '>=3.0.5 <4.0.0'

dependencies:
  flutter:
    sdk: flutter

  cupertino_icons: ^1.0.8
  easy_localization: ^3.0.7
  pinput: ^5.0.1
  fl_chart: ^0.63.0
  google_fonts: ^6.2.1
  flutter_svg: ^2.0.17
  dio: ^5.7.0
  get_it: ^8.0.3
  # shared_preferences: ^2.3.5
  bot_toast: ^4.1.3
  provider: ^6.1.2
  rxdart: ^0.28.0
  cached_network_image: ^3.4.1
  flutter_carousel_widget: ^3.1.0
  google_maps_flutter: ^2.5.0
  firebase_core: ^3.10.1
  firebase_messaging: ^15.2.1
  image_picker: ^1.1.2
  logging: ^1.3.0
  # go_sell_sdk_flutter: ^2.1.3
  myfatoorah_flutter: ^3.1.5
  location: ^7.0.1
  url_launcher: ^6.3.1
  # flutter_inappwebview: ^6.1.5
  path: ^1.9.0
  smooth_page_indicator: ^1.1.0
  connectivity_plus: ^5.0.2
  shared_preferences: ^2.3.3
  flutter_inappwebview: ^6.0.0
  font_awesome_flutter: ^10.8.0

dev_dependencies:
  flutter_test:
    sdk: flutter

  # flutter_lints: ^5.0.0

flutter:
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/images/
    - assets/translations/
  #   - images/a_dot_ham.jpeg

  fonts:
    - family: Almarai
      fonts:
        - asset: assets/fonts/Almarai-Regular.ttf
        - asset: assets/fonts/Almarai-Light.ttf
          weight: 300
        - asset: assets/fonts/Almarai-Bold.ttf
          weight: 700
        - asset: assets/fonts/Almarai-ExtraBold.ttf
          weight: 800
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
