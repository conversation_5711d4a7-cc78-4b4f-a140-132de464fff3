                    -H/Users/<USER>/src/flutter/packages/flutter_tools/gradle/src/main/groovy
-DCMAKE_SYSTEM_NAME=Android
-DCMAKE_EXPORT_COMPILE_COMMANDS=ON
-DCMAKE_SYSTEM_VERSION=26
-D<PERSON>DROID_PLATFORM=android-26
-<PERSON>ANDROID_ABI=x86
-DCMAKE_ANDROID_ARCH_ABI=x86
-DANDROID_NDK=/Users/<USER>/Library/Android/sdk/ndk/23.1.7779620
-DCMAKE_ANDROID_NDK=/Users/<USER>/Library/Android/sdk/ndk/23.1.7779620
-DCMAKE_TOOLCHAIN_FILE=/Users/<USER>/Library/Android/sdk/ndk/23.1.7779620/build/cmake/android.toolchain.cmake
-DCMAKE_MAKE_PROGRAM=/Users/<USER>/Library/Android/sdk/cmake/3.18.1/bin/ninja
-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=/Users/<USER>/StudioProjects/one_drop/build/app/intermediates/cxx/Debug/3b2817nc/obj/x86
-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=/Users/<USER>/StudioProjects/one_drop/build/app/intermediates/cxx/Debug/3b2817nc/obj/x86
-DCMAKE_BUILD_TYPE=Debug
-B/Users/<USER>/StudioProjects/one_drop/android/app/.cxx/Debug/3b2817nc/x86
-GNinja
-Wno-dev
--no-warn-unused-cli
                    Build command args: []
                    Version: 2