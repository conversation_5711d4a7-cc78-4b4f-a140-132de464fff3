# CMAKE generated file: DO NOT EDIT!
# Generated by "Ninja" Generator, CMake Version 3.18

# This file contains all the build statements describing the
# compilation DAG.

# =============================================================================
# Write statements declared in CMakeLists.txt:
# 
# Which is the root file.
# =============================================================================

# =============================================================================
# Project: Project
# Configurations: Debug
# =============================================================================

#############################################
# Minimal version of Ninja required by this file

ninja_required_version = 1.5


#############################################
# Set configuration variable for custom commands.

CONFIGURATION = Debug
# =============================================================================
# Include auxiliary files.


#############################################
# Include rules file.

include CMakeFiles/rules.ninja


#############################################
# Utility command for rebuild_cache

build CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cd /Users/<USER>/StudioProjects/one_drop/android/app/.cxx/Debug/3b2817nc/x86_64 && /Users/<USER>/Library/Android/sdk/cmake/3.18.1/bin/cmake --regenerate-during-build -S/Users/<USER>/src/flutter/packages/flutter_tools/gradle/src/main/groovy -B/Users/<USER>/StudioProjects/one_drop/android/app/.cxx/Debug/3b2817nc/x86_64
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build rebuild_cache: phony CMakeFiles/rebuild_cache.util


#############################################
# Utility command for edit_cache

build CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cd /Users/<USER>/StudioProjects/one_drop/android/app/.cxx/Debug/3b2817nc/x86_64 && /Users/<USER>/Library/Android/sdk/cmake/3.18.1/bin/cmake -E echo No\ interactive\ CMake\ dialog\ available.
  DESC = No interactive CMake dialog available...
  restat = 1

build edit_cache: phony CMakeFiles/edit_cache.util

# =============================================================================
# Target aliases.

# =============================================================================
# Folder targets.

# =============================================================================

#############################################
# Folder: /Users/<USER>/StudioProjects/one_drop/android/app/.cxx/Debug/3b2817nc/x86_64

build all: phony

# =============================================================================
# Built-in targets


#############################################
# Re-run CMake if any of its inputs changed.

build build.ninja: RERUN_CMAKE | /Users/<USER>/Library/Android/sdk/cmake/3.18.1/share/cmake-3.18/Modules/CMakeCCompiler.cmake.in /Users/<USER>/Library/Android/sdk/cmake/3.18.1/share/cmake-3.18/Modules/CMakeCCompilerABI.c /Users/<USER>/Library/Android/sdk/cmake/3.18.1/share/cmake-3.18/Modules/CMakeCInformation.cmake /Users/<USER>/Library/Android/sdk/cmake/3.18.1/share/cmake-3.18/Modules/CMakeCXXCompiler.cmake.in /Users/<USER>/Library/Android/sdk/cmake/3.18.1/share/cmake-3.18/Modules/CMakeCXXCompilerABI.cpp /Users/<USER>/Library/Android/sdk/cmake/3.18.1/share/cmake-3.18/Modules/CMakeCXXInformation.cmake /Users/<USER>/Library/Android/sdk/cmake/3.18.1/share/cmake-3.18/Modules/CMakeCheckCompilerFlagCommonPatterns.cmake /Users/<USER>/Library/Android/sdk/cmake/3.18.1/share/cmake-3.18/Modules/CMakeCommonLanguageInclude.cmake /Users/<USER>/Library/Android/sdk/cmake/3.18.1/share/cmake-3.18/Modules/CMakeDetermineCCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.18.1/share/cmake-3.18/Modules/CMakeDetermineCXXCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.18.1/share/cmake-3.18/Modules/CMakeDetermineCompileFeatures.cmake /Users/<USER>/Library/Android/sdk/cmake/3.18.1/share/cmake-3.18/Modules/CMakeDetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.18.1/share/cmake-3.18/Modules/CMakeDetermineCompilerABI.cmake /Users/<USER>/Library/Android/sdk/cmake/3.18.1/share/cmake-3.18/Modules/CMakeDetermineSystem.cmake /Users/<USER>/Library/Android/sdk/cmake/3.18.1/share/cmake-3.18/Modules/CMakeFindBinUtils.cmake /Users/<USER>/Library/Android/sdk/cmake/3.18.1/share/cmake-3.18/Modules/CMakeGenericSystem.cmake /Users/<USER>/Library/Android/sdk/cmake/3.18.1/share/cmake-3.18/Modules/CMakeInitializeConfigs.cmake /Users/<USER>/Library/Android/sdk/cmake/3.18.1/share/cmake-3.18/Modules/CMakeLanguageInformation.cmake /Users/<USER>/Library/Android/sdk/cmake/3.18.1/share/cmake-3.18/Modules/CMakeParseImplicitIncludeInfo.cmake /Users/<USER>/Library/Android/sdk/cmake/3.18.1/share/cmake-3.18/Modules/CMakeParseImplicitLinkInfo.cmake /Users/<USER>/Library/Android/sdk/cmake/3.18.1/share/cmake-3.18/Modules/CMakeSystem.cmake.in /Users/<USER>/Library/Android/sdk/cmake/3.18.1/share/cmake-3.18/Modules/CMakeSystemSpecificInformation.cmake /Users/<USER>/Library/Android/sdk/cmake/3.18.1/share/cmake-3.18/Modules/CMakeSystemSpecificInitialize.cmake /Users/<USER>/Library/Android/sdk/cmake/3.18.1/share/cmake-3.18/Modules/CMakeTestCCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.18.1/share/cmake-3.18/Modules/CMakeTestCXXCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.18.1/share/cmake-3.18/Modules/CMakeTestCompilerCommon.cmake /Users/<USER>/Library/Android/sdk/cmake/3.18.1/share/cmake-3.18/Modules/Compiler/CMakeCommonCompilerMacros.cmake /Users/<USER>/Library/Android/sdk/cmake/3.18.1/share/cmake-3.18/Modules/Compiler/Clang-C.cmake /Users/<USER>/Library/Android/sdk/cmake/3.18.1/share/cmake-3.18/Modules/Compiler/Clang-CXX.cmake /Users/<USER>/Library/Android/sdk/cmake/3.18.1/share/cmake-3.18/Modules/Compiler/Clang-FindBinUtils.cmake /Users/<USER>/Library/Android/sdk/cmake/3.18.1/share/cmake-3.18/Modules/Compiler/Clang.cmake /Users/<USER>/Library/Android/sdk/cmake/3.18.1/share/cmake-3.18/Modules/Compiler/GNU.cmake /Users/<USER>/Library/Android/sdk/cmake/3.18.1/share/cmake-3.18/Modules/Internal/CMakeCheckCompilerFlag.cmake /Users/<USER>/Library/Android/sdk/cmake/3.18.1/share/cmake-3.18/Modules/Internal/FeatureTesting.cmake /Users/<USER>/Library/Android/sdk/cmake/3.18.1/share/cmake-3.18/Modules/Platform/Android-Clang-C.cmake /Users/<USER>/Library/Android/sdk/cmake/3.18.1/share/cmake-3.18/Modules/Platform/Android-Clang-CXX.cmake /Users/<USER>/Library/Android/sdk/cmake/3.18.1/share/cmake-3.18/Modules/Platform/Android-Clang.cmake /Users/<USER>/Library/Android/sdk/cmake/3.18.1/share/cmake-3.18/Modules/Platform/Android-Determine-C.cmake /Users/<USER>/Library/Android/sdk/cmake/3.18.1/share/cmake-3.18/Modules/Platform/Android-Determine-CXX.cmake /Users/<USER>/Library/Android/sdk/cmake/3.18.1/share/cmake-3.18/Modules/Platform/Android-Determine.cmake /Users/<USER>/Library/Android/sdk/cmake/3.18.1/share/cmake-3.18/Modules/Platform/Android-Initialize.cmake /Users/<USER>/Library/Android/sdk/cmake/3.18.1/share/cmake-3.18/Modules/Platform/Android.cmake /Users/<USER>/Library/Android/sdk/cmake/3.18.1/share/cmake-3.18/Modules/Platform/Android/Determine-Compiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.18.1/share/cmake-3.18/Modules/Platform/Linux.cmake /Users/<USER>/Library/Android/sdk/cmake/3.18.1/share/cmake-3.18/Modules/Platform/UnixPaths.cmake /Users/<USER>/Library/Android/sdk/ndk/23.1.7779620/build/cmake/android-legacy.toolchain.cmake /Users/<USER>/Library/Android/sdk/ndk/23.1.7779620/build/cmake/android.toolchain.cmake /Users/<USER>/Library/Android/sdk/ndk/23.1.7779620/build/cmake/compiler_id.cmake /Users/<USER>/Library/Android/sdk/ndk/23.1.7779620/build/cmake/platforms.cmake /Users/<USER>/src/flutter/packages/flutter_tools/gradle/src/main/groovy/CMakeLists.txt CMakeCache.txt CMakeFiles/3.18.1-g262b901/CMakeCCompiler.cmake CMakeFiles/3.18.1-g262b901/CMakeCXXCompiler.cmake CMakeFiles/3.18.1-g262b901/CMakeSystem.cmake
  pool = console


#############################################
# A missing CMake input file is not an error.

build /Users/<USER>/Library/Android/sdk/cmake/3.18.1/share/cmake-3.18/Modules/CMakeCCompiler.cmake.in /Users/<USER>/Library/Android/sdk/cmake/3.18.1/share/cmake-3.18/Modules/CMakeCCompilerABI.c /Users/<USER>/Library/Android/sdk/cmake/3.18.1/share/cmake-3.18/Modules/CMakeCInformation.cmake /Users/<USER>/Library/Android/sdk/cmake/3.18.1/share/cmake-3.18/Modules/CMakeCXXCompiler.cmake.in /Users/<USER>/Library/Android/sdk/cmake/3.18.1/share/cmake-3.18/Modules/CMakeCXXCompilerABI.cpp /Users/<USER>/Library/Android/sdk/cmake/3.18.1/share/cmake-3.18/Modules/CMakeCXXInformation.cmake /Users/<USER>/Library/Android/sdk/cmake/3.18.1/share/cmake-3.18/Modules/CMakeCheckCompilerFlagCommonPatterns.cmake /Users/<USER>/Library/Android/sdk/cmake/3.18.1/share/cmake-3.18/Modules/CMakeCommonLanguageInclude.cmake /Users/<USER>/Library/Android/sdk/cmake/3.18.1/share/cmake-3.18/Modules/CMakeDetermineCCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.18.1/share/cmake-3.18/Modules/CMakeDetermineCXXCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.18.1/share/cmake-3.18/Modules/CMakeDetermineCompileFeatures.cmake /Users/<USER>/Library/Android/sdk/cmake/3.18.1/share/cmake-3.18/Modules/CMakeDetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.18.1/share/cmake-3.18/Modules/CMakeDetermineCompilerABI.cmake /Users/<USER>/Library/Android/sdk/cmake/3.18.1/share/cmake-3.18/Modules/CMakeDetermineSystem.cmake /Users/<USER>/Library/Android/sdk/cmake/3.18.1/share/cmake-3.18/Modules/CMakeFindBinUtils.cmake /Users/<USER>/Library/Android/sdk/cmake/3.18.1/share/cmake-3.18/Modules/CMakeGenericSystem.cmake /Users/<USER>/Library/Android/sdk/cmake/3.18.1/share/cmake-3.18/Modules/CMakeInitializeConfigs.cmake /Users/<USER>/Library/Android/sdk/cmake/3.18.1/share/cmake-3.18/Modules/CMakeLanguageInformation.cmake /Users/<USER>/Library/Android/sdk/cmake/3.18.1/share/cmake-3.18/Modules/CMakeParseImplicitIncludeInfo.cmake /Users/<USER>/Library/Android/sdk/cmake/3.18.1/share/cmake-3.18/Modules/CMakeParseImplicitLinkInfo.cmake /Users/<USER>/Library/Android/sdk/cmake/3.18.1/share/cmake-3.18/Modules/CMakeSystem.cmake.in /Users/<USER>/Library/Android/sdk/cmake/3.18.1/share/cmake-3.18/Modules/CMakeSystemSpecificInformation.cmake /Users/<USER>/Library/Android/sdk/cmake/3.18.1/share/cmake-3.18/Modules/CMakeSystemSpecificInitialize.cmake /Users/<USER>/Library/Android/sdk/cmake/3.18.1/share/cmake-3.18/Modules/CMakeTestCCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.18.1/share/cmake-3.18/Modules/CMakeTestCXXCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.18.1/share/cmake-3.18/Modules/CMakeTestCompilerCommon.cmake /Users/<USER>/Library/Android/sdk/cmake/3.18.1/share/cmake-3.18/Modules/Compiler/CMakeCommonCompilerMacros.cmake /Users/<USER>/Library/Android/sdk/cmake/3.18.1/share/cmake-3.18/Modules/Compiler/Clang-C.cmake /Users/<USER>/Library/Android/sdk/cmake/3.18.1/share/cmake-3.18/Modules/Compiler/Clang-CXX.cmake /Users/<USER>/Library/Android/sdk/cmake/3.18.1/share/cmake-3.18/Modules/Compiler/Clang-FindBinUtils.cmake /Users/<USER>/Library/Android/sdk/cmake/3.18.1/share/cmake-3.18/Modules/Compiler/Clang.cmake /Users/<USER>/Library/Android/sdk/cmake/3.18.1/share/cmake-3.18/Modules/Compiler/GNU.cmake /Users/<USER>/Library/Android/sdk/cmake/3.18.1/share/cmake-3.18/Modules/Internal/CMakeCheckCompilerFlag.cmake /Users/<USER>/Library/Android/sdk/cmake/3.18.1/share/cmake-3.18/Modules/Internal/FeatureTesting.cmake /Users/<USER>/Library/Android/sdk/cmake/3.18.1/share/cmake-3.18/Modules/Platform/Android-Clang-C.cmake /Users/<USER>/Library/Android/sdk/cmake/3.18.1/share/cmake-3.18/Modules/Platform/Android-Clang-CXX.cmake /Users/<USER>/Library/Android/sdk/cmake/3.18.1/share/cmake-3.18/Modules/Platform/Android-Clang.cmake /Users/<USER>/Library/Android/sdk/cmake/3.18.1/share/cmake-3.18/Modules/Platform/Android-Determine-C.cmake /Users/<USER>/Library/Android/sdk/cmake/3.18.1/share/cmake-3.18/Modules/Platform/Android-Determine-CXX.cmake /Users/<USER>/Library/Android/sdk/cmake/3.18.1/share/cmake-3.18/Modules/Platform/Android-Determine.cmake /Users/<USER>/Library/Android/sdk/cmake/3.18.1/share/cmake-3.18/Modules/Platform/Android-Initialize.cmake /Users/<USER>/Library/Android/sdk/cmake/3.18.1/share/cmake-3.18/Modules/Platform/Android.cmake /Users/<USER>/Library/Android/sdk/cmake/3.18.1/share/cmake-3.18/Modules/Platform/Android/Determine-Compiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.18.1/share/cmake-3.18/Modules/Platform/Linux.cmake /Users/<USER>/Library/Android/sdk/cmake/3.18.1/share/cmake-3.18/Modules/Platform/UnixPaths.cmake /Users/<USER>/Library/Android/sdk/ndk/23.1.7779620/build/cmake/android-legacy.toolchain.cmake /Users/<USER>/Library/Android/sdk/ndk/23.1.7779620/build/cmake/android.toolchain.cmake /Users/<USER>/Library/Android/sdk/ndk/23.1.7779620/build/cmake/compiler_id.cmake /Users/<USER>/Library/Android/sdk/ndk/23.1.7779620/build/cmake/platforms.cmake /Users/<USER>/src/flutter/packages/flutter_tools/gradle/src/main/groovy/CMakeLists.txt CMakeCache.txt CMakeFiles/3.18.1-g262b901/CMakeCCompiler.cmake CMakeFiles/3.18.1-g262b901/CMakeCXXCompiler.cmake CMakeFiles/3.18.1-g262b901/CMakeSystem.cmake: phony


#############################################
# Clean all the built files.

build clean: CLEAN


#############################################
# Print all primary targets available.

build help: HELP


#############################################
# Make the all target the default.

default all
