import UIKit
import Flutter
import GoogleMaps
import PassKit
import Firebase
import FirebaseMessaging
import UserNotifications

@main
@objc class AppDelegate: FlutterAppDelegate, MessagingDelegate {
    let supportedPaymentNetworks = [PKPaymentNetwork.visa, PKPaymentNetwork.masterCard, PKPaymentNetwork.amex]
    let applePayMerchantID = "merchant.com.ammar.oneDrop"
    var controller: FlutterViewController?
    var flutterChannel: FlutterMethodChannel?
    var countryCode = "AE"
    var currencyCode = "AED"
    var paymentSummaryItems = [PKPaymentSummaryItem]()
    
  override func application(
    _ application: UIApplication,
    didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey: Any]?
  ) -> Bool {
    // Initialize Firebase
    FirebaseApp.configure()
    
    // Set up Firebase Messaging
    Messaging.messaging().delegate = self
    
    // Set up notifications
    if #available(iOS 10.0, *) {
      UNUserNotificationCenter.current().delegate = self
      let authOptions: UNAuthorizationOptions = [.alert, .badge, .sound]
      UNUserNotificationCenter.current().requestAuthorization(
        options: authOptions,
        completionHandler: { _, _ in }
      )
    } else {
      let settings: UIUserNotificationSettings =
        UIUserNotificationSettings(types: [.alert, .badge, .sound], categories: nil)
      application.registerUserNotificationSettings(settings)
    }
    
    application.registerForRemoteNotifications()
    
    GMSServices.provideAPIKey("AIzaSyBypxT1CuIUy4sWyaqXCoa_vQMArvKTJGQ")
      controller = (window?.rootViewController as! FlutterViewController)
    flutterChannel = FlutterMethodChannel(name: "com.ammar.oneDrop/applePay",
                                          binaryMessenger: controller!.binaryMessenger)
    flutterChannel?.setMethodCallHandler({
        [weak self] (call: FlutterMethodCall, result: @escaping FlutterResult) -> Void in
        if call.method == "initApplePay" {
            self?.initApplePay(call: call, result: result)
        }
        else if call.method == "canMakePayments" {
            self?.canMakePayments(result: result);
        }
        else if call.method == "setCountryCode" {
            self?.setCountryCode(call: call, result: result)
        }
        else if call.method == "setCurrencyCode" {
            self?.setCurrencyCode(call: call, result: result)
        }
        else if call.method == "setPaymentSummaryItems" {
            self?.setPaymentSummaryItems(call: call, result: result)
        }
      })
    GeneratedPluginRegistrant.register(with: self)
    return super.application(application, didFinishLaunchingWithOptions: launchOptions)
  }
}

extension AppDelegate: PKPaymentAuthorizationViewControllerDelegate {
    private func setPaymentSummaryItems(call: FlutterMethodCall, result: @escaping FlutterResult) {
        print("--- setPaymentSummaryItems \(call.arguments ?? "")")
        guard let jsonString = call.arguments as? String else {
            result(false)
            return
        }
        let itemsData = Data(jsonString.utf8)
        let decoder = JSONDecoder()
        guard let paymentItems = try? decoder.decode([PayemntSummaryItemDecodable].self, from: itemsData) else {
            print("--- decoder error")
            result(false)
            return
        }
        paymentSummaryItems = paymentItems.map({ item in
            return PKPaymentSummaryItem(label: item.label, amount: NSDecimalNumber(value: item.amount))
        })
        result(true)
    }
    
    private func setCountryCode(call: FlutterMethodCall, result: @escaping FlutterResult) {
        print("--- setCountryCode \(call.arguments ?? "")")
        countryCode = (call.arguments as? String) ?? countryCode
        result(nil)
    }
    
    private func setCurrencyCode(call: FlutterMethodCall, result: @escaping FlutterResult) {
        print("--- setCurrencyCode \(call.arguments ?? "")")
        currencyCode = (call.arguments as? String) ?? currencyCode
        result(nil)
    }
    
    private func canMakePayments(result: @escaping FlutterResult) {
        result(PKPaymentAuthorizationViewController.canMakePayments(usingNetworks: self.supportedPaymentNetworks))
    }
    
    private func decodeFlutterAPPaymentRequest(call: FlutterMethodCall) -> APPaymentRequestDecodable? {
        guard let jsonString = call.arguments as? String else {
            return nil
        }
        let requestData = Data(jsonString.utf8)
        let decoder = JSONDecoder()
        guard let request = try? decoder.decode(APPaymentRequestDecodable.self, from: requestData) else {
            print("--- decodeFlutterAPPaymentRequest error")
            return nil
        }
        return request
    }
    
    private func initApplePay(call: FlutterMethodCall, result: FlutterResult) {
        print("--- initApplePay")
        guard let flutterPaymentRequest = decodeFlutterAPPaymentRequest(call: call) else {
            result(false);
            return
        }
        let paymentRequest = PKPaymentRequest()
        paymentRequest.merchantIdentifier = flutterPaymentRequest.merchantID
        paymentRequest.supportedNetworks = supportedPaymentNetworks
        paymentRequest.merchantCapabilities = PKMerchantCapability.capability3DS
        paymentRequest.countryCode = flutterPaymentRequest.countryCode
        paymentRequest.currencyCode = flutterPaymentRequest.currencyCode
//        if #available(iOS 11.0, *) {
//            paymentRequest.requiredShippingContactFields = [PKContactField.emailAddress]
//        } else {
//        }
//        paymentRequest.requiredShippingAddressFields = PKAddressField.all
        paymentRequest.paymentSummaryItems = flutterPaymentRequest.paymentSummaryItems.map({ item in
            return PKPaymentSummaryItem(label: item.label, amount: NSDecimalNumber(value: item.amount))
        })
//        paymentRequest.paymentSummaryItems = [
//            PKPaymentSummaryItem(label: "Car Wash", amount: 20),
//            PKPaymentSummaryItem(label: "OneDrop", amount: 20)
//        ]
        guard let applePayController = PKPaymentAuthorizationViewController(paymentRequest: paymentRequest) else {
            print("--- could not initialize PKPaymentAuthorizationViewController")
            result(false);
            return
        }
        applePayController.delegate = self
        UIApplication.shared.windows.first?.rootViewController?.present(applePayController, animated: true, completion: nil)
        result(true);
    }
    
    func paymentAuthorizationViewController(_ controller: PKPaymentAuthorizationViewController, didAuthorizePayment payment: PKPayment, completion: @escaping ((PKPaymentAuthorizationStatus) -> Void)) {
        print("--- PKPaymentAuthorizationStatus.success")
        let paymentDict: [String: String] = ["token": String(decoding: payment.token.paymentData, as: UTF8.self), "transactionID":  payment.token.transactionIdentifier]
        print("--- token \(String(decoding: payment.token.paymentData, as: UTF8.self))")
        flutterChannel?.invokeMethod("userDidAuthorizePayment", arguments: paymentDict, result: { value in
//            print("--- flutter return \(value ?? "")")
            completion(PKPaymentAuthorizationStatus.success)
        })
    }
    
//    @available(iOS 11.0, *)
//    func paymentAuthorizationViewController(_ controller: PKPaymentAuthorizationViewController, didSelect paymentMethod: PKPaymentMethod, handler completion: @escaping (PKPaymentRequestPaymentMethodUpdate) -> Void) {
//        print("--- didSelect paymentMethod")
//    }
    
//    func paymentAuthorizationViewController(_ controller: PKPaymentAuthorizationViewController, didSelect shippingMethod: PKShippingMethod, handler completion: @escaping (PKPaymentRequestShippingMethodUpdate) -> Void) {
//        completion()
//    }
    
    func paymentAuthorizationViewControllerDidFinish(_ controller: PKPaymentAuthorizationViewController) {
        print("--- did finish payment")
        controller.dismiss(animated: true, completion: nil)
    }
}

extension AppDelegate {
    func messaging(_ messaging: Messaging, didReceiveRegistrationToken fcmToken: String?) {
        print("Firebase registration token: \(String(describing: fcmToken))")
        let dataDict: [String: String] = ["token": fcmToken ?? ""]
        NotificationCenter.default.post(name: Notification.Name("FCMToken"), object: nil, userInfo: dataDict)
    }
    
    override func userNotificationCenter(_ center: UNUserNotificationCenter, willPresent notification: UNNotification, withCompletionHandler completionHandler: @escaping (UNNotificationPresentationOptions) -> Void) {
        completionHandler([[.alert, .sound]])
    }
    
    override func userNotificationCenter(_ center: UNUserNotificationCenter, didReceive response: UNNotificationResponse, withCompletionHandler completionHandler: @escaping () -> Void) {
        completionHandler()
    }
}
