{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e986702f2b39137c77a90ca666830d56b78", "buildSettings": {"ASSETCATALOG_COMPILER_APPICON_NAME": "AppIcon", "ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME": "AccentColor", "BUILD_LIBRARY_FOR_DISTRIBUTION": "YES", "CLANG_ENABLE_OBJC_WEAK": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks", "ONLY_ACTIVE_ARCH": "NO", "SDKROOT": "iphoneos", "TARGETED_DEVICE_FAMILY": "1,2"}, "guid": "bfdfe7dc352907fc980b868725387e98839026bd23279f9e01dc6e98b05a0a64", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98300d13c5096e8844bd74b07ceb74b688", "buildSettings": {"ASSETCATALOG_COMPILER_APPICON_NAME": "AppIcon", "ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME": "AccentColor", "BUILD_LIBRARY_FOR_DISTRIBUTION": "YES", "CLANG_ENABLE_OBJC_WEAK": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks", "SDKROOT": "iphoneos", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES"}, "guid": "bfdfe7dc352907fc980b868725387e98e9f39394222a10bfd61f61768c95bd86", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98300d13c5096e8844bd74b07ceb74b688", "buildSettings": {"ASSETCATALOG_COMPILER_APPICON_NAME": "AppIcon", "ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME": "AccentColor", "BUILD_LIBRARY_FOR_DISTRIBUTION": "YES", "CLANG_ENABLE_OBJC_WEAK": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks", "SDKROOT": "iphoneos", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES"}, "guid": "bfdfe7dc352907fc980b868725387e9841bc4ffa15de0f7dd2dfbb2db52778bc", "name": "Release"}], "buildPhases": [{"alwaysOutOfDate": "false", "alwaysRunForInstallHdrs": "false", "buildFiles": [], "emitEnvironment": "false", "guid": "bfdfe7dc352907fc980b868725387e98d37f8cc66ccc1c1c85a3ca6feb95609f", "inputFileListPaths": ["${PODS_ROOT}/Target Support Files/GoogleMaps/GoogleMaps-xcframeworks-input-files.xcfilelist"], "inputFilePaths": [], "name": "[CP] Copy XCFrameworks", "originalObjectID": "ADB971F1E19125E3A80F390B9806C798", "outputFileListPaths": ["${PODS_ROOT}/Target Support Files/GoogleMaps/GoogleMaps-xcframeworks-output-files.xcfilelist"], "outputFilePaths": [], "sandboxingOverride": "basedOnBuildSetting", "scriptContents": "\"${PODS_ROOT}/Target Support Files/GoogleMaps/GoogleMaps-xcframeworks.sh\"\n", "shellPath": "/bin/sh", "type": "com.apple.buildphase.shell-script"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e9877354dc0c1379e634078de2da2deba6b", "name": "GoogleMaps-GoogleMapsResources"}], "guid": "bfdfe7dc352907fc980b868725387e9818352c54edac2258b91768852065ce5e", "name": "GoogleMaps", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "", "configurationName": "Debug", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "", "configurationName": "Profile", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "", "configurationName": "Release", "provisioningStyle": 0}], "type": "aggregate"}