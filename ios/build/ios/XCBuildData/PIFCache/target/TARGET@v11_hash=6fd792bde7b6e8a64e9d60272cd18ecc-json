{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9846271a62ecc19ce147f1a00761c07079", "buildSettings": {"BUILD_LIBRARY_FOR_DISTRIBUTION": "YES", "CLANG_ALLOW_NON_MODULAR_INCLUDES_IN_FRAMEWORK_MODULES": "YES", "CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCore/FirebaseCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCore/FirebaseCore.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseCore", "PRODUCT_NAME": "FirebaseCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98d8b4ae31ee71c1cfcdf2a0fbcd507a0e", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98a2867e5f343756f7ce7e4130def4533e", "buildSettings": {"BUILD_LIBRARY_FOR_DISTRIBUTION": "YES", "CLANG_ALLOW_NON_MODULAR_INCLUDES_IN_FRAMEWORK_MODULES": "YES", "CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCore/FirebaseCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCore/FirebaseCore.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCore", "PRODUCT_NAME": "FirebaseCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98e1bf6f4f01e3ae8230ee4c09506aa931", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98a2867e5f343756f7ce7e4130def4533e", "buildSettings": {"BUILD_LIBRARY_FOR_DISTRIBUTION": "YES", "CLANG_ALLOW_NON_MODULAR_INCLUDES_IN_FRAMEWORK_MODULES": "YES", "CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCore/FirebaseCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCore/FirebaseCore.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCore", "PRODUCT_NAME": "FirebaseCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e982ea1a75e363094655e74754d157b49e0", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e989b91becf09577dd04200f5435e747c46", "guid": "bfdfe7dc352907fc980b868725387e989843b13476a51dd652487bc4c2a136b5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986252c098539d7e5e77c19b2e0a12ed47", "guid": "bfdfe7dc352907fc980b868725387e98745f23d725a83a4b1ceffb7f94501776", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988445a794a26cf2c114ae35d60a07b5d3", "guid": "bfdfe7dc352907fc980b868725387e98c14a0c87173225b1a0651e217c5b9534"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9874b1acef33368a67fe07785db44ab42e", "guid": "bfdfe7dc352907fc980b868725387e987f9c315326c0465cad71b7ee01d76697"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987b0520555ae4001178404469cfc738dd", "guid": "bfdfe7dc352907fc980b868725387e98220cfd47b00e6f3ad67e99616502ab21"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bb6b90414eb2bf3c7982193080a8ab2b", "guid": "bfdfe7dc352907fc980b868725387e98c5e23ba132087b744ea06d06859c4d65"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9863f7f29659a1a4ca85c6fb119475ba18", "guid": "bfdfe7dc352907fc980b868725387e98abf290c0495f9e63ff15d8f2aa30849b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a7e12bd6251dbe6c534843576883dee0", "guid": "bfdfe7dc352907fc980b868725387e9860c7dad2c78e7266212cc7880ec67e9e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b407c29f4aa9f98d6014f8436eac9ed1", "guid": "bfdfe7dc352907fc980b868725387e980d6c85d60889b7560abe0deba5ac5a78", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982c3961ed327b10ddb11882737949102f", "guid": "bfdfe7dc352907fc980b868725387e9866137b87c677206a01f986ddc5fd5091"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988d582be08571988a5c942b661f85528a", "guid": "bfdfe7dc352907fc980b868725387e983613539a060b4278759db7a634983ef0", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988f24802e8d02073c8492e228bd2320f3", "guid": "bfdfe7dc352907fc980b868725387e98162b73002b03a7e4f91d4cd2d4639c00", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9822303ae40c86ddcfc4fe718c8cd351df", "guid": "bfdfe7dc352907fc980b868725387e984afc4e5fbaa71aa82007c83f340f05b8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9822ab4484cc609bc7f8428b3c680e5e8d", "guid": "bfdfe7dc352907fc980b868725387e98412481ae17e32374d2e3f1d048caf5b2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98868725becf937004e723934a507a20b2", "guid": "bfdfe7dc352907fc980b868725387e9872235a058e845751f583eeb80d9875e5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9822899a1ff8d7d3be5397873500f4b394", "guid": "bfdfe7dc352907fc980b868725387e98b4d9ca37b1f874f3bfbe1f3cdefcfaf8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981c55590d44ad473ca7899f42b8327d58", "guid": "bfdfe7dc352907fc980b868725387e989436709455709328b1048c32d61114bf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986c69f5f1963af112cb90b881b7df0af4", "guid": "bfdfe7dc352907fc980b868725387e98821e331d28f1c4b6fed6ab434fed9ff4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98967f8b6cf4ed193b46ccf855cf1dc190", "guid": "bfdfe7dc352907fc980b868725387e9804de1a18316547d94bfd14f7a5759979", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9807e623c10ea3fc48d8e0475cedd576fa", "guid": "bfdfe7dc352907fc980b868725387e98995ddf370532d52cc2af5f8c7f6a077d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d5581e43bc1b6637344a7fb9d314043c", "guid": "bfdfe7dc352907fc980b868725387e9823e19fac200298f957b10412cff455ff", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980157706b7eeef366dbb351c7dfffba31", "guid": "bfdfe7dc352907fc980b868725387e9890a5a6a0ee7a0cbef662aa88a0c65fb2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981746e45f7a431055c2011f54c140a0be", "guid": "bfdfe7dc352907fc980b868725387e98c331520dfaecfc2e7d78da397d0ca311", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98ec5ecb668b341705d61d75968d015c28", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98afa7eb4f90794e59a84ad1b6549d0846", "guid": "bfdfe7dc352907fc980b868725387e985035f7fd19ee82f56b3b2df40ba6a25f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982e59ad0e21e93ba2a98c48f1f8be6ebc", "guid": "bfdfe7dc352907fc980b868725387e98677071914c1084fa86ad8de1321f669b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981224783868266c3394c51901f59003f8", "guid": "bfdfe7dc352907fc980b868725387e9862bacaa4586ae90ffd50d639e9272255"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9842676e881ceaeb1288f043eabaf427ee", "guid": "bfdfe7dc352907fc980b868725387e98936ed67037a3fe8dafb1ef5986379f7f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c4a0ac08e8cf3fb9f7d3e23da5b2e780", "guid": "bfdfe7dc352907fc980b868725387e986ad5274d082c9d9a0bc69fba048744c7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9850865624935a9505552105d918e735a7", "guid": "bfdfe7dc352907fc980b868725387e986a098da0b1cdce9e437d90accbe89079"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989b485443338b5722eecf5f4ac6ee3e74", "guid": "bfdfe7dc352907fc980b868725387e98a7ec4389ef21e02d54573c633e712974"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98beee7075c08fead9ac5ede81de3b4930", "guid": "bfdfe7dc352907fc980b868725387e980183ce6599b638145488f7e4f2f03c12"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9883df1631de9b54e643584a15ca4136d5", "guid": "bfdfe7dc352907fc980b868725387e9833f0b4a5c7a3f27f959cac34a42cc263"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987b1318bf43ff7afca0e85203fd0eb6cd", "guid": "bfdfe7dc352907fc980b868725387e98c9e6b2f9723490f8d00704a1ebed05aa"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bd48efeb1be560d88e295ca3985e2dd2", "guid": "bfdfe7dc352907fc980b868725387e98cc1c290f739422c27da6fc812de8a51a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982f41f78f277bfe022daf0ec786090149", "guid": "bfdfe7dc352907fc980b868725387e982b684097b0f0a45aea5ddcaad0d5baef"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9847487fcfa7d706a2d739b98fb1969757", "guid": "bfdfe7dc352907fc980b868725387e9889671f26394114a837e55ad6233ef3b3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980c6d523538d748d7adbff70b1533860f", "guid": "bfdfe7dc352907fc980b868725387e981186e1414943183c7387dbde5a890df6"}], "guid": "bfdfe7dc352907fc980b868725387e9865e91e0f25bf353d7854bc924c45f29d", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9834dfa23abd1ee3cbdf63f2e7e7c8692e", "guid": "bfdfe7dc352907fc980b868725387e98a4b8400875e276e832503a82cb16afcd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d5192e7597d49b2508d02a0ca39a2058", "guid": "bfdfe7dc352907fc980b868725387e98adb2eda4560b825ab24c037060987f65"}], "guid": "bfdfe7dc352907fc980b868725387e980a5efd2793b4d6e8836a338cf3263893", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98cbe694358581f6b84a0903cb22503297", "targetReference": "bfdfe7dc352907fc980b868725387e98678fb6500ea02c78520816441717cc14"}], "guid": "bfdfe7dc352907fc980b868725387e9899bfc2e998001af9ef22473bbc977a97", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98678fb6500ea02c78520816441717cc14", "name": "FirebaseCore-FirebaseCore_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98020791fd2e7b7ddc8fb2658339c42e16", "name": "FirebaseCoreInternal"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}], "guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e988ae261e418baab0fdd0a48d117fe7fa2", "name": "FirebaseCore.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}