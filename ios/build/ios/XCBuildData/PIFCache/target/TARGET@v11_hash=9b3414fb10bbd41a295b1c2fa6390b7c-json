{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9889e86020e3014cd9d0f3a0d225665d4c", "buildSettings": {"BUILD_LIBRARY_FOR_DISTRIBUTION": "YES", "CLANG_ALLOW_NON_MODULAR_INCLUDES_IN_FRAMEWORK_MODULES": "YES", "CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "GoogleUtilities", "PRODUCT_NAME": "GoogleUtilities", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98383fe47fd9d83ef4ae502933a5a1dc59", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e988fb94ac1e5913e421badd2d6942a415a", "buildSettings": {"BUILD_LIBRARY_FOR_DISTRIBUTION": "YES", "CLANG_ALLOW_NON_MODULAR_INCLUDES_IN_FRAMEWORK_MODULES": "YES", "CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities.modulemap", "PRODUCT_MODULE_NAME": "GoogleUtilities", "PRODUCT_NAME": "GoogleUtilities", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9814cc77f0a023449afed4ed20d88c0da9", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e988fb94ac1e5913e421badd2d6942a415a", "buildSettings": {"BUILD_LIBRARY_FOR_DISTRIBUTION": "YES", "CLANG_ALLOW_NON_MODULAR_INCLUDES_IN_FRAMEWORK_MODULES": "YES", "CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities.modulemap", "PRODUCT_MODULE_NAME": "GoogleUtilities", "PRODUCT_NAME": "GoogleUtilities", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e984a827ee75a14dc5433e330ba589f2158", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98592d4468d6c43b40155f974eb976b0e0", "guid": "bfdfe7dc352907fc980b868725387e98ac0ba1636a4ffd79c6373cda0d96624a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f2188dd087ed9f7f1048b65ef2a304e5", "guid": "bfdfe7dc352907fc980b868725387e98d5f346da6d8d9741848b18adb55079dd", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989394013579f142860403d42503d59911", "guid": "bfdfe7dc352907fc980b868725387e98613703efbe289baa71dc4d03e3f9e507"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98db95bc605603547b3ea0fa4896d81c9c", "guid": "bfdfe7dc352907fc980b868725387e9882203249689324e4742ac06a2130626a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b7cb6356d4f7dff8fb3a10d1c1f5c41d", "guid": "bfdfe7dc352907fc980b868725387e98ffd671ec71e1a3bc1aa69c1897da40aa", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ccc2d68d62ade70c50564f1153f0136b", "guid": "bfdfe7dc352907fc980b868725387e98376a63c1c0bfb1c3172a452432d3ab85", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987c632ef5bca77d561b39c91708d9719e", "guid": "bfdfe7dc352907fc980b868725387e981279b7f5f82bf25fa95dc4e3c6fd75d8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983b7f9e3a08093a6e34e28984d95a43bb", "guid": "bfdfe7dc352907fc980b868725387e98fb6f6b9e1ee53e856daf6165bdb38478", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98058bb0d952f969d93ac3c059617bb9af", "guid": "bfdfe7dc352907fc980b868725387e981bdd2caf9b75d791b2916b294bb0dc77"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ce4b7aca0fc54841bf9461297ea3282b", "guid": "bfdfe7dc352907fc980b868725387e987039b8894cad462934045e5859c4d045", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984c091eaeec8d0b358d42232cda0a5c14", "guid": "bfdfe7dc352907fc980b868725387e987782077fc535862331c59aac207509fc", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982c8f865547ccc6aba949c14e7549f156", "guid": "bfdfe7dc352907fc980b868725387e986bec3177550eb0cc59a553dc78787713", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9864eb70009d5685a6334fdfca6dc73eea", "guid": "bfdfe7dc352907fc980b868725387e985981aae7795a4155dd1a3cc582b5c0fc", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ce9297eb2094f92607c29e2e86b2843f", "guid": "bfdfe7dc352907fc980b868725387e988630ece585ae3db117c3890f62aef552", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d9886594f21cb3e43de59f4220268d02", "guid": "bfdfe7dc352907fc980b868725387e98fe85c8c857d7d14912426d9ef09f4f97"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b63c9cb47664fc51f1d411bf9c46de4f", "guid": "bfdfe7dc352907fc980b868725387e98f03bb119a45f742d95d65789c5afdc6a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c52b205458e96b1c94756afc1c037c7b", "guid": "bfdfe7dc352907fc980b868725387e98fe2955f2cf9d6f4b4c168112143d857c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983aa79d15ed815dfe19fb4353228232dc", "guid": "bfdfe7dc352907fc980b868725387e9835b2689ade21f20ea881dff8e35fe0ab", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984b0731cb99eea4c7dc60121646950c47", "guid": "bfdfe7dc352907fc980b868725387e98bb81c361fe072a65dcd8e3f5a72e4595", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988f103852f9e5a5a834282fad09cde7ff", "guid": "bfdfe7dc352907fc980b868725387e98926f7a8a51c2e6acc5ea609c4f024748", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ee24f7bfbbe0cc65b143ba898991f3d8", "guid": "bfdfe7dc352907fc980b868725387e982d70c523aaa8a74e089e24baf6358bf2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989cc6730d4f838de49995e4ab74d1ffe2", "guid": "bfdfe7dc352907fc980b868725387e984b05a31ef5511834d22c6c53139105cf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98844e9338a1bc10f79eefcb3fcf9dad13", "guid": "bfdfe7dc352907fc980b868725387e98114967022fe2662ca873ae8e4e471332"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984f82bcc36f15673f708670ab62ad4428", "guid": "bfdfe7dc352907fc980b868725387e980b511b275bc60d3ed3e144c34ad43e29", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98414b7ce5dbe69aae12a454d5b787cf0f", "guid": "bfdfe7dc352907fc980b868725387e9864f576d7c61ba580cff96f362782c6fc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98874bb32a6416cb4cbaaac8aa4066a6f0", "guid": "bfdfe7dc352907fc980b868725387e9857bded34721c9a5ea7072ed4640a826e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9878573c221afe69997d772e3d7970478d", "guid": "bfdfe7dc352907fc980b868725387e983ecb02a5310f4b2f5d2e930ca2eb52bf", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9805363aa83193db251fbc9dbb87f19290", "guid": "bfdfe7dc352907fc980b868725387e98f69932ebd1d70875bcf17fe85fa191a0"}], "guid": "bfdfe7dc352907fc980b868725387e985346dff13ae42f6f8177ee211fb52fcb", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98a18a87885c429bdaeff10b316d87336f", "guid": "bfdfe7dc352907fc980b868725387e986a100563e53e80ef11df71025fe57195"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d703fc3bd8b7dd0c61b5a9da3641d825", "guid": "bfdfe7dc352907fc980b868725387e987604536c49abedf8f62365671fd9a690"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9852d85fc43f9e11f2fec8782c8e14c000", "guid": "bfdfe7dc352907fc980b868725387e98aa40b529a9c779ca6d38d10f45515e91"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ffe5cea9477388ecd1204135927194aa", "guid": "bfdfe7dc352907fc980b868725387e988f3f5ffc65611ed90aa38312a347f895"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c9c5600ce1edd4612745b7eafd64fd73", "guid": "bfdfe7dc352907fc980b868725387e98722cee7c9d8688495868209b58df7668"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f9cc092e4b69f40db4a18f153a46582a", "guid": "bfdfe7dc352907fc980b868725387e984a5cead79c8fa1368620aefbd8421dfb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b52e620a847b57414d543ba194a3d23e", "guid": "bfdfe7dc352907fc980b868725387e980cce9475011a735c97582c8ce88f426c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d4acf4299f8a208ad968d84187342501", "guid": "bfdfe7dc352907fc980b868725387e98296e43753576ca394b7692c27d5ce9d8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d93fe705082d18e4457d773bdd1321b0", "guid": "bfdfe7dc352907fc980b868725387e98da6e25a5b3c719bf998e3b0cf15a9ee8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985f75c9f55688c148d7d373b5b28d6aa6", "guid": "bfdfe7dc352907fc980b868725387e985ebaef75dcd5645c87c893fba9fa3342"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989f194ae0157204b6cb56ea1881116654", "guid": "bfdfe7dc352907fc980b868725387e9818f1f8538a75eddd811b7e871c8f2457"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bfd1f2acbf3ba3c440dcc7484b759098", "guid": "bfdfe7dc352907fc980b868725387e9871fe6db925ede178215e01ea82169984"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9835513c58bbf154c24d5845c393f630a7", "guid": "bfdfe7dc352907fc980b868725387e98a46ff2b09a68fc0f96e4e20d42a75490"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98549e167bc6cf9ad58dfb93f7e909e783", "guid": "bfdfe7dc352907fc980b868725387e989b10c4f75604b9779473770e2b1a036e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d948db0b29e56749ce8ed3d8999a26b6", "guid": "bfdfe7dc352907fc980b868725387e9804d82dfd9633645f1bb4410c414b4794"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981e89dff999fde9178cfc8e5a01f95fa2", "guid": "bfdfe7dc352907fc980b868725387e98c73ace4e1094d2883a4ac43db607d0bf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e931bc52d139f2195c7a077fd0e1ba46", "guid": "bfdfe7dc352907fc980b868725387e98ffb29e61173ba61c70038c57971d0352"}], "guid": "bfdfe7dc352907fc980b868725387e980e7976702d938a305ddcc7f33df62754", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9834dfa23abd1ee3cbdf63f2e7e7c8692e", "guid": "bfdfe7dc352907fc980b868725387e98dcd371d1a199b0b0bc50b971a0cc4186"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a22b70908b57b2cde3e263c1645f2bb4", "guid": "bfdfe7dc352907fc980b868725387e989bd68eabf13639ffbb82be6a48302166"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fc5258faf66465db915e1f497e2fd189", "guid": "bfdfe7dc352907fc980b868725387e9848426ccd75f3f68468f88e2b928739a6"}], "guid": "bfdfe7dc352907fc980b868725387e98fb3a0b817823f89671fa7e49713661d3", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e981f921a9efb66958ce9a26cf4f78b6f46", "targetReference": "bfdfe7dc352907fc980b868725387e981a9fac6eb9c80f8eed49fda0531af6a4"}], "guid": "bfdfe7dc352907fc980b868725387e98fe578bb0618b6a91c1d1ff72fe1a2356", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e981a9fac6eb9c80f8eed49fda0531af6a4", "name": "GoogleUtilities-GoogleUtilities_Privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98ca49ca851f2777b997a3e74ccb860358", "name": "GoogleUtilities.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}