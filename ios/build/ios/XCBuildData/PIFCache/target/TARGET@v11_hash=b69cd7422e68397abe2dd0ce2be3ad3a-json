{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e989944ee980e3f7bab4fcd541f6b2759bd", "buildSettings": {"BUILD_LIBRARY_FOR_DISTRIBUTION": "YES", "CLANG_ALLOW_NON_MODULAR_INCLUDES_IN_FRAMEWORK_MODULES": "YES", "CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseCoreInternal", "PRODUCT_NAME": "FirebaseCoreInternal", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98edb83222c99ab572277ca1c2b6d71e84", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98c049726971c5d6e6b78f5543a8e1ecd6", "buildSettings": {"BUILD_LIBRARY_FOR_DISTRIBUTION": "YES", "CLANG_ALLOW_NON_MODULAR_INCLUDES_IN_FRAMEWORK_MODULES": "YES", "CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCoreInternal", "PRODUCT_NAME": "FirebaseCoreInternal", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e989b6ee60f848d447ad0fd8c01120a1f73", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98c049726971c5d6e6b78f5543a8e1ecd6", "buildSettings": {"BUILD_LIBRARY_FOR_DISTRIBUTION": "YES", "CLANG_ALLOW_NON_MODULAR_INCLUDES_IN_FRAMEWORK_MODULES": "YES", "CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCoreInternal", "PRODUCT_NAME": "FirebaseCoreInternal", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98fa6bdd64a2d85e58427cc3823dedd893", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98392cb7b01388bbf7c3de21a5b4437fee", "guid": "bfdfe7dc352907fc980b868725387e986daa719f827590708a3af97cf8bc947a", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e9874de1fe108f0c2bf6f79ca5c56952c73", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98dccac4fe34edb41f79e26df7e88d0b6e", "guid": "bfdfe7dc352907fc980b868725387e98bb573f30446305945a67dbba620d8958"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cc9e2aa45c589ee085e113505edf4d73", "guid": "bfdfe7dc352907fc980b868725387e98d9b309fb83faad10053afbbe66c5972e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984e38586f470e3a916e0d2882dac2b944", "guid": "bfdfe7dc352907fc980b868725387e988e0be5a2ab0ba4338f58d2b6a7322004"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983471f706a08384ac26330e3543bf7f11", "guid": "bfdfe7dc352907fc980b868725387e9803aafdbba0be9950ba974138c62a8ec3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9809e21c02e4f5cc659c0c13ff69d2201a", "guid": "bfdfe7dc352907fc980b868725387e980677613d3bb28a67b7c7e067177e02f2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985c858ae133a70700a7a57ea3790288fa", "guid": "bfdfe7dc352907fc980b868725387e985009d6a815a74fdbb77f3007676583b7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a3e2d2d74ea966b2123d16cd5345cab8", "guid": "bfdfe7dc352907fc980b868725387e98fe7a8db4e2c318c14e03dc53e1946d46"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98828eed60de327e701b1b310db78afaa1", "guid": "bfdfe7dc352907fc980b868725387e984c15657ebc24a1f41e0bc4343fc1debb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980ea1bacc11db5f78a5842dec1960a881", "guid": "bfdfe7dc352907fc980b868725387e98541a893941995ab7d065f6bf3aeaa7cc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986a3e76091135da7f6277e5da1ff7506c", "guid": "bfdfe7dc352907fc980b868725387e9849efe73445518ec6b7941f7553389b86"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987ad091d524ad4056351aa40d4d339e33", "guid": "bfdfe7dc352907fc980b868725387e982e3f5ca6df78da53da501a2f76af60e4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c552707e1eb04aeba506ac535c8fc325", "guid": "bfdfe7dc352907fc980b868725387e98641f240c186122547fa9b864e32d150e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d0afd8dd066af6a447352aadd463bb62", "guid": "bfdfe7dc352907fc980b868725387e985abebb4ce53a4c38430844b96a345184"}], "guid": "bfdfe7dc352907fc980b868725387e98557ed9b56d401aa2302c80a3f946f4b3", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9834dfa23abd1ee3cbdf63f2e7e7c8692e", "guid": "bfdfe7dc352907fc980b868725387e988661e30809cbd5c650a2cf0fcec5b7e9"}], "guid": "bfdfe7dc352907fc980b868725387e983737098366c12ed0a6fb8bf31cbe2e0c", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e9859358d3e43aa72afe81fd80667ade284", "targetReference": "bfdfe7dc352907fc980b868725387e98e5b592b076e092ab7ac9d9b5c85edc6f"}], "guid": "bfdfe7dc352907fc980b868725387e984419aa4ac3a574bdaacdf7b40ba5ef50", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98e5b592b076e092ab7ac9d9b5c85edc6f", "name": "FirebaseCoreInternal-FirebaseCoreInternal_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}], "guid": "bfdfe7dc352907fc980b868725387e98020791fd2e7b7ddc8fb2658339c42e16", "name": "FirebaseCoreInternal", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e983d86e87924acfad2934921ce7ad9fbea", "name": "FirebaseCoreInternal.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}