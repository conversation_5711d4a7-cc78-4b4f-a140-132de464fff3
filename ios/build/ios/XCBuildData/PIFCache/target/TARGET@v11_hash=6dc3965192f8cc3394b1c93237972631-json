{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98c9423489d17f877c7270fa2360e3fabf", "buildSettings": {"BUILD_LIBRARY_FOR_DISTRIBUTION": "YES", "CLANG_ALLOW_NON_MODULAR_INCLUDES_IN_FRAMEWORK_MODULES": "YES", "CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseInstallations", "PRODUCT_NAME": "FirebaseInstallations", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98a029da8171cc127a353d08d2f22db7b4", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e985c60b7e356f7ab3f0db372e9d0a0cba6", "buildSettings": {"BUILD_LIBRARY_FOR_DISTRIBUTION": "YES", "CLANG_ALLOW_NON_MODULAR_INCLUDES_IN_FRAMEWORK_MODULES": "YES", "CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations.modulemap", "PRODUCT_MODULE_NAME": "FirebaseInstallations", "PRODUCT_NAME": "FirebaseInstallations", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98d9f8b7213510a18e552978b5ca8e7fe2", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e985c60b7e356f7ab3f0db372e9d0a0cba6", "buildSettings": {"BUILD_LIBRARY_FOR_DISTRIBUTION": "YES", "CLANG_ALLOW_NON_MODULAR_INCLUDES_IN_FRAMEWORK_MODULES": "YES", "CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations.modulemap", "PRODUCT_MODULE_NAME": "FirebaseInstallations", "PRODUCT_NAME": "FirebaseInstallations", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e981bf88c580d75e91c9d2d3f3577f667cf", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e985ef9d65df30c3c37766bf5b8352980bc", "guid": "bfdfe7dc352907fc980b868725387e98588cc43ed81348f1adc80eea4493d8c5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989c09f11c319938d63770c44d5c082657", "guid": "bfdfe7dc352907fc980b868725387e98720f281046e1951ab4d0991ae5ac9522"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98df34969460918d1fbc99a853def2465c", "guid": "bfdfe7dc352907fc980b868725387e9842723da42e036dd145996c6f908dedd4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98704e1837975e533fcb5d5f9db396fb2f", "guid": "bfdfe7dc352907fc980b868725387e98065d0f3eca2909255c630b45283a9aa8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985a807133ab87231e53759f8fef6e4f5e", "guid": "bfdfe7dc352907fc980b868725387e9885fe442524c46662bad8225362010c84"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98260cb1d8db544a1bd7221a32ec92c19e", "guid": "bfdfe7dc352907fc980b868725387e98458fe351d22b6075493bd33d40847f64"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e99129fb5897f52c76dd50c43daa196d", "guid": "bfdfe7dc352907fc980b868725387e98319f03204c5c8e971b29ea44f9c2d8fb", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981ef3f231180691cc88c7fd684ac6b07b", "guid": "bfdfe7dc352907fc980b868725387e9846b2f08cf9dfb51e854d577f5eab6cbe", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ad9c3690896d4b2b41adbd5e4d6f206f", "guid": "bfdfe7dc352907fc980b868725387e9826cc89d0d1c1a77505be0490b0c513db"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e1cb4e674ae62a8d7fbe505c657af308", "guid": "bfdfe7dc352907fc980b868725387e98defd49161ce72c2f362339f773fab9c0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980e728d1f07b81e72790b90b553fb71bd", "guid": "bfdfe7dc352907fc980b868725387e98c6d45884e3de67fa4b67c3620a782e00", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9871b94848c59dc41b900b2d0cb5061a66", "guid": "bfdfe7dc352907fc980b868725387e98b7c35fe6ec6bc5a13fcce6fecfb76b30"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987694658de2245b256cdc6b384c078c96", "guid": "bfdfe7dc352907fc980b868725387e98da0da6c27553674e5a1460b1f1c1f43c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980f9bfb6a957933f03d42ae9f08a00c2a", "guid": "bfdfe7dc352907fc980b868725387e98d77cae430085c8e290350601b804a8bb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987587195a2a3c40caba2e671a7bfc526a", "guid": "bfdfe7dc352907fc980b868725387e985674cb27c45a509567b89f3e92660fc8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98820fbfc0dec9b9068ac20cfc4242f11d", "guid": "bfdfe7dc352907fc980b868725387e98542a861bdddeb74f700daeb08a92d616", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988781906e2c4da1b7bfc8cf021c7ff4fb", "guid": "bfdfe7dc352907fc980b868725387e984b0894b0508d456182664c404148fb14"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98071023953110ef55619cc06d80704ca8", "guid": "bfdfe7dc352907fc980b868725387e980ff4020411c899dc4b7162c89ede7848"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9855e440a07bb0105192df5c88695be089", "guid": "bfdfe7dc352907fc980b868725387e98fdfcb5ed547c463ad41369784179cafb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9841208f2217bd7e8b413f077128456e42", "guid": "bfdfe7dc352907fc980b868725387e98ea0bc51e827917f028c722dbbdac9886"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dbde796b8fe1ee888318e296e95d4fdf", "guid": "bfdfe7dc352907fc980b868725387e986d333c76b08c1a8d7c5f5df3e1b1dccb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d7fd2cd97b2281b18d4d712e82e0d719", "guid": "bfdfe7dc352907fc980b868725387e98d5703e35871101b13dcd3dae57b113cd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a9a19ac7a51ad78877784acaab464b23", "guid": "bfdfe7dc352907fc980b868725387e983d92dc64106d4b409c97a4b19e939c2b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f4b6cb97362e12ddaac83a4092e8c789", "guid": "bfdfe7dc352907fc980b868725387e98608b6fe2f59c4cfeb1b3f1fae926c4d7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ad9ea19a2c947c1bb7507ba951c24b07", "guid": "bfdfe7dc352907fc980b868725387e98eb58d39c8f88af633947c898b0a91650"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98765689f85b3eb1a99f2f7803c7d91a3d", "guid": "bfdfe7dc352907fc980b868725387e98c7f0dffde8f1d624f92fcce1986f2fa4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983578450b773ac2f119b376d568587e1b", "guid": "bfdfe7dc352907fc980b868725387e988b0be168cfd1db9a8b0fd00a2bf9ef70"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a1f099dd81da263eba5c575a748fd526", "guid": "bfdfe7dc352907fc980b868725387e98f9d0f6390e34838d8ae828e433a0a6fa"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986f08575991288683252603f68e17e487", "guid": "bfdfe7dc352907fc980b868725387e9823a943e42e3c64c86050971aba1d2680"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b43fc50a00b6db227f08379fcafdc2e4", "guid": "bfdfe7dc352907fc980b868725387e9885e32042df49d3637cd8aa62e5fd62ca"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d7e14cf76279c5dd5f306a4e44a14cee", "guid": "bfdfe7dc352907fc980b868725387e981c303e8738dd6bde7450dcbed91e77da"}], "guid": "bfdfe7dc352907fc980b868725387e9879c81d84e254749daf2145f90e6da9e4", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e987207ecf6afc704d2ca7720bb1c0dfcd6", "guid": "bfdfe7dc352907fc980b868725387e98a01a9c80955fdeb090c7e27fdf3d8ae8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ee4984baae878231949df889a2f7ccd0", "guid": "bfdfe7dc352907fc980b868725387e987382d6e8244d7f1198c6e2cca0b501f1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9829ec094f2940fc35266a576fd4b053ef", "guid": "bfdfe7dc352907fc980b868725387e981e95c602c20a71ef8008e431a1b789e4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d5cf0168705beed97b9e5d7dc326a949", "guid": "bfdfe7dc352907fc980b868725387e984dd0b5492e1e0916e5c67e1192d9ca8f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980992e54fca356de2fadac5c8c03da9c6", "guid": "bfdfe7dc352907fc980b868725387e982e1444d143252f8af9ad7f6d8f72fe43"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983ac7807ebe6db8430c79ddf70a75ff62", "guid": "bfdfe7dc352907fc980b868725387e9873dcc391c4553dceba2a24345d91a7ca"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9869e09bd513e987079570f747b5104018", "guid": "bfdfe7dc352907fc980b868725387e983f9e9ab249614c0248f9879a108c6280"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9813b91982946c747d97e993a9a11c6a9e", "guid": "bfdfe7dc352907fc980b868725387e9846c8ab337acf088d2e34d6dc8b47c974"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9882d6fb71f25fd185b0d927d8492488a4", "guid": "bfdfe7dc352907fc980b868725387e9827b3230c4dfdc39a4ea75fd0795476a1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cb0e24526a7991796d10408507da1a55", "guid": "bfdfe7dc352907fc980b868725387e983858c3c5d55a050fae56a3d377707aec"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985504c655c8eaf7fec056d34c929bdfc0", "guid": "bfdfe7dc352907fc980b868725387e9897287cbb90e2436fc6e4d4424a312dc3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988fae31c4de257e64641cb3d17bc39b93", "guid": "bfdfe7dc352907fc980b868725387e9867c4b9d9fca6906db5dca77e25680cc8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98603810e219dc5608b43ae50a834b399f", "guid": "bfdfe7dc352907fc980b868725387e980467be48bcedaa524c7f6e57dccd3115"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f7251d66884a61a5d4a663251d29a1eb", "guid": "bfdfe7dc352907fc980b868725387e98e9d4f031a81602f6eecbcc89f582de36"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a63ea1a6361f23c59aa613877ed2534d", "guid": "bfdfe7dc352907fc980b868725387e98f514c891e3270581f37f63ad16c9339c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ef2a0b28b8e09b5c89bffd0de2b81b5b", "guid": "bfdfe7dc352907fc980b868725387e989d6707cfcc733032815f2ac95e3a5ed2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9825f5b263169f3877f41d48a15393a82f", "guid": "bfdfe7dc352907fc980b868725387e9855e30f747315ddd2749c33cfcc9c6a5e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f101be0114c804c0ec1174242d6efb71", "guid": "bfdfe7dc352907fc980b868725387e98e6e582e6f726f589a3717953d4ca048a"}], "guid": "bfdfe7dc352907fc980b868725387e98b96e4d36d466f81296224c6cdf3e0443", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9834dfa23abd1ee3cbdf63f2e7e7c8692e", "guid": "bfdfe7dc352907fc980b868725387e98f7613190f175f810d2877c35ebed81e8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a22b70908b57b2cde3e263c1645f2bb4", "guid": "bfdfe7dc352907fc980b868725387e98b94c8343a7f842ca6eba9aa52f3265d0"}], "guid": "bfdfe7dc352907fc980b868725387e98d0c49e3cacf4becf26716920127b6925", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98d6f278cf93e9e50b83a879b9f845ef5e", "targetReference": "bfdfe7dc352907fc980b868725387e984535f130e81fa6507008242e4e8916fc"}], "guid": "bfdfe7dc352907fc980b868725387e98ef1bd380a194719835e31830fb71f577", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore"}, {"guid": "bfdfe7dc352907fc980b868725387e984535f130e81fa6507008242e4e8916fc", "name": "FirebaseInstallations-FirebaseInstallations_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}, {"guid": "bfdfe7dc352907fc980b868725387e98f10882e1684b8a3dfdec597bc0a47af3", "name": "PromisesObjC"}], "guid": "bfdfe7dc352907fc980b868725387e98566ec9a1d71c4629f4f85ecb735ce614", "name": "FirebaseInstallations", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e9860819b8e327bf41b291e92315614a812", "name": "FirebaseInstallations.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}