{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98e2d114b44337b2bf6e6d7706235d6197", "buildSettings": {"BUILD_LIBRARY_FOR_DISTRIBUTION": "YES", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/GoogleDataTransport", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "", "EXPANDED_CODE_SIGN_IDENTITY": "-", "IBSC_MODULE": "GoogleDataTransport", "INFOPLIST_FILE": "Target Support Files/GoogleDataTransport/ResourceBundle-GoogleDataTransport_Privacy-GoogleDataTransport-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_NAME": "GoogleDataTransport_Privacy", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e984e8be5a46bdedec11138b29953e2c4e0", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98091b3d9192a3118ff23d0aa53194229a", "buildSettings": {"BUILD_LIBRARY_FOR_DISTRIBUTION": "YES", "CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/GoogleDataTransport", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "", "EXPANDED_CODE_SIGN_IDENTITY": "-", "IBSC_MODULE": "GoogleDataTransport", "INFOPLIST_FILE": "Target Support Files/GoogleDataTransport/ResourceBundle-GoogleDataTransport_Privacy-GoogleDataTransport-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "PRODUCT_NAME": "GoogleDataTransport_Privacy", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e98a79019821f944f81f910720ac2f25180", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98091b3d9192a3118ff23d0aa53194229a", "buildSettings": {"BUILD_LIBRARY_FOR_DISTRIBUTION": "YES", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/GoogleDataTransport", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "", "EXPANDED_CODE_SIGN_IDENTITY": "-", "IBSC_MODULE": "GoogleDataTransport", "INFOPLIST_FILE": "Target Support Files/GoogleDataTransport/ResourceBundle-GoogleDataTransport_Privacy-GoogleDataTransport-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "PRODUCT_NAME": "GoogleDataTransport_Privacy", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e98fe94221f211b64e259492f212f92e188", "name": "Release"}], "buildPhases": [{"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e982d4908395318c67e4fd319bf5a058648", "type": "com.apple.buildphase.sources"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98586a7426841e7b5b87a688f3b86061ca", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98ecbb0f14f705b7b7b8fcfb6ee23d4bfc", "guid": "bfdfe7dc352907fc980b868725387e98e173a9a87ff478551a304cbbcc3d6984"}], "guid": "bfdfe7dc352907fc980b868725387e98be4ae8c8ba24f5966af833af0badba01", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [], "guid": "bfdfe7dc352907fc980b868725387e98bb3e3ebadbb0b9a8a4f20f605e3cb3cb", "name": "GoogleDataTransport-GoogleDataTransport_Privacy", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e988384e3ef3584a97142df3583f18d4cf4", "name": "GoogleDataTransport_Privacy.bundle", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.bundle", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 0}], "type": "standard"}