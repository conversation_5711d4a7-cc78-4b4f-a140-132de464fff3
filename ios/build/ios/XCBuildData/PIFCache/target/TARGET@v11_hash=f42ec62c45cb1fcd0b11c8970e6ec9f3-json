{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e982e7ca64270468e58795f9b7e053409c5", "buildSettings": {"BUILD_LIBRARY_FOR_DISTRIBUTION": "YES", "CLANG_ALLOW_NON_MODULAR_INCLUDES_IN_FRAMEWORK_MODULES": "YES", "CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/src/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/src/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/google_maps_flutter_ios/google_maps_flutter_ios-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/google_maps_flutter_ios/google_maps_flutter_ios-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/google_maps_flutter_ios/google_maps_flutter_ios.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "google_maps_flutter_ios", "PRODUCT_NAME": "google_maps_flutter_ios", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98f1f8693f7c908f5b0affe1f241e6c220", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e981c394ba86af6833b1bc0454605653515", "buildSettings": {"BUILD_LIBRARY_FOR_DISTRIBUTION": "YES", "CLANG_ALLOW_NON_MODULAR_INCLUDES_IN_FRAMEWORK_MODULES": "YES", "CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/src/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/src/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/google_maps_flutter_ios/google_maps_flutter_ios-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/google_maps_flutter_ios/google_maps_flutter_ios-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/google_maps_flutter_ios/google_maps_flutter_ios.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "google_maps_flutter_ios", "PRODUCT_NAME": "google_maps_flutter_ios", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98b731d65cc4406511fff066edd613cb3e", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e981c394ba86af6833b1bc0454605653515", "buildSettings": {"BUILD_LIBRARY_FOR_DISTRIBUTION": "YES", "CLANG_ALLOW_NON_MODULAR_INCLUDES_IN_FRAMEWORK_MODULES": "YES", "CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/src/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/src/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/google_maps_flutter_ios/google_maps_flutter_ios-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/google_maps_flutter_ios/google_maps_flutter_ios-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/google_maps_flutter_ios/google_maps_flutter_ios.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "google_maps_flutter_ios", "PRODUCT_NAME": "google_maps_flutter_ios", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e986d26b1adc32d733906901b1009d9fe42", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98c6b541aa655e867f4ffee32b42a6a3f1", "guid": "bfdfe7dc352907fc980b868725387e9826d944e7e5dbe34563446a3e758e3c97", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c039c345d88906b7b0dadb483bd5a4f0", "guid": "bfdfe7dc352907fc980b868725387e98a7fa45549d850eb8c72c903f1d4df534", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c51ae62b14e108c3875700e82e9f90d6", "guid": "bfdfe7dc352907fc980b868725387e98c9980ec40e14e533520302c0addfc129", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988316c1b27223469507b39ad13af25ce6", "guid": "bfdfe7dc352907fc980b868725387e981a0253ca8236b2356ace6497f390b8da", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9813b556af1c2df5813891cd09b085c6d2", "guid": "bfdfe7dc352907fc980b868725387e9883f423328b710e16048e6c1e36f95f07", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bd15217a1bc35fc5fc20d40047a0d9ad", "guid": "bfdfe7dc352907fc980b868725387e98ab72dedf3bdd3d652c3e6660af0d44df", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9815c5c72e319a4835b3383717ca762551", "guid": "bfdfe7dc352907fc980b868725387e985e560f967fc36d3f5792db6210cd8d7f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9862b8059028b06f03a98335b2292161f8", "guid": "bfdfe7dc352907fc980b868725387e98ee8d700f8fdbed195242f6232fa9edb3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f7a8b2dff4faadd2191889e955db4ddf", "guid": "bfdfe7dc352907fc980b868725387e98ebfbb7e756d1ebaa66324a532de8abdf", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98555cfa1dfdfebc7eba818f08d1199770", "guid": "bfdfe7dc352907fc980b868725387e98db4cbd66691a075dec236f2f049f783b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984a1c0ed39bb8f15a1cb512be520765ed", "guid": "bfdfe7dc352907fc980b868725387e98f4fdd91f6659afc648c05e57c4edf8e7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f067f2ff18193189eca56761dc9e02e7", "guid": "bfdfe7dc352907fc980b868725387e98d38d7209dfbeb30917d103def304e045", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98aed7df9c1b67499681042f2373e164ad", "guid": "bfdfe7dc352907fc980b868725387e98e1b835fe1d12f123de44830b9d39ef33", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9858c9ccc7f38363b020ff30127ce9d0ec", "guid": "bfdfe7dc352907fc980b868725387e9897f4d8054d146697b89e9ae95e6cab1a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983a04f59b793a1ac76b78bb9b941c347f", "guid": "bfdfe7dc352907fc980b868725387e98f4cae67c92706a04a914adb1f102c5d4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9848c73c2967e2c5c94c181f91c7b200d2", "guid": "bfdfe7dc352907fc980b868725387e986da3806c1c15c1c19d947c614ab658bd", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e9891b6c87882cff834a6d4722792d27722", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e985bcf096056ea9255460dfdb874107c3d", "guid": "bfdfe7dc352907fc980b868725387e980e81720d61a3801d969422a07b3694f1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986d24f865d340055935400553ed8ddf67", "guid": "bfdfe7dc352907fc980b868725387e9852677058a7640559c4feadf4e3c0c5f3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9882e16449cbf7dd7592f01084d117c7de", "guid": "bfdfe7dc352907fc980b868725387e98f9e123e91d169f5077b5b4c09138b1f4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ed83a11497c4e07f4bd5a9fd3748dde3", "guid": "bfdfe7dc352907fc980b868725387e98b4dc059e1d83e87ebcefe43ba94df0f6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98967b27d4c8b36679d53f7dd25ef9de1a", "guid": "bfdfe7dc352907fc980b868725387e9861a80f10186a44162739a66582e34ac7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981a21f1fa861236f253e63e326d6ab800", "guid": "bfdfe7dc352907fc980b868725387e98321228c8645ec8c337f06a4080584f23"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986db1e853ea6f38291f5748b1e963681f", "guid": "bfdfe7dc352907fc980b868725387e9876e2fcde8e9bf49d940acd65e1dee028"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ac9c9ccdd5a347662f7e5d495d787ffb", "guid": "bfdfe7dc352907fc980b868725387e98127082dd44fec19b84a6b82a9671f1ac"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b2a3481740b758cd66ff1a273562bed7", "guid": "bfdfe7dc352907fc980b868725387e98e7358517f8700c32c40012ae498f65d0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a0538329162c2e2688fafb345eb2b08a", "guid": "bfdfe7dc352907fc980b868725387e986e96d52f7011c1a80218cf8d88c5daf1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981dd9515b0879952c23abb5c2264c11fe", "guid": "bfdfe7dc352907fc980b868725387e98c2c33a008b1dfed23a9d7d00602717e2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cee866d561a67926e68e239ebb41c343", "guid": "bfdfe7dc352907fc980b868725387e9807681073bd833184cb12f4453cb4aa18"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982390910d44af626f07399d624a6b12fd", "guid": "bfdfe7dc352907fc980b868725387e981992c38dfd126c35ec1d145b29914f16"}], "guid": "bfdfe7dc352907fc980b868725387e98448c8389dfc199dac83239b7c4d4a87d", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9834dfa23abd1ee3cbdf63f2e7e7c8692e", "guid": "bfdfe7dc352907fc980b868725387e98f30217f2638041bc05fbf64d6676681c"}], "guid": "bfdfe7dc352907fc980b868725387e9837666ca8f131a9306dd950b0c4ffecf0", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98ceec10663e715758ba9294abee964c51", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e98117b13c59de776c223f2f14af197afb1", "name": "Google-Maps-iOS-Utils"}, {"guid": "bfdfe7dc352907fc980b868725387e9818352c54edac2258b91768852065ce5e", "name": "GoogleMaps"}, {"guid": "bfdfe7dc352907fc980b868725387e9845fff747e8d3c707f1d7451d71a9982f", "name": "google_maps_flutter_ios-google_maps_flutter_ios_privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98df83286ef0c813795b2a6e5600f49912", "name": "google_maps_flutter_ios", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98e749aca54f09b9c5c4f2ba052cee0d36", "name": "google_maps_flutter_ios.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}