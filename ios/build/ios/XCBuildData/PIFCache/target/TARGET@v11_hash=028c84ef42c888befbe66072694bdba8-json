{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9850431216ea7d3e231fef45abccb8f24d", "buildSettings": {"BUILD_LIBRARY_FOR_DISTRIBUTION": "YES", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/sqflite_darwin", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "", "EXPANDED_CODE_SIGN_IDENTITY": "-", "IBSC_MODULE": "sqflite_darwin", "INFOPLIST_FILE": "Target Support Files/sqflite_darwin/ResourceBundle-sqflite_darwin_privacy-sqflite_darwin-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_NAME": "sqflite_darwin_privacy", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e989207f6b3e40bef473186683e333d3058", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9814013af89f3e15e6fe89ee24d71e686a", "buildSettings": {"BUILD_LIBRARY_FOR_DISTRIBUTION": "YES", "CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/sqflite_darwin", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "", "EXPANDED_CODE_SIGN_IDENTITY": "-", "IBSC_MODULE": "sqflite_darwin", "INFOPLIST_FILE": "Target Support Files/sqflite_darwin/ResourceBundle-sqflite_darwin_privacy-sqflite_darwin-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "PRODUCT_NAME": "sqflite_darwin_privacy", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e980918b75e665026dcf332cc04568b68a4", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9814013af89f3e15e6fe89ee24d71e686a", "buildSettings": {"BUILD_LIBRARY_FOR_DISTRIBUTION": "YES", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/sqflite_darwin", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "", "EXPANDED_CODE_SIGN_IDENTITY": "-", "IBSC_MODULE": "sqflite_darwin", "INFOPLIST_FILE": "Target Support Files/sqflite_darwin/ResourceBundle-sqflite_darwin_privacy-sqflite_darwin-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "PRODUCT_NAME": "sqflite_darwin_privacy", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e986ecced83ac694fe1c70e3e26e81a9437", "name": "Release"}], "buildPhases": [{"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e987b76f881c4ad1c093dfa8a864c39a603", "type": "com.apple.buildphase.sources"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98562f02a54a1264ce78756d8476e257ed", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98dea1b4ef0cced1c218921fb633c8439f", "guid": "bfdfe7dc352907fc980b868725387e98441e3b034b1779f1b70da47a3df0a745"}], "guid": "bfdfe7dc352907fc980b868725387e981f25eb2298ccc49f6fb66bf2c4e7c68a", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [], "guid": "bfdfe7dc352907fc980b868725387e9883134bb5f399cb37a1eb075d4fea30d8", "name": "sqflite_darwin-sqflite_darwin_privacy", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e9849c1d4b1200fcbf6f387f94121c7d0bf", "name": "sqflite_darwin_privacy.bundle", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.bundle", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 0}], "type": "standard"}