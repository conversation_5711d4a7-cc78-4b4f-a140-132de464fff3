{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98d2eb0c13019a7554321adb3a6e51eb5f", "buildSettings": {"BUILD_LIBRARY_FOR_DISTRIBUTION": "YES", "CLANG_ALLOW_NON_MODULAR_INCLUDES_IN_FRAMEWORK_MODULES": "YES", "CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/src/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/src/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/flutter_inappwebview_ios/flutter_inappwebview_ios-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/flutter_inappwebview_ios/flutter_inappwebview_ios-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/flutter_inappwebview_ios/flutter_inappwebview_ios.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "flutter_inappwebview_ios", "PRODUCT_NAME": "flutter_inappwebview_ios", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98b89882dc28a6d5c860523e82f554cc84", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98494965bd9574dd867c12c889fe0b8316", "buildSettings": {"BUILD_LIBRARY_FOR_DISTRIBUTION": "YES", "CLANG_ALLOW_NON_MODULAR_INCLUDES_IN_FRAMEWORK_MODULES": "YES", "CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/src/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/src/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/flutter_inappwebview_ios/flutter_inappwebview_ios-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/flutter_inappwebview_ios/flutter_inappwebview_ios-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/flutter_inappwebview_ios/flutter_inappwebview_ios.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "flutter_inappwebview_ios", "PRODUCT_NAME": "flutter_inappwebview_ios", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98e29cd749ce03643e9abbafc9d7ee4c45", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98494965bd9574dd867c12c889fe0b8316", "buildSettings": {"BUILD_LIBRARY_FOR_DISTRIBUTION": "YES", "CLANG_ALLOW_NON_MODULAR_INCLUDES_IN_FRAMEWORK_MODULES": "YES", "CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/src/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/src/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/flutter_inappwebview_ios/flutter_inappwebview_ios-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/flutter_inappwebview_ios/flutter_inappwebview_ios-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/flutter_inappwebview_ios/flutter_inappwebview_ios.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "flutter_inappwebview_ios", "PRODUCT_NAME": "flutter_inappwebview_ios", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e982fd3c6a7f4d84d77d3348746052d187a", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9877f380abeca3101e3d481504b2531f2c", "guid": "bfdfe7dc352907fc980b868725387e98807bebc10b9b2347b3c38ab76dcd39c8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9804f92f9d07af438520476a07b53885a0", "guid": "bfdfe7dc352907fc980b868725387e98830df83e0ec997753ec519538ef6f60c", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98e2f978f0bcd85da9c3f3f0ea66389fce", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98e0d3c0ab06af4bf215a3c85cca2979ea", "guid": "bfdfe7dc352907fc980b868725387e98ccdfea99e0cc660b9001c8a5d5adb33a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986206d4b05703569caef44f5e5adfdb00", "guid": "bfdfe7dc352907fc980b868725387e98dc5de76e264055f43c861d8493e01121"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9820535814f9451bd72a67a980a77f4f8b", "guid": "bfdfe7dc352907fc980b868725387e9871791bf8d2f68b9f58e2fe1d470f4886"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9809e855ac742592471b09cef45ab23337", "guid": "bfdfe7dc352907fc980b868725387e98250472815ebabfbe2170b8bbf902db8f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98578c8235cb5f6fff854b9b0a20bc7318", "guid": "bfdfe7dc352907fc980b868725387e9875bed1f957181399dbba774b75f2fadd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988158207f4ecddc1b59cd1875e7435106", "guid": "bfdfe7dc352907fc980b868725387e9883888c5b2799e1a0437df6bc3e5fbba7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9809ce9d8165ac0490930828946a0a244b", "guid": "bfdfe7dc352907fc980b868725387e98e6c0000aa9967ac168d5c88601b76b16"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9882a4d79bcbd4f426bc9b4e14a94b2823", "guid": "bfdfe7dc352907fc980b868725387e9808eca2f1587502e4fb0253dd70590083"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b90eafc7d83199f6eada2a625bd37721", "guid": "bfdfe7dc352907fc980b868725387e98a72d821a485fdcedd26adedfb0a2b23d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98247c8b3edc571427174c1d9244efff00", "guid": "bfdfe7dc352907fc980b868725387e98ca4096acac43a317bda5ba0c47bb8bda"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b55d793d747a1035a3bb622b05083be5", "guid": "bfdfe7dc352907fc980b868725387e982ad692615143ac287df3354fda01c162"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983e9e511be6774025405c737ec26b0b08", "guid": "bfdfe7dc352907fc980b868725387e9864b3dbf6d6ced697454ea174127f7c6a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9872918f3a45d86f2c638bb71582a38d0b", "guid": "bfdfe7dc352907fc980b868725387e986fa224351bf07d506906474ad5f24fc3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986192c6e1f787df8cb8aa5f52beac348e", "guid": "bfdfe7dc352907fc980b868725387e980c4b58a8297e745a2a277b5c4d9247fc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98facfbacfc820679e0b7967b342e57a31", "guid": "bfdfe7dc352907fc980b868725387e9812491126bbc5062ff02880e7b428066a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9827f1bce76c75acfd9afdfa8c2b91f833", "guid": "bfdfe7dc352907fc980b868725387e987c7759cedb074971adacad0051eaa2c5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fb342f466bd5018d88bf696b9b9c18f2", "guid": "bfdfe7dc352907fc980b868725387e98ded215c4320567521f37c44a3f5f8d43"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e8b5c67529dbf2e54de03cc9872b2ab7", "guid": "bfdfe7dc352907fc980b868725387e98d1f5a1d290b761132be798590f0e63cb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985d9b08225f3f5e1c5ccc0b432b25195e", "guid": "bfdfe7dc352907fc980b868725387e982fca89ef90efb3c5d03b926ccf48972b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982fe2e846554a37c458dfe0b155e5869d", "guid": "bfdfe7dc352907fc980b868725387e98724a5382285ba30611c6c80c7223a6cf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cabe6c97093995cec79cdc92b143ab8c", "guid": "bfdfe7dc352907fc980b868725387e98314e1d0592601eec0305df273487779b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cf472eee7a72ebf777b06ca62717bc73", "guid": "bfdfe7dc352907fc980b868725387e982da34c67b6738b27c0b8164aa29b2f6b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f1ebb1cf6de2c6041813ffc8ee60e733", "guid": "bfdfe7dc352907fc980b868725387e983f12e6082182025f7e041ef080820aca"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a0cb40d48750894ef14beb136649983c", "guid": "bfdfe7dc352907fc980b868725387e98cbe2590344c31ed3a21c3773426e3fe4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986fb39eafba57f086fac6b8f9ac6c433a", "guid": "bfdfe7dc352907fc980b868725387e98b35df1c3f81bed4424b8b446948a1c70"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ae927eae2f543804e8caa02d9c92280d", "guid": "bfdfe7dc352907fc980b868725387e989eb0d23d507b408da8575a7e972a2cf2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98951deedf0b818fbf212f3dd250bd0adf", "guid": "bfdfe7dc352907fc980b868725387e985359bf3f299bad8a26efcc1796d7c1cd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981faa42d44917a89aca8718674e0efe1b", "guid": "bfdfe7dc352907fc980b868725387e98462265d7e73c937aba0c1a0c7a05f7e6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9842ebd64cde5723b433e90485d5777ef1", "guid": "bfdfe7dc352907fc980b868725387e989cc343ddd199dcedcf3100c93f46534f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d7e507a4f3354f55a4d7b24412e1b6d3", "guid": "bfdfe7dc352907fc980b868725387e98ab5fcee13c8c4444d60bddb6a295be5e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983376dda157d7520fd83daeef7e99599d", "guid": "bfdfe7dc352907fc980b868725387e981f3185aeafddcb4f6ad2369d0f8b8b8a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985c6a3a8123c713dbd3922b8a8cdf40d1", "guid": "bfdfe7dc352907fc980b868725387e988129f147513ddb8d2cd36defbe96ac9b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c0ef7a55f444ea2f1f7d09e431df0b7d", "guid": "bfdfe7dc352907fc980b868725387e98a160350763ead235ae8e93e475392e1f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988b6afdd4274706ec99e32d72bd5574ae", "guid": "bfdfe7dc352907fc980b868725387e98164e73f85bbfb94e6fd4ff8e92faf005"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98260a16d3ad502e88d13a4d05f6d89868", "guid": "bfdfe7dc352907fc980b868725387e982ca5c8128c4301080ce2ab76a87de2a5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ffbb15ac74d08887e35ce7dada20cb9f", "guid": "bfdfe7dc352907fc980b868725387e98a3aa5c4326493008dfe5627384906f85"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ffde3b38a0f829e4489080c75c086930", "guid": "bfdfe7dc352907fc980b868725387e983ef3a1fa9beea16913e59206e92a27fa"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9839ce18f71db476ff140a96f1cf94e606", "guid": "bfdfe7dc352907fc980b868725387e9894d4fc4cdf98039d9ddf9524d12d795d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980ab48035b225074dc19656ff7bb5783a", "guid": "bfdfe7dc352907fc980b868725387e98db847b3345cc1944e6e7659ddef1e272"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989574f185555bde6e6e7f7fabf0d670c7", "guid": "bfdfe7dc352907fc980b868725387e98ce1c4d48c89aab4d580616508036dd9d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98372be1357eeafcf273ee2b0197be71ff", "guid": "bfdfe7dc352907fc980b868725387e9819971fed787079059c7146de03897424"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9899550fd47b6d9cbfa5596b9ff9c59c18", "guid": "bfdfe7dc352907fc980b868725387e985694432eba2505c3088b40c4577e6121"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c764e14fa3873cf06dad5eb4b880a25e", "guid": "bfdfe7dc352907fc980b868725387e98e100eee6ab91604739cd83bc225305cd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b135f3ab73ba4518d0d02c8bbb388d6c", "guid": "bfdfe7dc352907fc980b868725387e98f05e2e6f5f20f2097468207b2b08f057"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9857a18677e7a22fb13caf906439a29577", "guid": "bfdfe7dc352907fc980b868725387e9829d94119edcc321aea742c339acb7459"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987e6e4a871fc6ef9392704466423400e7", "guid": "bfdfe7dc352907fc980b868725387e980facc7b14738d485db14bdf99debf547"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98187590a80e08d98a86cd28a72fdb7341", "guid": "bfdfe7dc352907fc980b868725387e9875aab928f91efc50bca0b3a4b53323ed"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a62028e7ba0515ae007fecd2648d9a59", "guid": "bfdfe7dc352907fc980b868725387e98cdd19bdba7752c5cd9ec9adbc95dfd75"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9823c8e3837d0bd6272edcd51f8e4a11b8", "guid": "bfdfe7dc352907fc980b868725387e98e5de14b5482be462d4bb44c08a0b4e92"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9893aff397564f17b63dd240d533e4321d", "guid": "bfdfe7dc352907fc980b868725387e983cea68a78c71ced38e5925a70d2e4d0a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e48b5b701ca0e3112607429dab35f2b6", "guid": "bfdfe7dc352907fc980b868725387e98811167c16fb4f59c13d4f45c4b0bf823"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982e4822b6a081b3e2d026089f532a9059", "guid": "bfdfe7dc352907fc980b868725387e982fae6087faf90913c54ca9ba872a78b1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f8394dcd23238181edeb38550db1abc8", "guid": "bfdfe7dc352907fc980b868725387e981dda166fea1d00755baa7c2e29f91792"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b4e7d681c90178ea2587d7580bc18048", "guid": "bfdfe7dc352907fc980b868725387e98c27886c8f0e54a0a2e68beffe517c4c0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f64a9d37bc821a723a04fad802605272", "guid": "bfdfe7dc352907fc980b868725387e989da49c25cceb0f746dc7b6d52a264248"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b32aa8fb0b6c8fc893ea2703ef6eae34", "guid": "bfdfe7dc352907fc980b868725387e98c2daa5cb5599c651b6c709acea822f42"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fbdba72c10ddc14fd1991837838afc35", "guid": "bfdfe7dc352907fc980b868725387e98f995a9f501a217d614b8f4b2cecf3390"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981a3a5ca28837f56045d7909ff2010ae2", "guid": "bfdfe7dc352907fc980b868725387e98d2ff23ba143638be7746ca5b910f7369"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9883318f0b8981ce039b1eaa9c5fb882ba", "guid": "bfdfe7dc352907fc980b868725387e98bbffa023bc2ad159c246eb78792c3849"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98653583afe6177e70469929839b945d15", "guid": "bfdfe7dc352907fc980b868725387e98aa8696a741ccd4b6741719a926fea194"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988b09ab201de56d7ea1202364fb3fac01", "guid": "bfdfe7dc352907fc980b868725387e980b0a11f669d8564f8815d6d3a179b046"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d25c771157adff37d1eb2506ca697bbe", "guid": "bfdfe7dc352907fc980b868725387e98ce47c641f0458341bf50d7b25f1c6a2f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98463d0a00271db7c236b6f6df8a574ecd", "guid": "bfdfe7dc352907fc980b868725387e98f9e52fc7467b0174f0e055e0eba47775"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c07b6547cd7e4fcffc41abf77d1fd75c", "guid": "bfdfe7dc352907fc980b868725387e98d2b0721788df1c5d87ce43d2ad84dff9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9862032ea3ed86474ee972982238c08578", "guid": "bfdfe7dc352907fc980b868725387e98a22b91bc30195e74d25116f730383a0f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ca22c361d3247fa968ff9ed7dfd53d61", "guid": "bfdfe7dc352907fc980b868725387e98cf64948e2e73b70435172d32ba723704"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988977281ef1c6c5ec7370ab22b9346ab3", "guid": "bfdfe7dc352907fc980b868725387e981ad231c682034a6053a672fe7e0b2c42"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d903819a6f328ab4f46e62d7a79f5724", "guid": "bfdfe7dc352907fc980b868725387e989ccdfd00db0fd04284ddf335fafa86f5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d01fbf9f419d388838c11d41d5642ad6", "guid": "bfdfe7dc352907fc980b868725387e986aa0ed2968449bb085643a863f0937ad"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c217ed6c464da67ca665508bc7f4251f", "guid": "bfdfe7dc352907fc980b868725387e98e588ceaedef830278f4fcdfa03d515c1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987699ff499d51be4f950ad56951e423fb", "guid": "bfdfe7dc352907fc980b868725387e98fc667a1b02350a9be46c6509f23aef81"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d9f478041584702151c0450eb7169f48", "guid": "bfdfe7dc352907fc980b868725387e98162e2a5689a129dba61a30062c656c93"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981dccc183ce7010c8f37977c062353bf1", "guid": "bfdfe7dc352907fc980b868725387e9874c29c73a36beaebd65b0cb0fb9225af"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98090bc0165665b0c3f5efc10d1be8f9f5", "guid": "bfdfe7dc352907fc980b868725387e98d432c3f46b28ab7c360d9d055f1a6ac9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9880fbf4bbefac17a0c71be3377fc14bc5", "guid": "bfdfe7dc352907fc980b868725387e98a94054347508856afb9f5fd629bd1851"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dfa3a58fe06fb60bfe0d0154d2418f37", "guid": "bfdfe7dc352907fc980b868725387e98eeaa4a3d7df0ff0514f95cf919a6efc0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9870d257205531ab0252b7a7893c40ef95", "guid": "bfdfe7dc352907fc980b868725387e985b9dbcf06d35ab48c4feae2b62c0a9b5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989d1c102411fb0a8b9e86aa3b344f6da7", "guid": "bfdfe7dc352907fc980b868725387e98f0cb55ef7a1593e9205541db820b22c1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9814e0966f29e2db81c0ee4afe86c0bcaa", "guid": "bfdfe7dc352907fc980b868725387e98193b45a979ab1a1e15db8ad8f20b7024"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982400b9f6f3016fcd9c4e625cc3512d96", "guid": "bfdfe7dc352907fc980b868725387e98b33dff1b8addf2e809060b9f0431620b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988555ea4a64e429f0110e7f09c2597c13", "guid": "bfdfe7dc352907fc980b868725387e98c37a16228db23d06e86e00dd29019c33"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989580e81755eb6b93cd457b628badd3cb", "guid": "bfdfe7dc352907fc980b868725387e981e2e1346a1633ce1ee4eb4cb55b57c7d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980ac382fb40bdc50690e1664165dc3c25", "guid": "bfdfe7dc352907fc980b868725387e985d9ebef983dfa659514f8975005289dd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987ee397d55bbfe1c9cb10e516ddb4689f", "guid": "bfdfe7dc352907fc980b868725387e984c9519493ccdac0b918d35fb0cf523af"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987f0c1c5e5c985c44f9b35c43ba6840cb", "guid": "bfdfe7dc352907fc980b868725387e98e80213b22df3304548a02d49a2d8e3f3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9834e68462e23e95c9cd9829fb2911e63b", "guid": "bfdfe7dc352907fc980b868725387e98f08ee4bfef07c27371e0706f6620feb2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9851a26f4d23bb8cc890e1f290133b708e", "guid": "bfdfe7dc352907fc980b868725387e9875e616075cc95c8cd36fbd21fd6b0440"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986799260da01bb781867c7caf7d7f1c4d", "guid": "bfdfe7dc352907fc980b868725387e989658d9408f6bba8d3ca6ca5e9dbc8c90"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98807ce0f883ef18c693b7acd833d43f97", "guid": "bfdfe7dc352907fc980b868725387e981a727837450fac18a64627732dc82897"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f2f6011204caf69a56318e1cc1b5354c", "guid": "bfdfe7dc352907fc980b868725387e98be4bfcca04bf0482d361a80c377ffc42"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f29d06283a616e54af5e256656c42198", "guid": "bfdfe7dc352907fc980b868725387e9885397fa3e0a6ac3fe2c9b9c7b909826c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989012b5856b8a694e52efa40b9fa0d113", "guid": "bfdfe7dc352907fc980b868725387e98d61c6522170340c11f804d594d516906"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9800aec17ea7a44a78f302d2d862630198", "guid": "bfdfe7dc352907fc980b868725387e984e695630728ab22705d1b42df7f49906"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d045a020a6fc8cd9130e27c37323d79b", "guid": "bfdfe7dc352907fc980b868725387e9805b0bcf4ef09ecebd888d0e223785cff"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a28b762a026d620f6bc220d12fff5f71", "guid": "bfdfe7dc352907fc980b868725387e98a601a1a8dda4277e4adcc83c0e68fe33"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cb4413b40cb9c24004a3efaf8420c959", "guid": "bfdfe7dc352907fc980b868725387e98e207a2aef02fa50a6ec65bb21c712e24"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e7c0aa76fc4bb7669fe25beac85f10a2", "guid": "bfdfe7dc352907fc980b868725387e98472398837d89d36c21b64cc493a94a41"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98124ac193c8023f9bf94eef0b472e31b3", "guid": "bfdfe7dc352907fc980b868725387e98e72d95288ea46299e61700c842e7b4da"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cb2aa2a4c8a1c11ebaa9615d54fad8f5", "guid": "bfdfe7dc352907fc980b868725387e98d0061c648c5ae49d828560fcae9a0534"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985f58956ed891afe483aad9fbacf88937", "guid": "bfdfe7dc352907fc980b868725387e98225b5fe7185fdcd8126bbe8d691c29ef"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988799c6a68cdba6d681c765f7b13fb325", "guid": "bfdfe7dc352907fc980b868725387e98262a5ae95bec7b8f6379fdf1257d87a8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9846fec5eae1778b5f6b96bb8c94e6046a", "guid": "bfdfe7dc352907fc980b868725387e98e3e492a5e14907a00207c7702af8d212"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a05733061be86b91ab52cef64eedc9aa", "guid": "bfdfe7dc352907fc980b868725387e98cda12c54392375845b2f8302144b7245"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c766ce4c8e4f8bd5941a315ac02491fe", "guid": "bfdfe7dc352907fc980b868725387e98af0decb17c191742bee91d1b04138c8e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d8705a732a03995d62a84f427f7a1688", "guid": "bfdfe7dc352907fc980b868725387e98ba174d72bbb3036585382e9428ba9077"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987e1fc0344bbbbbf2b07173bd9de00d89", "guid": "bfdfe7dc352907fc980b868725387e98be57fb2fa056f7111daf382f49270451"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9883328292e6e123bc83e21e525519dc08", "guid": "bfdfe7dc352907fc980b868725387e98c14d819d66ed9375efcc6a539b85b7ec"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98af4cb88ea146778804aceb0a8be469a2", "guid": "bfdfe7dc352907fc980b868725387e98e308fbcee206f934ffb00475cc523a36"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9828f6e7c9fb6ed9a5b991f3f176e1baa3", "guid": "bfdfe7dc352907fc980b868725387e98b3132a0220e94aab07f737db82c3cfe4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988d1a9ef50eced5815ec6a67ee8df7925", "guid": "bfdfe7dc352907fc980b868725387e988893378490c2052a8a464aa57090531f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9809291edfdbb01ee9dbaa7dc9d0f51c37", "guid": "bfdfe7dc352907fc980b868725387e98154b1d25eb1a439b8767d19ef39e1ae6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9810452cdacb7013dbed72bd8244b0a24d", "guid": "bfdfe7dc352907fc980b868725387e98b0674a3442b1a2a48f3f4262ef7237b7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98462ea8a4efa9fae5a483b7cdc909d2c2", "guid": "bfdfe7dc352907fc980b868725387e98ebf7b3e19e4965170d45392782771cb7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980b003e68150d455dc7072ddf31af58c0", "guid": "bfdfe7dc352907fc980b868725387e98852d58f5e479ee1b5233ae013ab7002a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982f6477de841f383ce0099ca324e22013", "guid": "bfdfe7dc352907fc980b868725387e981fe38c3f705316063a59f18ad70ac314"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986c60117b715a55ab7325b927981eac82", "guid": "bfdfe7dc352907fc980b868725387e983c25e1b3ec34195ddd1a48092e7d5c26"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983393ac741ae74303384a5a5bad26a7ba", "guid": "bfdfe7dc352907fc980b868725387e98a68eaafbd6e7d960af0028a8386b3330"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e4d1d5a6c48424bdc04177424dce97fb", "guid": "bfdfe7dc352907fc980b868725387e983f3d27c22745d72b49edccf768470f8a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f65599f21710656c477d1e6ef2f3bebe", "guid": "bfdfe7dc352907fc980b868725387e98e8f28ae119a17ae79c19b0db5ad09cbb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982978f9a70cc198233b6bb41e1bd53d67", "guid": "bfdfe7dc352907fc980b868725387e98bc6a9ba2dd8868dbc2fb45beea927d56"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988691a246ca4fa6b2f4e095b713b6078f", "guid": "bfdfe7dc352907fc980b868725387e98d7d69d6c5dce8ac5c9d18c643e30de06"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982fae4f206807cbed141fce2a2040f50d", "guid": "bfdfe7dc352907fc980b868725387e98e3d136553c1a88705b7f7d040ca3a677"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e7deea0ea8ee1e5688ff5e4f3e9fe314", "guid": "bfdfe7dc352907fc980b868725387e98ee9a4010577dcfb314226dce4f361e27"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9886bcad87614ad8c42198698292d17334", "guid": "bfdfe7dc352907fc980b868725387e98e41af1ee7dc191c44cc159e5c2478f05"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ad8a35e08eb328bc4684f405c3654584", "guid": "bfdfe7dc352907fc980b868725387e981410763db0b310f0024e0807fdafd422"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981f07917a2d56ac4bcd38fe11be25cbcb", "guid": "bfdfe7dc352907fc980b868725387e98b96aa6334ad332103ecc32edb480d0f1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98243042324eb3f88623f81cc0b914c7f2", "guid": "bfdfe7dc352907fc980b868725387e98693ab963407f58e1d2533433981cc8a9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985bf7bcac6119af540f7d6e2ffa8d8ee7", "guid": "bfdfe7dc352907fc980b868725387e98530872d8b12748c7cc42dfe53eb608f3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98887e26de17f4df43c611b72adcd16372", "guid": "bfdfe7dc352907fc980b868725387e986550f864d69eb5b789ef74c2f45eaf60"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980aa11c9875ae7db1f36b5a8020d32698", "guid": "bfdfe7dc352907fc980b868725387e984ca6c988a2f9d3d9b69702cd0b293872"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b4a8b1e50c9d6c5071f3e4a17589d3db", "guid": "bfdfe7dc352907fc980b868725387e98c0aaaa1042ab55ac68bc3d509c3c0620"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f7836f3b0e32b572298c6ca9bb27601a", "guid": "bfdfe7dc352907fc980b868725387e9887ca8566a609060d63341628c7144b4b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982a6af7e1b224c1d2218f24d0551eaff6", "guid": "bfdfe7dc352907fc980b868725387e98c83e1006198954f0fa914a878343b98c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9899d9910b6bc1e2cac79241e9c1cb4d97", "guid": "bfdfe7dc352907fc980b868725387e98315f7552f7ef320c380e76f948f8d0ed"}], "guid": "bfdfe7dc352907fc980b868725387e98cac5758aaab041b306b3055c248885f4", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9834dfa23abd1ee3cbdf63f2e7e7c8692e", "guid": "bfdfe7dc352907fc980b868725387e98027d34dd1e1770c79dd54f5404987fd6"}], "guid": "bfdfe7dc352907fc980b868725387e98d8f70b5ff677ed02b77232961e037957", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98b7d49ad1fe66522e6eab9248dd2331d6", "targetReference": "bfdfe7dc352907fc980b868725387e98feffba4bc77d9f3d84a98c192cefdc8b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c02ad89c5bf95e8671a9b9e56eb80b15", "guid": "bfdfe7dc352907fc980b868725387e98ec5268a1792f6b5cd4753bb67be7b719"}], "guid": "bfdfe7dc352907fc980b868725387e98ba929dd1b11c60f9a4f8fd06e4eaa3e7", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e985f0ec3a68eeed5241cb87afb05bcc380", "name": "OrderedSet"}, {"guid": "bfdfe7dc352907fc980b868725387e98feffba4bc77d9f3d84a98c192cefdc8b", "name": "flutter_inappwebview_ios-flutter_inappwebview_ios_privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98a562549a031aeda8bf3440b79b3420bc", "name": "flutter_inappwebview_ios", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e9810acd6d3a97e7ef91b90dda5618dc5c0", "name": "flutter_inappwebview_ios.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}