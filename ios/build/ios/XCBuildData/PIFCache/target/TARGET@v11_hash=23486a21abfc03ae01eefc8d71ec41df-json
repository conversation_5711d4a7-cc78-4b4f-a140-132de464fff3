{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98ea9c40f2a9e5045fb34f6b5d9fcdeaa6", "buildSettings": {"BUILD_LIBRARY_FOR_DISTRIBUTION": "YES", "CLANG_ALLOW_NON_MODULAR_INCLUDES_IN_FRAMEWORK_MODULES": "YES", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PromisesObjC/PromisesObjC-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesObjC/PromisesObjC.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FBLPromises", "PRODUCT_NAME": "FBLPromises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e987c2545fc781ad168a9f51102fdcf007c", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e985b5d8c1be764b8751f485ba4f9b27c70", "buildSettings": {"BUILD_LIBRARY_FOR_DISTRIBUTION": "YES", "CLANG_ALLOW_NON_MODULAR_INCLUDES_IN_FRAMEWORK_MODULES": "YES", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PromisesObjC/PromisesObjC-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesObjC/PromisesObjC.modulemap", "PRODUCT_MODULE_NAME": "FBLPromises", "PRODUCT_NAME": "FBLPromises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98d25988d0f24747bcf7652936be5e04d6", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e985b5d8c1be764b8751f485ba4f9b27c70", "buildSettings": {"BUILD_LIBRARY_FOR_DISTRIBUTION": "YES", "CLANG_ALLOW_NON_MODULAR_INCLUDES_IN_FRAMEWORK_MODULES": "YES", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PromisesObjC/PromisesObjC-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesObjC/PromisesObjC.modulemap", "PRODUCT_MODULE_NAME": "FBLPromises", "PRODUCT_NAME": "FBLPromises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e987a3975cdd3c43e9a3052c2d13695c51e", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98ce56e5084d1044feb3c6d2ff6d849416", "guid": "bfdfe7dc352907fc980b868725387e98c09bf89968998b2efc3ccac29aedeea7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981cf2d15f3d38930a094ab126e6ce1307", "guid": "bfdfe7dc352907fc980b868725387e984501491298e337be1675a724ad0fe892", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98430cf2fd5d0e33ef6b8f51fbc9655825", "guid": "bfdfe7dc352907fc980b868725387e98667cc7179c20a6403455830513849a72", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987d79685cbe47cffebeb95234f7a513f4", "guid": "bfdfe7dc352907fc980b868725387e98b045f8ccf00ed263ecbfe718a676fd58", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c4d9971e3e0901ec273636741290952b", "guid": "bfdfe7dc352907fc980b868725387e98d5e66aa06bd3824628acff3b488cfde8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d989b29d3a642e498b1141ff2b4f48f6", "guid": "bfdfe7dc352907fc980b868725387e98bc811dcae87fcecb783f687d2771821c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982b3b0d71aaf2353016cfb9ad2d4cf02e", "guid": "bfdfe7dc352907fc980b868725387e98de93f7f7ccc9debe9451445922157698", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c681673b893ac55bfd5604889a3e9097", "guid": "bfdfe7dc352907fc980b868725387e9854b9d3290aca650eda748d26f2e9d614", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989b22fdedc5d9800f48bd4f48d1aba8c5", "guid": "bfdfe7dc352907fc980b868725387e98c64140118c57f25d889e8d71ba27df5e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98045d743f2e203ef14b55b57337c82521", "guid": "bfdfe7dc352907fc980b868725387e98a0e2c3313359a62efc1f215a0fa9ce50", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985fadfca674226765e20e97695c819e93", "guid": "bfdfe7dc352907fc980b868725387e988f027ed0874d54d94b19a2696358fb37", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980acce1f348153ee6b9b53ce289650268", "guid": "bfdfe7dc352907fc980b868725387e984a03b1d6d5076018db5940a64e72ae65", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9881140549d5f2396c979de8acf9184e0c", "guid": "bfdfe7dc352907fc980b868725387e98592c3bff8951336afe1de16ce5df3fc9", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982861679c237f894fb6177b65d9d61d38", "guid": "bfdfe7dc352907fc980b868725387e98a993035e09180dc6c324abd81aa7dd3f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982792fea13a75d579d37bc361baab0159", "guid": "bfdfe7dc352907fc980b868725387e98601e011402bbd0f88e72094234ca9c53", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984fc97b5455190a393b8b42b10c0ee1c3", "guid": "bfdfe7dc352907fc980b868725387e9881d2f65204108250b882cdb70ea6d3bc", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981f70cc93a58cee7e5af5c728c51cfede", "guid": "bfdfe7dc352907fc980b868725387e98c4d129f9615344cffa8b735039456a96", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983bab56b41308dca7bd2016ffabc4138e", "guid": "bfdfe7dc352907fc980b868725387e986159c85f38ce30e0f37d521accd7bf48", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9856948f03e29c110155a72c1cbee4c980", "guid": "bfdfe7dc352907fc980b868725387e98506f6b273fe9f80b68e1161d5d5790fb", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987d8d3c6764df50b761ade434d45d909e", "guid": "bfdfe7dc352907fc980b868725387e985151c374b7de283fc13772096b27245c", "headerVisibility": "private"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9886af16c8ad22a9794702febf686a491d", "guid": "bfdfe7dc352907fc980b868725387e9888b61fcb447646e3d1da7a8b84b39a14", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fbe71086b3680cc8f4418c64ccefddb0", "guid": "bfdfe7dc352907fc980b868725387e986e05d3a5532a99eb7088eb30c8a37898", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98a8816d750c3989d45343cb88a1acc750", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9828a7d22a89d9e322edb22388fb93c394", "guid": "bfdfe7dc352907fc980b868725387e98cee7693e805fd3f86e057085a21d2688"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98429bb13a93eb5ef7c6f95126d37819ce", "guid": "bfdfe7dc352907fc980b868725387e986b4fa6b99314fd683362b76a6daad264"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ea70980ddd5c9ee7af162a4be064c799", "guid": "bfdfe7dc352907fc980b868725387e9896cf1db66e54a3d34b85f2aa5507ece1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98028c02e6a2d869d729b13542342b3896", "guid": "bfdfe7dc352907fc980b868725387e9850cbda50ce4dc081a70076aaa90f2fed"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d6aedaf99fa6f2c3a86c01ac30c1b228", "guid": "bfdfe7dc352907fc980b868725387e9804e88fdebbac4f2aca2473a68c08cc5d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eb32e1d10319b17d7ddbf5cf6207972c", "guid": "bfdfe7dc352907fc980b868725387e9850e3ffa6689d50096da171abbc037a9d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eda575c5a9031ba4eb9281dc51523c53", "guid": "bfdfe7dc352907fc980b868725387e98545d3c561ea827e8e8029d5854949849"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98052246d4124774484eb6f233123bfc83", "guid": "bfdfe7dc352907fc980b868725387e98df517d79caa8f7d534d8136fae2914cb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bd062eda99859f91c01ced5e9e09f986", "guid": "bfdfe7dc352907fc980b868725387e980079677cfb932840139e6315ed921bb7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982c9620b83df7c563de56eaa8c2d317f5", "guid": "bfdfe7dc352907fc980b868725387e9846bb8ee6b25ebccd7fa3dc0c97fc7a84"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987a6a8ac83f2d270b1befea40a7ae9e4e", "guid": "bfdfe7dc352907fc980b868725387e98bc1bf2181c2688d48275ee498af58213"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980bce8322af7cb6976e2a5f6d02e6b6e5", "guid": "bfdfe7dc352907fc980b868725387e987e7b5dcc28dcc902730da1b4d094be9c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985dcdc2f9de8c48f3dc4015be68027276", "guid": "bfdfe7dc352907fc980b868725387e98a0274fe77e72df050681dc49a7288fde"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a8e973ba79aa8d108647fb613b2a8f34", "guid": "bfdfe7dc352907fc980b868725387e9842e2226a79532346a5ed2589da2c78c5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98440c5a4d2f8bbd2c4a25e64932c2ab31", "guid": "bfdfe7dc352907fc980b868725387e98aa4b694f853ec971e364f37194434192"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9809735941628b77dc0bf1626944ba2780", "guid": "bfdfe7dc352907fc980b868725387e9818c76ec9262f6a1accedb97baf6dd02c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e6d82a61b981ea78d19ea8c0634397e0", "guid": "bfdfe7dc352907fc980b868725387e9845f8cb1db4c6e7db9aa70cbe02f182bf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98226bb1dbed015fe25d9ffa332e3727af", "guid": "bfdfe7dc352907fc980b868725387e98c86dd74fce49b690fad111ea7d545547"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9863fe3b8e4816ca715fb4299d4268dc81", "guid": "bfdfe7dc352907fc980b868725387e987bbae3e9f3c16e36ab32c2f7a0cba4f1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98701e50c29a06995d067b1613071388a3", "guid": "bfdfe7dc352907fc980b868725387e985e4b09eb271439052705305d11094448"}], "guid": "bfdfe7dc352907fc980b868725387e9868ce83b9351e3001ffab816ff6760713", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9834dfa23abd1ee3cbdf63f2e7e7c8692e", "guid": "bfdfe7dc352907fc980b868725387e98d2077b7cfe9b8045caaeeca1b33746b2"}], "guid": "bfdfe7dc352907fc980b868725387e984bf9dd2a6edcc6ced38b7c1e97460b02", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98c1b3e3b0873bf43a7d7159bf7611f6eb", "targetReference": "bfdfe7dc352907fc980b868725387e98ad53226b339581a6725de188f2c8f823"}], "guid": "bfdfe7dc352907fc980b868725387e98c3ce61de2c553e8713f4e9c805b6c475", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98ad53226b339581a6725de188f2c8f823", "name": "PromisesObjC-FBLPromises_Privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98f10882e1684b8a3dfdec597bc0a47af3", "name": "PromisesObjC", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e981c795e45f8d875aac88217c6a2a95faa", "name": "FBLPromises.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}