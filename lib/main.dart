import 'dart:io';

import 'package:easy_localization/easy_localization.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/material.dart';

import 'firebase_options.dart';
import 'one_drop.dart';
import 'services/service_locator.dart';

Future<void> _initializeFirebase() async {
  // Check if Firebase is already initialized to avoid duplicate app error
  try {
    await Firebase.initializeApp(options: DefaultFirebaseOptions.currentPlatform);
  } catch (e) {
    if (e.toString().contains('duplicate-app')) {
      print('Firebase already initialized, skipping...');
    } else {
      print('Firebase initialization error: $e');
      rethrow;
    }
  }

  final messaging = FirebaseMessaging.instance;

  if (Platform.isIOS) {
    // Request provisional authorization and configure notifications for iOS
    await messaging.requestPermission(
      alert: true,
      badge: true,
      sound: true,
      provisional: true,
    );

    await messaging.setForegroundNotificationPresentationOptions(
      alert: true,
      badge: true,
      sound: true,
    );

    // Initialize APNS token
    try {
      final apnsToken = await messaging.getAPNSToken();
      print('APNS Token: $apnsToken');
    } catch (e) {
      print('Error getting APNS token: $e');
    }
  }
}

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  await Future.wait([
    _initializeFirebase(),
    ServiceLocator.setupLocators(),
    EasyLocalization.ensureInitialized(),
  ]);
  runApp(
    EasyLocalization(
      supportedLocales: const [Locale('en', 'US'), Locale('ar', 'AE')],
      path: 'assets/translations',
      startLocale: const Locale('ar', 'AE'),
      useOnlyLangCode: true,
      fallbackLocale: const Locale('en', 'US'),
      child: OneDrop(),
    ),
  );
}

// Flutter: Channel stable, 3.7.1
