import 'dart:async';

import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:one_drop/models/order_model.dart';
import 'package:one_drop/providers/user_provider.dart';
import 'package:one_drop/routes.dart';
import 'package:one_drop/services/service_locator.dart';
import 'package:provider/provider.dart';

import '../widgets/home_tab.dart';
import '../widgets/my_account_tab.dart';
import '../widgets/my_orders_tab.dart';
import '../widgets/od_bottom_nav_bar.dart';
import '../widgets/store_tab.dart';
import '../widgets/targets_tab.dart';
import 'notifications_screen.dart';
import 'order_details_screen.dart';

class MainTabController extends StatefulWidget {
  static const routeName = 'home-tab-controller';

  final int initialTab;

  const MainTabController({
    Key? key,
    this.initialTab = 0,
  }) : super(key: key);

  @override
  State<MainTabController> createState() => _MainTabControllerState();
}

class _MainTabControllerState extends State<MainTabController> {
  StreamSubscription<RemoteMessage>? _notificationsSubscription;

  @override
  void initState() {
    super.initState();

    FirebaseMessaging.instance.requestPermission(
      alert: true,
      badge: true,
      sound: true,
      announcement: false,
      carPlay: false,
      criticalAlert: false,
      provisional: false,
    );
    // FirebaseMessaging.instance.getToken().then((value) {
    //   print('---- notification token');
    //   print('--- $value');
    // });
    // FirebaseMessaging.onMessage.listen((event) {
    //   print('--- onMessage');
    //   print(event);
    // });
    _notificationsSubscription = FirebaseMessaging.onMessageOpenedApp.listen((event) {
      try {
        final orderModel = OrderModel.fromMap(event.data);
        sl.get<GlobalKey<NavigatorState>>().currentState?.push(
              CupertinoPageRoute(
                builder: (cxt) => OrderDetailsScreen(order: orderModel),
                settings: const RouteSettings(name: OrderDetailsScreen.routeName),
              ),
            );
      } catch (e) {
        sl.get<GlobalKey<NavigatorState>>().currentState?.pushNamed(NotificationsScreen.routeName);
      }
    });
    Future.delayed(Duration.zero, _initState);
  }

  @override
  void dispose() {
    _notificationsSubscription?.cancel();
    super.dispose();
  }

  void _initState() {
    final userProvider = context.read<UserProvider>();
    final nextRoute = userProvider.nextRoute;
    // final nextRouteParam = userProvider.nextRouteParam;
    if (nextRoute == null || Routes.table[nextRoute] == null) return;
    context.read<UserProvider>().nextRoute = null;
    userProvider.nextRouteParam = null;
    Navigator.pushNamed(context, nextRoute);
    // if (nextRouteParam == null) {
    // }
    // else {
    //   Navigator.push(context, nextRoute);
    // }
  }

  @override
  Widget build(BuildContext context) {
    final tabs = <Widget>[
      const HomeTab(),
      const MyOrdersTab(),
      const StoreTab(),
      const TargetsTab(),
      const MyAccountTab(),
    ];
    return DefaultTabController(
      length: tabs.length,
      initialIndex: widget.initialTab,
      child: Scaffold(
        body: AnnotatedRegion(
          value: SystemUiOverlayStyle.dark,
          child: TabBarView(
            children: tabs,
            physics: const NeverScrollableScrollPhysics(),
          ),
        ),
        bottomNavigationBar: const ODBottomNavBar(),
      ),
    );
  }
}
