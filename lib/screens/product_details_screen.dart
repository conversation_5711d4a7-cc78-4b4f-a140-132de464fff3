import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_carousel_widget/flutter_carousel_widget.dart';
import 'package:smooth_page_indicator/smooth_page_indicator.dart';

import 'package:one_drop/models/product.dart';
import 'package:one_drop/screens/cart_screen.dart';
import 'package:one_drop/services/cart_service.dart';
import 'package:one_drop/services/store_service.dart';
import 'package:one_drop/styles.dart';
import 'package:one_drop/widgets/common_app_bar.dart';

class ProductDetailsScreen extends StatefulWidget {
  static const routeName = '/product-details';
  final int productId;

  const ProductDetailsScreen({
    Key? key,
    required this.productId,
  }) : super(key: key);

  @override
  State<ProductDetailsScreen> createState() => _ProductDetailsScreenState();
}

class _ProductDetailsScreenState extends State<ProductDetailsScreen>
    with SingleTickerProviderStateMixin {
  final _storeService = StoreService();
  bool _isLoading = true;
  String? _error;
  Product? _product;
  int _quantity = 1;
  bool _addingToCart = false;
  final FlutterCarouselController _carouselController =
      FlutterCarouselController();
  int _currentImageIndex = 0;
  late AnimationController _animationController;
  bool _isDescriptionExpanded = false;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 300),
    );
    _loadProduct();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  Future<void> _loadProduct() async {
    try {
      setState(() {
        _isLoading = true;
        _error = null;
      });

      final product = await _storeService.getProductDetails(widget.productId);
      setState(() {
        _product = product;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _error = e.toString();
        _isLoading = false;
      });
    }
  }

  Widget _buildImageCarousel() {
    return Stack(
      children: [
        FlutterCarousel(
          options: FlutterCarouselOptions(
            height: MediaQuery.of(context).size.width,
            showIndicator: false,
            viewportFraction: 1.0,
            onPageChanged: (index, reason) {
              setState(() => _currentImageIndex = index);
            },
          ),
          items: [
            _product!.image,
            ...List.generate(3, (index) => _product!.image)
          ].map((imageUrl) {
            return Hero(
              tag: 'product-${_product!.id}',
              child: Image.network(
                imageUrl,
                fit: BoxFit.cover,
                errorBuilder: (context, error, stackTrace) {
                  return Container(
                    color: Colors.grey[200],
                    child: const Icon(Icons.image_not_supported,
                        color: Colors.grey, size: 48),
                  );
                },
              ),
            );
          }).toList(),
        ),
        Positioned(
          bottom: 16,
          left: 0,
          right: 0,
          child: Center(
            child: AnimatedSmoothIndicator(
              activeIndex: _currentImageIndex,
              count: 4,
              effect: ExpandingDotsEffect(
                dotHeight: 8,
                dotWidth: 8,
                activeDotColor: Styles.primaryColor,
                dotColor: Colors.white.withOpacity(0.5),
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildProductInfo() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: const BorderRadius.vertical(top: Radius.circular(24)),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, -5),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      _product!.name,
                      style: Theme.of(context).textTheme.titleLarge?.copyWith(
                            color: Styles.textPrimaryColor,
                            fontWeight: FontWeight.w600,
                          ),
                    ),
                    const SizedBox(height: 4),
                    Row(
                      children: [
                        const Icon(Icons.star, color: Colors.amber, size: 20),
                        Text(
                          ' 4.5 ',
                          style:
                              Theme.of(context).textTheme.bodyLarge?.copyWith(
                                    color: Styles.textPrimaryColor,
                                    fontWeight: FontWeight.w500,
                                  ),
                        ),
                        Text(
                          '(128 reviews)',
                          style:
                              Theme.of(context).textTheme.bodyMedium?.copyWith(
                                    color: Styles.textSecondaryColor,
                                  ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
              Container(
                padding:
                    const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                decoration: BoxDecoration(
                  color: Styles.primaryColor,
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Text(
                  '${_product!.price.toStringAsFixed(2)} ${tr('saudi_riyal')}',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                      ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 24),
          _buildSpecifications(),
          const SizedBox(height: 24),
          _buildDescription(),
          if (_product!.quantity > 0) ...[
            const SizedBox(height: 24),
            _buildQuantitySelector(),
          ],
        ],
      ),
    );
  }

  Widget _buildSpecifications() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'specifications'.tr(),
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                color: Styles.textPrimaryColor,
                fontWeight: FontWeight.w600,
              ),
        ),
        const SizedBox(height: 16),
        Row(
          children: [
            _specificationItem(
                Icons.inventory_2_outlined, 'SKU', _product!.id.toString()),
            _specificationItem(
                Icons.branding_watermark, 'brand'.tr(), _product!.brand.name),
            _specificationItem(
                Icons.category_outlined, 'category'.tr(), 'Electronics'),
          ],
        ),
      ],
    );
  }

  Widget _specificationItem(IconData icon, String title, String value) {
    return Expanded(
      child: Column(
        children: [
          Icon(icon, color: Styles.primaryColor),
          const SizedBox(height: 8),
          Text(
            title,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Styles.textSecondaryColor,
                ),
          ),
          const SizedBox(height: 4),
          Text(
            value,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Styles.textPrimaryColor,
                  fontWeight: FontWeight.w600,
                ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildDescription() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        InkWell(
          onTap: () {
            setState(() => _isDescriptionExpanded = !_isDescriptionExpanded);
            if (_isDescriptionExpanded) {
              _animationController.forward();
            } else {
              _animationController.reverse();
            }
          },
          child: Row(
            children: [
              Text(
                'description'.tr(),
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      color: Styles.textPrimaryColor,
                      fontWeight: FontWeight.w600,
                    ),
              ),
              const Spacer(),
              RotationTransition(
                turns:
                    Tween(begin: 0.0, end: 0.5).animate(_animationController),
                child: const Icon(Icons.keyboard_arrow_down),
              ),
            ],
          ),
        ),
        AnimatedCrossFade(
          firstChild: const SizedBox.shrink(),
          secondChild: Padding(
            padding: const EdgeInsets.only(top: 16),
            child: Text(
              _product!.description,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Styles.textSecondaryColor,
                  ),
            ),
          ),
          crossFadeState: _isDescriptionExpanded
              ? CrossFadeState.showSecond
              : CrossFadeState.showFirst,
          duration: const Duration(milliseconds: 300),
        ),
      ],
    );
  }

  Widget _buildQuantitySelector() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey[100],
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        children: [
          Text(
            'quantity'.tr(),
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  color: Styles.textPrimaryColor,
                  fontWeight: FontWeight.w600,
                ),
          ),
          const Spacer(),
          Container(
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(8),
            ),
            child: Row(
              children: [
                _quantityButton(
                  icon: Icons.remove,
                  onPressed:
                      _quantity > 1 ? () => setState(() => _quantity--) : null,
                ),
                Container(
                  width: 40,
                  alignment: Alignment.center,
                  child: Text(
                    _quantity.toString(),
                    style: Theme.of(context).textTheme.titleMedium,
                  ),
                ),
                _quantityButton(
                  icon: Icons.add,
                  onPressed: _quantity < _product!.quantity
                      ? () => setState(() => _quantity++)
                      : null,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _quantityButton({required IconData icon, VoidCallback? onPressed}) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: onPressed,
        borderRadius: BorderRadius.circular(8),
        child: Padding(
          padding: const EdgeInsets.all(8),
          child: Icon(icon,
              size: 20,
              color: onPressed == null ? Colors.grey : Styles.primaryColor),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      extendBodyBehindAppBar: true,
      appBar: CommonAppBar(
        title: '',
        showCartIcon: true,
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _error != null
              ? Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(
                        'error_loading_product'.tr(),
                        style: Theme.of(context).textTheme.titleMedium,
                      ),
                      const SizedBox(height: 8),
                      Text(
                        _error!,
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                              color: Styles.errorColor,
                            ),
                      ),
                      const SizedBox(height: 16),
                      ElevatedButton(
                        onPressed: _loadProduct,
                        child: Text('retry'.tr()),
                      ),
                    ],
                  ),
                )
              : SingleChildScrollView(
                  child: Column(
                    children: [
                      _buildImageCarousel(),
                      _buildProductInfo(),
                    ],
                  ),
                ),
      bottomNavigationBar: _product != null
          ? Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.white,
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.05),
                    blurRadius: 10,
                    offset: const Offset(0, -5),
                  ),
                ],
              ),
              child: SafeArea(
                child: Row(
                  children: [
                    Expanded(
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'total_price'.tr(),
                            style: Theme.of(context)
                                .textTheme
                                .bodyMedium
                                ?.copyWith(
                                  color: Styles.textSecondaryColor,
                                ),
                          ),
                          Text(
                            '\$${(_product!.price * _quantity).toStringAsFixed(2)} ${tr('saudi_riyal')}',
                            style: Theme.of(context)
                                .textTheme
                                .titleLarge
                                ?.copyWith(
                                  color: Styles.primaryColor,
                                  fontWeight: FontWeight.w600,
                                ),
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: _addingToCart
                          ? ElevatedButton(
                              onPressed: null,
                              style: ElevatedButton.styleFrom(
                                padding:
                                    const EdgeInsets.symmetric(vertical: 12),
                              ),
                              child: const SizedBox(
                                width: 20,
                                height: 20,
                                child: CircularProgressIndicator(
                                  strokeWidth: 2,
                                  valueColor: AlwaysStoppedAnimation<Color>(
                                      Colors.white),
                                ),
                              ),
                            )
                          : ElevatedButton(
                              onPressed: _product!.quantity > 0
                                  ? () async {
                                      setState(() {
                                        _addingToCart = true;
                                      });
                                      try {
                                        await CartService().addItem(
                                            _product!.id,
                                            quantity: _quantity);
                                        if (!mounted) return;
                                        ScaffoldMessenger.of(context)
                                            .showSnackBar(
                                          SnackBar(
                                            content: Text('${_product!.name} ' +
                                                'added_to_cart'.tr()),
                                            action: SnackBarAction(
                                              label: 'view_cart'.tr(),
                                              onPressed: () {
                                                Navigator.push(
                                                  context,
                                                  MaterialPageRoute(
                                                    builder: (context) =>
                                                        const CartScreen(),
                                                  ),
                                                );
                                              },
                                            ),
                                          ),
                                        );
                                      } catch (e) {
                                        if (!mounted) return;
                                        ScaffoldMessenger.of(context)
                                            .showSnackBar(
                                          SnackBar(
                                            content: Text(
                                                'error_adding_to_cart'.tr() +
                                                    ': ${e.toString()}'),
                                            backgroundColor: Colors.red,
                                          ),
                                        );
                                      } finally {
                                        setState(() {
                                          _addingToCart = false;
                                        });
                                      }
                                    }
                                  : null,
                              style: ElevatedButton.styleFrom(
                                padding:
                                    const EdgeInsets.symmetric(vertical: 12),
                              ),
                              child: Text(
                                _product!.quantity > 0
                                    ? 'add_to_cart'.tr()
                                    : 'out_of_stock'.tr(),
                              ),
                            ),
                    ),
                  ],
                ),
              ),
            )
          : null,
    );
  }
}
