import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:one_drop/styles.dart';

class LanguageScreen extends StatelessWidget {
  static const routeName = 'language-screen';

  const LanguageScreen({Key? key}) : super(key: key);

  void _selectLang({
    required BuildContext context,
    required String locale,
  }) {
    if (locale == 'ar') {
      context.setLocale(const Locale('ar', 'AE'));
    } else {
      context.setLocale(const Locale('en', 'US'));
    }
  }

  @override
  Widget build(BuildContext context) {
    final currentLang = EasyLocalization.of(context)?.locale.languageCode;
    return Scaffold(
      appBar: AppBar(
        title: Text(tr('select_language')),
        centerTitle: true,
      ),
      body: Safe<PERSON>rea(
        child: Column(
          children: [
            ListTile(
              title: const Text('العربية'),
              trailing: currentLang == 'ar'
                  ? const Icon(
                      Icons.check_circle,
                      color: Styles.primaryColor,
                    )
                  : null,
              onTap: () => _selectLang(context: context, locale: 'ar'),
            ),
            ListTile(
              title: const Text('English'),
              trailing: currentLang == 'en'
                  ? const Icon(
                      Icons.check_circle,
                      color: Styles.primaryColor,
                    )
                  : null,
              onTap: () => _selectLang(context: context, locale: 'en'),
            ),
          ],
        ),
      ),
    );
  }
}
