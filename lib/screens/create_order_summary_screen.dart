import 'package:bot_toast/bot_toast.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import '../widgets/sign_in_for_order_dialog.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:one_drop/api/user_api.dart';
import 'package:one_drop/helpers.dart';
import 'package:one_drop/providers/user_provider.dart';
import 'package:one_drop/styles.dart';
import 'package:provider/provider.dart';

import '../constants.dart';
import '../models/package_item_model.dart';
import '../providers/create_order_provider.dart';
import '../widgets/order_coupon_field.dart';
import 'payment_method_screen.dart';
import 'package:one_drop/screens/sign_up_phone_number_screen.dart';

class CreateOrderSummaryScreen extends StatefulWidget {
  static const routeName = 'create-order-summary';

  const CreateOrderSummaryScreen({Key? key}) : super(key: key);

  @override
  State<CreateOrderSummaryScreen> createState() =>
      _CreateOrderSummaryScreenState();
}

class _CreateOrderSummaryScreenState extends State<CreateOrderSummaryScreen> {
  @override
  void initState() {
    super.initState();

    Future.delayed(Duration.zero, () {
      context.read<CreateOrderProvider>().coupon = null;
    });
  }

  void _onCouponApplyPressed(String coupon) async {
    final selectedCarType = context.read<CreateOrderProvider>().selectedCarType;
    final price = selectedCarType?.price ?? 0;
    BotToast.showLoading();
    final couponModel = await UserAPI().checkCoupon(coupon: coupon);
    BotToast.closeAllLoading();
    if (couponModel == null || !couponModel.isActive) {
      Helpers.showNotification(tr('invalid_coupon_code'));
    } else if (couponModel.isExpired ||
        couponModel.usageTimesRemaining == 0 ||
        couponModel.currentUserUsageTimesRmaining == 0) {
      Helpers.showNotification(tr('expired_coupon'));
    } else if (couponModel.maxCouponAmount <= price ||
        couponModel.minOrderAmount >= price) {
      Helpers.showNotification(tr('coupon_not_applicable_to_this_order'));
    } else {
      context.read<CreateOrderProvider>().coupon = couponModel;
    }
  }

  void _proceedPressed() async {
    final isAuth = context.read<UserProvider>().isAuth;
    if (!isAuth) {
      final shouldSignIn = await showDialog<bool>(
        context: context,
        builder: (context) => const SignInForOrderDialog(),
      );
      if (shouldSignIn == true) {
        Navigator.pushNamed(context, SignUpPhoneNumberScreen.routeName);
      }
      return;
    }

    final createOrderProvider = context.read<CreateOrderProvider>();
    final hasFreeWash = createOrderProvider.hasFreeWash;
    if (hasFreeWash) {
      final user = context.read<UserProvider>().userModel;
      PaymentMethodScreen.createOrder(
        includeCart: false,
        context: context,
        paymentMethod: K.paymentTypeTarget,
        transactionID: '${user?.id}-${DateTime.now().millisecondsSinceEpoch}',
      );
    } else {
      BotToast.showLoading();
      final userPackages = await UserAPI().getUserPackages();
      BotToast.closeAllLoading();
      final createOrderProvider = context.read<CreateOrderProvider>();
      final selectedCarType = createOrderProvider.selectedCarType;
      final selectedOrderCategory = createOrderProvider.selectedOrderCategory;
      PackageItemModel? paymentPackage;
      try {
        paymentPackage = userPackages.firstWhere((element) {
          return element.isSubscribed &&
              element.category?.id == selectedOrderCategory?.id &&
              element.car?.id == selectedCarType?.id &&
              element.remainingWashes > 0;
        });
      } catch (_) {}
      Navigator.push(
        context,
        CupertinoPageRoute(
          builder: (cxt) => PaymentMethodScreen(
            paymentAmount:
                createOrderProvider.orderTotalPrice, // this is the problem
            onPaymentSuccess: null,
            paymentDescription: createOrderProvider.selectedOrderCategory?.name,
            paymentPackage: paymentPackage,
            paymentMethods: [
              K.paymentMethodApplePay,
              if (paymentPackage != null) K.paymentMethodPackage,
              K.paymentMethodWallet,
              K.paymentMethodCard,
              K.paymentMethodCash,
            ],
          ),
          settings: const RouteSettings(name: PaymentMethodScreen.routeName),
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final createOrderProvider = context.watch<CreateOrderProvider>();
    final selectedAddress = createOrderProvider.selectedAddress;
    final selectedCarType = createOrderProvider.selectedCarType;
    final selectedOrderCategory = createOrderProvider.selectedOrderCategory;
    // احسب المجموع الإجمالي مباشرة
    final carPrice = selectedCarType?.price ?? 0;
    final discountAmount = createOrderProvider.coupon?.discount ?? 0;
    final discountPercentage = createOrderProvider.coupon?.percentage ?? 0;

// حساب المجموع مباشرة
    double calculatedTotal;
    if (discountPercentage > 0) {
      final amount = carPrice * (discountPercentage / 100);
      calculatedTotal = carPrice - amount;
    } else if (discountAmount > 0) {
      calculatedTotal = carPrice - discountAmount;
    } else {
      calculatedTotal = carPrice;
    }

// استخدم قيمة صفر إذا كانت النتيجة سالبة أو غير صالحة
    calculatedTotal = calculatedTotal > 0 ? calculatedTotal : 0;
    final total = createOrderProvider.orderTotalPrice;
    final hasFreeWash = createOrderProvider.hasFreeWash;
    const xPadding = EdgeInsets.symmetric(horizontal: 20);
    return Scaffold(
      appBar: AppBar(
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => Navigator.pop(context),
        ),
      ),
      body: SafeArea(
        bottom: false,
        child: selectedOrderCategory == null ||
                selectedCarType == null ||
                selectedAddress == null
            ? Center(child: Text(tr('error')))
            : Column(
                children: [
                  Expanded(
                    child: SingleChildScrollView(
                      child: Column(
                        children: [
                          Padding(
                            padding: xPadding,
                            child: Stack(
                              children: [
                                Container(
                                  decoration: BoxDecoration(
                                    color: Styles.lightGrey,
                                    borderRadius: BorderRadius.circular(10),
                                  ),
                                  padding: const EdgeInsets.symmetric(
                                      horizontal: 20, vertical: 20),
                                  child: Column(
                                    children: [
                                      Row(
                                        children: [
                                          SizedBox(
                                            width: 60,
                                            height: 60,
                                            child: kReleaseMode
                                                ? CachedNetworkImage(
                                                    imageUrl:
                                                        selectedOrderCategory
                                                            .image,
                                                    placeholder: (_, __) =>
                                                        kImagePlaceholder,
                                                    errorWidget: (_, __, ___) =>
                                                        kImagePlaceholder,
                                                  )
                                                : SvgPicture.asset(
                                                    'assets/images/29.svg',
                                                  ),
                                          ),
                                          const SizedBox(width: 20),
                                          Expanded(
                                            child: Column(
                                              crossAxisAlignment:
                                                  CrossAxisAlignment.start,
                                              children: [
                                                Text(
                                                  selectedOrderCategory.name,
                                                  style: Theme.of(context)
                                                      .textTheme
                                                      .labelLarge,
                                                ),
                                                const SizedBox(height: 10),
                                                Text(
                                                  selectedOrderCategory.desc,
                                                  style: Theme.of(context)
                                                      .textTheme
                                                      .labelMedium,
                                                ),
                                              ],
                                            ),
                                          ),
                                        ],
                                      ),
                                      const Divider(height: 40),
                                      const SizedBox(height: 10),
                                      Row(
                                        children: [
                                          SizedBox(
                                            width: 30,
                                            height: 30,
                                            child: kReleaseMode
                                                ? CachedNetworkImage(
                                                    imageUrl:
                                                        selectedCarType.image,
                                                    placeholder: (_, __) =>
                                                        kImagePlaceholder,
                                                    errorWidget: (_, __, ___) =>
                                                        kImagePlaceholder,
                                                  )
                                                : SvgPicture.asset(
                                                    'assets/images/29.svg',
                                                  ),
                                          ),
                                          const SizedBox(width: 20),
                                          Expanded(
                                            child: Column(
                                              crossAxisAlignment:
                                                  CrossAxisAlignment.start,
                                              children: [
                                                Text(
                                                  '${tr('car_type')}: ${selectedCarType.name}',
                                                  style: Theme.of(context)
                                                      .textTheme
                                                      .labelLarge,
                                                ),
                                                const SizedBox(height: 10),
                                                Text(
                                                  selectedCarType.desc ?? '',
                                                  style: Theme.of(context)
                                                      .textTheme
                                                      .labelMedium
                                                      ?.copyWith(
                                                          color: Colors.grey),
                                                ),
                                              ],
                                            ),
                                          ),
                                        ],
                                      ),
                                      const SizedBox(height: 20),
                                      Row(
                                        children: [
                                          SizedBox(
                                            width: 30,
                                            height: 30,
                                            child: SvgPicture.asset(
                                                'assets/images/29.svg'),
                                          ),
                                          const SizedBox(width: 20),
                                          Expanded(
                                            child: Column(
                                              crossAxisAlignment:
                                                  CrossAxisAlignment.start,
                                              children: [
                                                Text(
                                                  selectedAddress.name,
                                                  style: Theme.of(context)
                                                      .textTheme
                                                      .labelLarge,
                                                ),
                                                const SizedBox(height: 10),
                                                Text(
                                                  selectedAddress.address,
                                                  style: Theme.of(context)
                                                      .textTheme
                                                      .labelMedium
                                                      ?.copyWith(
                                                          color: Colors.grey),
                                                ),
                                              ],
                                            ),
                                          ),
                                        ],
                                      ),
                                    ],
                                  ),
                                ),
                                Positioned(
                                  right: -20,
                                  top: 80,
                                  child: Container(
                                    width: 40,
                                    height: 40,
                                    decoration: const BoxDecoration(
                                      color: Colors.white,
                                      shape: BoxShape.circle,
                                    ),
                                  ),
                                ),
                                Positioned(
                                  left: -20,
                                  top: 80,
                                  child: Container(
                                    width: 40,
                                    height: 40,
                                    decoration: const BoxDecoration(
                                      color: Colors.white,
                                      shape: BoxShape.circle,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                          const SizedBox(height: 30),
                          if (!hasFreeWash)
                            Padding(
                              padding: xPadding,
                              child: Row(
                                children: [
                                  Text(tr('do_you_have_discount_coupon'))
                                ],
                              ),
                            ),
                          if (!hasFreeWash) const SizedBox(height: 10),
                          if (!hasFreeWash)
                            Padding(
                              padding: xPadding,
                              child: OrderCouponField(
                                onApplyPressed: _onCouponApplyPressed,
                              ),
                            ),
                          const SizedBox(height: 30),
                          Padding(
                            padding: xPadding,
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Text(tr('sub_total')),
                                Text(
                                    '${selectedCarType.price.toInt()} ${tr('sr')}'),
                              ],
                            ),
                          ),
                          const SizedBox(height: 10),
                          Padding(
                            padding: xPadding,
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Text(tr('discount')),
                                if (discountAmount > 0)
                                  Text(hasFreeWash
                                      ? '${selectedCarType.price.toInt()} ${tr('sr')}'
                                      : '$discountAmount ${tr('sr')}')
                                else if (discountPercentage > 0)
                                  Text(hasFreeWash
                                      ? '${selectedCarType.price.toInt()} ${tr('sr')}'
                                      : '$discountPercentage %')
                                else
                                  Text(hasFreeWash
                                      ? '${selectedCarType.price.toInt()} ${tr('sr')}'
                                      : '$discountAmount ${tr('sr')}'),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                  // const Spacer(),
                  Container(
                    width: double.infinity,
                    height: 150,
                    padding: const EdgeInsets.symmetric(
                        horizontal: 20, vertical: 20),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      boxShadow: [kBoxShadow],
                      borderRadius: const BorderRadius.only(
                        topLeft: Radius.circular(10),
                        topRight: Radius.circular(10),
                      ),
                    ),
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Column(
                          children: [
                            Text(tr('overall_total')),
                            const SizedBox(height: 10),
                            Row(
                              children: [
                                Text(
                                  hasFreeWash
                                      ? '0.0 '
                                      : '${calculatedTotal.toInt()} ',
                                  style: Theme.of(context)
                                      .textTheme
                                      .titleLarge
                                      ?.copyWith(color: Styles.primaryColor),
                                ),
                                Text(tr('sr')),
                              ],
                            ),
                          ],
                        ),
                        SizedBox(
                          width: 200,
                          child: ElevatedButton(
                            onPressed: _proceedPressed,
                            child: Text(hasFreeWash
                                ? tr('proceed')
                                : tr('proceed_to_payment')),
                            style: ButtonStyle(
                              backgroundColor: MaterialStateProperty.all(
                                  Styles.secondaryColor),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
      ),
    );
  }
}
