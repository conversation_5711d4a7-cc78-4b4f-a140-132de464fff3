import 'dart:async';
import 'dart:io';

import 'package:bot_toast/bot_toast.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:google_maps_flutter_android/google_maps_flutter_android.dart';
import 'package:google_maps_flutter_platform_interface/google_maps_flutter_platform_interface.dart';
import 'package:location/location.dart';
import 'package:one_drop/helpers.dart';
import 'package:one_drop/providers/create_order_provider.dart';
import 'package:one_drop/providers/user_provider.dart';
import 'package:one_drop/styles.dart';
import 'package:provider/provider.dart';

import '../api/google_api.dart';
import '../api/user_api.dart';
import '../models/user_address_model.dart';
import '../widgets/location_permission_dialog.dart';
import 'create_order_details_screen.dart';
import 'select_location_screen.dart';
import '../widgets/common_app_bar.dart';

class CreateOrderSelectLocationMapScreen extends StatefulWidget {
  static const routeName = 'create-order-select-location';

  const CreateOrderSelectLocationMapScreen({
    this.popOnConformPressed = false,
    Key? key,
  }) : super(key: key);

  final bool popOnConformPressed;

  @override
  State<CreateOrderSelectLocationMapScreen> createState() =>
      _CreateOrderSelectLocationMapScreenState();
}

const kInitialMapPosition = CameraPosition(
  target: LatLng(24.755004360484044, 46.67672831565142),
  zoom: 12,
);

class _CreateOrderSelectLocationMapScreenState
    extends State<CreateOrderSelectLocationMapScreen> {
  final _controllerCompleter = Completer<GoogleMapController>();
  var _loading = false;
  late GoogleMapController? _mapController;
  bool _isMapInitialized = false;

  @override
  void initState() {
    super.initState();
    if (!_isMapInitialized) {
      _initializeMap();
      _isMapInitialized = true;
    }
  }

  @override
  void dispose() {
    if (_mapController != null) {
      _mapController!.dispose();
    }
    super.dispose();
  }

  Future<void> _initializeMap() async {
    if (Platform.isAndroid) {
      final googleMapsFlutterAndroid = GoogleMapsFlutterAndroid();
      try {
        await googleMapsFlutterAndroid
            .initializeWithRenderer(AndroidMapRenderer.latest);
      } catch (e) {
        debugPrint('Map renderer already initialized: $e');
      }
    }
    _initLocation();
  }

  Future<void> _initLocation() async {
    final location = Location();
    BotToast.showLoading();

    try {
      bool serviceEnabled = await location.serviceEnabled();
      if (!serviceEnabled) {
        serviceEnabled = await location.requestService();
        if (!serviceEnabled) {
          BotToast.closeAllLoading();
          return;
        }
      }

      PermissionStatus permissionGranted = await location.hasPermission();
      if (permissionGranted == PermissionStatus.denied) {
        BotToast.closeAllLoading();
        if (!mounted) return;

        final getPermission = await showDialog<bool>(
          context: context,
          builder: (cxt) => const LocationPermissionDialog(),
        );

        if (!(getPermission ?? false)) return;

        BotToast.showLoading();
        permissionGranted = await location.requestPermission();
        if (permissionGranted != PermissionStatus.granted) {
          BotToast.closeAllLoading();
          return;
        }
      }

      final locationData = await location.getLocation();
      BotToast.closeAllLoading();

      if (locationData.latitude != null && locationData.longitude != null) {
        _onMapPressed(LatLng(locationData.latitude!, locationData.longitude!));
      }
    } catch (e) {
      debugPrint('Location initialization error: $e');
      BotToast.closeAllLoading();
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text(tr('location_error'))),
        );
      }
    }
  }

  void _onSelectLocationPressed() async {
    final UserAddressModel? selectedAddress =
        await Navigator.pushNamed<dynamic>(
            context, SelectLocationScreen.routeName);
    if (selectedAddress == null) return;
    context.read<CreateOrderProvider>().selectedAddress = selectedAddress;
    final mapController = await _controllerCompleter.future;
    // CameraUpdate.newCameraPosition(CameraPosition(target: target))
    mapController.animateCamera(CameraUpdate.newLatLngZoom(
      LatLng(
          double.parse(selectedAddress.lat), double.parse(selectedAddress.lng)),
      16,
    ));
    setState(() {});
    final results = await GoogleAPI().geocode(
      lat: double.tryParse(selectedAddress.lat),
      lng: double.tryParse(selectedAddress.lng),
    );
    if (results?.isNotEmpty ?? false) {
      if (!mounted) return;
      context.read<CreateOrderProvider>().cityOrNeighborhoodName =
          results?.first.cityOrNeighborhoodName ?? '';
    }
  }

  void _onMapPressed(LatLng locaiton) async {
    // print('--- _onMapPressed');
    final mapController = await _controllerCompleter.future;
    mapController.animateCamera(CameraUpdate.newLatLngZoom(locaiton, 16));
    if (!mounted) return;
    setState(() {
      _loading = true;
    });
    final results = await GoogleAPI().geocode(
      lat: locaiton.latitude,
      lng: locaiton.longitude,
    );
    if (results?.isEmpty ?? true) {
      if (!mounted) return;
      setState(() {
        _loading = false;
      });
      return Helpers.showErrorNotification(
          tr('could_not_find_your_selected_address'));
    }
    if (!mounted) return;
    final googleAddress = results!.first;
    final isAuth = context.read<UserProvider>().isAuth;
    if (isAuth) {
      final userAddress = await UserAPI().addAddress(
        lat: googleAddress.lat,
        lng: googleAddress.lng,
        name: googleAddress.name,
        address: googleAddress.formattedAddress,
        city: googleAddress.cityOrNeighborhoodName ?? '',
      );
      context.read<CreateOrderProvider>().selectedAddress = userAddress;
    } else {
      context.read<CreateOrderProvider>().selectedAddress = UserAddressModel(
        id: null,
        name: googleAddress.name,
        address: googleAddress.formattedAddress,
        lat: googleAddress.lat.toString(),
        lng: googleAddress.lng.toString(),
      );
    }
    context.read<CreateOrderProvider>().cityOrNeighborhoodName =
        googleAddress.cityOrNeighborhoodName ?? '';
    _loading = false;
    setState(() {});
  }

  void _onConfirmAddressPressed() {
    if (widget.popOnConformPressed) {
      Navigator.pop(context);
    } else {
      Navigator.pushReplacementNamed(
          context, CreateOrderDetailsScreen.routeName);
    }
  }

  @override
  Widget build(BuildContext context) {
    final viewPadding = MediaQuery.of(context).viewPadding;
    final safeAreaTopPadding = viewPadding.top;
    final safeAreaBottomPadding = viewPadding.bottom;
    final selectedAddress = context.read<CreateOrderProvider>().selectedAddress;
    return Scaffold(
      appBar: CommonAppBar(
        showCartIcon: false,
        title: 'select_location'.tr(),
      ),
      body: Column(
        children: [
          Expanded(
            child: Stack(
              children: [
                if (_isMapInitialized)
                  GoogleMap(
                    initialCameraPosition: selectedAddress == null
                        ? kInitialMapPosition
                        : CameraPosition(
                            target: LatLng(
                              double.parse(selectedAddress.lat),
                              double.parse(selectedAddress.lng),
                            ),
                            zoom: 16,
                          ),
                    markers: selectedAddress == null
                        ? {}
                        : {
                            Marker(
                              markerId: MarkerId(
                                  '${selectedAddress.lat}${selectedAddress.lng}'),
                              position: LatLng(
                                double.parse(selectedAddress.lat),
                                double.parse(selectedAddress.lng),
                              ),
                            )
                          },
                    myLocationEnabled: true,
                    myLocationButtonEnabled: true,
                    zoomControlsEnabled: true,
                    mapType: MapType.normal,
                    onTap: _onMapPressed,
                    onMapCreated: (GoogleMapController controller) {
                      _controllerCompleter.complete(controller);
                      _mapController = controller;
                    },
                  ),
                // Positioned(
                //   top: 10 + safeAreaTopPadding,
                //   right: 20,
                //   left: 20,
                //   child: Row(
                //     children: [
                //       SizedBox(
                //         width: 45,
                //         height: 45,
                //         child: TextButton(
                //           onPressed: () {
                //             Navigator.pop(context);
                //           },
                //           style: ButtonStyle(
                //             padding: MaterialStateProperty.all(
                //                 const EdgeInsets.all(0)),
                //             backgroundColor:
                //                 MaterialStateProperty.all(Colors.white),
                //             shape: MaterialStateProperty.all(
                //               RoundedRectangleBorder(
                //                 side: BorderSide(color: Colors.grey[400]!),
                //                 borderRadius: BorderRadius.circular(5),
                //               ),
                //             ),
                //           ),
                //           child: const Icon(Icons.arrow_back),
                //         ),
                //       ),
                //     ],
                //   ),
                // ),
              ],
            ),
          ),
          Container(
            width: double.infinity,
            height:
                (selectedAddress != null ? 220 : 150) + safeAreaBottomPadding,
            decoration: BoxDecoration(
              color: Colors.white,
              boxShadow: [kBoxShadow],
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(10),
                topRight: Radius.circular(10),
              ),
            ),
            padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 20),
            child: Column(
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      tr('select_your_location'),
                      style: Theme.of(context)
                          .textTheme
                          .titleMedium
                          ?.copyWith(fontWeight: FontWeight.bold),
                    ),
                    if (_loading)
                      const SizedBox(
                        width: 18,
                        height: 18,
                        child: CircularProgressIndicator(strokeWidth: 2),
                      ),
                  ],
                ),
                const SizedBox(height: 20),
                if (selectedAddress != null)
                  InkWell(
                    onTap: _onSelectLocationPressed,
                    child: Row(
                      children: [
                        Container(
                          width: 40,
                          height: 40,
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            color: Styles.primaryColor.withOpacity(0.1),
                          ),
                          child: const Icon(Icons.location_on,
                              color: Styles.primaryColor),
                        ),
                        const SizedBox(width: 10),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                selectedAddress.name,
                                style: Theme.of(context).textTheme.labelLarge,
                              ),
                              const SizedBox(height: 5),
                              Text(
                                selectedAddress.address,
                                maxLines: 2,
                                overflow: TextOverflow.ellipsis,
                              ),
                            ],
                          ),
                        ),
                        const Icon(Icons.edit, color: Styles.primaryColor),
                      ],
                    ),
                  )
                else
                  TextButton(
                    onPressed: _onSelectLocationPressed,
                    style: ButtonStyle(
                      backgroundColor:
                          MaterialStateProperty.all(Colors.grey[200]),
                      foregroundColor: MaterialStateProperty.all(Colors.grey),
                      textStyle: MaterialStateProperty.all(
                        const TextStyle(fontWeight: FontWeight.normal),
                      ),
                    ),
                    child: Row(
                      children: [
                        const Icon(Icons.search),
                        Expanded(
                          child: Text(
                            tr('select_your_location_and_select_from_saved_locations'),
                            style: TextStyle(
                                fontFamily: GoogleFonts.almarai().fontFamily),
                          ),
                        ),
                      ],
                    ),
                  ),
                if (selectedAddress != null) const SizedBox(height: 20),
                if (selectedAddress != null)
                  SizedBox(
                    width: 300,
                    child: ElevatedButton(
                      onPressed: _onConfirmAddressPressed,
                      child: Text(tr('confirm_address')),
                      style: ButtonStyle(
                        backgroundColor:
                            MaterialStateProperty.all(Styles.secondaryColor),
                      ),
                    ),
                  ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
