import 'package:bot_toast/bot_toast.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:one_drop/providers/user_provider.dart';
import 'package:one_drop/styles.dart';
import 'package:provider/provider.dart';

import '../api/user_api.dart';
import '../constants.dart';
import 'balance_added_success_screen.dart';
import 'main_tab_controller.dart';
import 'payment_method_screen.dart';

class AddBalanceScreen extends StatefulWidget {
  static const routeName = 'add-balance';

  const AddBalanceScreen({Key? key}) : super(key: key);

  @override
  State<AddBalanceScreen> createState() => _AddBalanceScreenState();
}

class _AddBalanceScreenState extends State<AddBalanceScreen> {
  final _formKey = GlobalKey<FormState>();
  final _amountFieldController = TextEditingController();

  @override
  void dispose() {
    _amountFieldController.dispose();
    super.dispose();
  }

  void _onNextPressed() async {
    FocusScope.of(context).unfocus();
    final isValidForm = _formKey.currentState?.validate() ?? false;
    if (!isValidForm) return;
    final amount = double.tryParse(_amountFieldController.text) ?? 0;
    Navigator.push(
      context,
      CupertinoPageRoute(
        builder: (cxt) => PaymentMethodScreen(
          onPaymentSuccess: _handlePaymentSuccess,
          paymentAmount: amount,
          paymentDescription: tr('add-balance'),
          paymentMethods: const [
            K.paymentMethodApplePay,
            K.paymentMethodCard,
          ],
        ),
        settings: const RouteSettings(name: PaymentMethodScreen.routeName),
      ),
    );
    // final paymentSuccess = await PaymentMethodScreen.payWithCard(
    //   paymentAmount: amount,
    //   paymentDescription: tr('send-gift'),
    // );
    // if (paymentSuccess) {
    //   _handlePaymentSuccess();
    // } else {
    //   Helpers.showErrorNotification();
    // }
    ///////////////////////////////////////////////////////////////
    // showModalBottomSheet<AddBalanceBottomSheetActions>(
    //   context: context,
    //   builder: (cxt) => AddBalanceBottomSheet(
    //     parentContext: context,
    //     title: tr('add-balance'),
    //     submitButtonText: tr('add-balance-to-wallet'),
    //     onSubmitButtonPressed: () {
    //       Navigator.pushNamedAndRemoveUntil(
    //         context,
    //         BalanceAddedSuccessScreen.routeName,
    //         (route) => route.settings.name == MainTabController.routeName,
    //       );
    //     },
    //   ),
    // );
  }

  void _handlePaymentSuccess({
    required PaymentMethod paymentMethod,
    required String transactionID,
  }) async {
    BotToast.showLoading();
    final user = context.read<UserProvider>().userModel;
    if (user == null || user.id == null) {
      BotToast.closeAllLoading();
      BotToast.showText(text: tr('something_went_wrong'));
      return;
    }
    try {
      await UserAPI().pay(
        userID: user.id!,
        paymentAmount: int.tryParse(_amountFieldController.text) ?? 0,
        purchasedItemID: null,
        paymentMethod: kMapPaymentMethodEnumToString[paymentMethod]!,
        purchasedItemType: K.purchasedItemTypeCharge,
        transactionID: transactionID,
        receivedID: null,
      );
    } catch (_) {}
    BotToast.closeAllLoading();
    Navigator.pushAndRemoveUntil(
      context,
      CupertinoPageRoute(
        builder: (cxt) => BalanceAddedSuccessScreen(
          amount: double.tryParse(_amountFieldController.text) ?? 0,
        ),
        settings:
            const RouteSettings(name: BalanceAddedSuccessScreen.routeName),
      ),
      (route) => route.settings.name == MainTabController.routeName,
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        centerTitle: true,
        title: Text(tr('add-balance')),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => Navigator.pop(context),
        ),
      ),
      body: SafeArea(
        child: Column(
          children: [
            const Spacer(),
            Form(
              key: _formKey,
              child: TextFormField(
                controller: _amountFieldController,
                autofocus: true,
                keyboardType: const TextInputType.numberWithOptions(
                  decimal: true,
                  signed: false,
                ),
                textAlign: TextAlign.center,
                style: Theme.of(context).textTheme.displaySmall?.copyWith(
                      color: Colors.black,
                      fontWeight: FontWeight.bold,
                    ),
                decoration: const InputDecoration(
                  border: InputBorder.none,
                  enabledBorder: InputBorder.none,
                  focusedBorder: InputBorder.none,
                  disabledBorder: InputBorder.none,
                  errorBorder: InputBorder.none,
                ),
                validator: (value) {
                  final number = double.tryParse(value ?? '') ?? 0;
                  if (number == 0) {
                    return tr('please-enter-valid-number');
                  }
                  return null;
                },
              ),
            ),
            Text(tr('saudi-riyal')),
            const Spacer(),
            SizedBox(
              width: 300,
              child: ElevatedButton(
                onPressed: _onNextPressed,
                child: Text(tr('next')),
                style: ButtonStyle(
                  backgroundColor:
                      MaterialStateProperty.all(Styles.secondaryColor),
                ),
              ),
            ),
            const SizedBox(height: 20),
          ],
        ),
      ),
    );
  }
}
