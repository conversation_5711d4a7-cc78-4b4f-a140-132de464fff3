import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';

import '../constants.dart';
import '../models/transaction_item_model.dart';
import 'wallet_screen.dart';

class AllTransactionsScreen extends StatefulWidget {
  static const routeName = 'all-transacitons';

  final List<TransactionItemModel> transactions;

  const AllTransactionsScreen({
    Key? key,
    required this.transactions,
  }) : super(key: key);

  @override
  State<AllTransactionsScreen> createState() => _AllTransactionsScreenState();
}

class _AllTransactionsScreenState extends State<AllTransactionsScreen> {
  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    // Widget child;
    // if (_loading) {
    //   child = const Center(child: CircularProgressIndicator());
    // } else if (_savedCards == null) {
    //   child = Center(child: Text(tr('error')));
    // } else if (_savedCards?.isEmpty ?? true) {
    //   child = Center(child: Text(tr('no-saved-cards')));
    // } else {
    // }
    final locale = EasyLocalization.of(context)?.currentLocale?.languageCode;
    final listItems = widget.transactions.map<Widget>((e) {
      return WalletFinancialTransactionItem(
        amount: e.amount?.toString() ?? '0',
        date: e.date == null ? '' : DateFormat.yMMMEd(locale).format(e.date!),
        icon: SvgPicture.asset(
          e.reason == K.transactionAddBalanaceReason ? 'assets/images/32.svg' : 'assets/images/33.svg',
          width: 30,
          height: 30,
        ),
        text: e.reason == K.transactionAddBalanaceReason ? tr('add-balance') : tr('send-gift'),
      );
    }).toList();
    final child = ListView.separated(
      itemBuilder: (cxt, index) => listItems[index],
      separatorBuilder: (cxt, index) => const SizedBox(height: 20),
      itemCount: listItems.length,
      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 20),
    );
    return Scaffold(
      appBar: AppBar(
        title: Text(tr('finacial_transactions')),
      ),
      body: SafeArea(child: child),
    );
  }
}
