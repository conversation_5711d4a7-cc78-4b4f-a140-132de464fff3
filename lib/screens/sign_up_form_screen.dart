import 'package:bot_toast/bot_toast.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:one_drop/api/user_api.dart';
import 'package:one_drop/helpers.dart';
import 'package:one_drop/providers/user_provider.dart';
import 'package:provider/provider.dart';

import '../models/city_model.dart';
import 'main_tab_controller.dart';

class SignUpFormScreen extends StatefulWidget {
  static const routeName = 'sign-up-form';

  const SignUpFormScreen({Key? key}) : super(key: key);

  @override
  State<SignUpFormScreen> createState() => _SignUpFormScreenState();
}

class _SignUpFormScreenState extends State<SignUpFormScreen> {
  final _formKey = GlobalKey<FormState>();
  final _nameFieldController = TextEditingController();
  final _emailFieldController = TextEditingController();
  final _neighborhoorFieldController = TextEditingController();
  var _loading = true;
  int? _selectedCityID;
  List<CityModel>? _cities;

  @override
  void initState() {
    super.initState();

    _initState();
  }

  @override
  void dispose() {
    _nameFieldController.dispose();
    _emailFieldController.dispose();
    _neighborhoorFieldController.dispose();
    super.dispose();
  }

  void _initState() async {
    _cities = await UserAPI().getCities();
    _loading = false;
    if (!mounted) return;
    setState(() {});
  }

  void _onContinuePressed() async {
    FocusScope.of(context).unfocus();
    final validForm = _formKey.currentState?.validate() ?? false;
    if (!validForm) return;
    // final model = context.read<UserProvider>().userModel;
    // print(model);
    BotToast.showLoading();
    final userMdoel = await UserAPI().updateProfile(
      name: _nameFieldController.text,
      email: _emailFieldController.text,
      neighborhood: _neighborhoorFieldController.text,
      cityID: _selectedCityID,
      profileImage: null,
    );
    if (userMdoel == null) {
      BotToast.closeAllLoading();
      // return Helpers.showErrorNotification();
      return;
    }
    await context.read<UserProvider>().setUserModel(userMdoel);
    BotToast.closeAllLoading();
    Navigator.pushNamed(context, MainTabController.routeName);
  }

  bool _isValidForm() {
    final nameError = Helpers.validateName(_nameFieldController.text);
    if (nameError?.isNotEmpty ?? false) {
      return false;
    }
    final emailError = Helpers.validateEmail(_emailFieldController.text);
    if (emailError?.isNotEmpty ?? false) {
      return false;
    }
    return true;
  }

  @override
  Widget build(BuildContext context) {
    Widget child;
    if (_loading) {
      child = const Center(child: CircularProgressIndicator());
    } else if (_cities == null) {
      child = Center(child: Text(tr('error')));
    } else {
      child = SingleChildScrollView(
        padding: const EdgeInsets.symmetric(horizontal: 20.0),
        child: Form(
          key: _formKey,
          child: Column(
            children: [
              Row(
                children: [
                  Text(
                    tr('sign_up'),
                    style: Theme.of(context).textTheme.headlineSmall,
                  ),
                ],
              ),
              const SizedBox(height: 20),
              TextFormField(
                controller: _nameFieldController,
                autofocus: true,
                decoration: InputDecoration(
                  labelText: tr('name'),
                ),
                validator: Helpers.validateName,
                onChanged: (value) {
                  setState(() {});
                },
              ),
              const SizedBox(height: 10),
              TextFormField(
                controller: _emailFieldController,
                decoration: InputDecoration(
                  labelText: tr('email'),
                ),
                keyboardType: TextInputType.emailAddress,
                validator: Helpers.validateEmail,
                onChanged: (value) {
                  setState(() {});
                },
              ),
              const SizedBox(height: 10),
              DropdownButtonFormField<int>(
                decoration: InputDecoration(
                  labelText: tr('city'),
                ),
                items: (_cities ?? []).map<DropdownMenuItem<int>>((e) {
                  return DropdownMenuItem<int>(
                    child: Text(e.getName(context)),
                    value: e.id,
                  );
                }).toList(),
                // items: const <DropdownMenuItem<int>>[
                //   DropdownMenuItem(
                //     child: Text('المدينة'),
                //     value: 1,
                //   ),
                //   DropdownMenuItem(
                //     child: Text('مكة'),
                //     value: 2,
                //   ),
                // ],
                onChanged: (value) {
                  _selectedCityID = value;
                  setState(() {});
                },
              ),
              const SizedBox(height: 10),
              TextFormField(
                controller: _neighborhoorFieldController,
                decoration: InputDecoration(
                  labelText: tr('neighborhood'),
                ),
              ),
              const SizedBox(height: 50),
            ],
          ),
        ),
      );
    }
    return Scaffold(
      appBar: AppBar(
        leading: IconButton(
          icon: const Icon(Icons.arrow_back_sharp),
          iconSize: 30,
          onPressed: () => Navigator.pop(context),
        ),
      ),
      body: GestureDetector(
        onTap: () => FocusScope.of(context).unfocus(),
        child: SafeArea(
          child: Column(
            children: [
              Expanded(child: child),
              SizedBox(
                width: 300,
                child: ElevatedButton(
                  onPressed: _isValidForm() ? _onContinuePressed : null,
                  child: Text(tr('continue')),
                ),
              ),
              const SizedBox(height: 20),
            ],
          ),
        ),
      ),
    );
  }
}
