import 'dart:async';
import 'dart:io';
import 'dart:math';

import 'package:bot_toast/bot_toast.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';
import 'package:flutter_svg/svg.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:intl/intl.dart';
import 'package:myfatoorah_flutter/myfatoorah_flutter.dart';
import 'package:one_drop/api/user_api.dart';
import 'package:one_drop/models/available_time_slot_model.dart';
import 'package:one_drop/models/cart.dart';
import 'package:one_drop/providers/create_order_provider.dart';
import 'package:one_drop/providers/user_provider.dart';
import 'package:one_drop/screens/main_tab_controller.dart';
import 'package:one_drop/services/cart_service.dart';
import 'package:one_drop/services/service_locator.dart';
import 'package:one_drop/services/card_payment_browser_service.dart';
import 'package:one_drop/services/orders_service.dart';
import 'package:one_drop/widgets/time_slot_selector.dart';
import 'package:provider/provider.dart';
import 'package:one_drop/services/cart_update_service.dart';

import '../api/my_fatoorah_api.dart';
import '../constants.dart';
import '../models/package_item_model.dart';
import '../models/wallet_model.dart';
import '../styles.dart';
import '../utils/notification_utils.dart';
import '../widgets/payment_method_list_item.dart';
import 'order_confirmation_screen.dart';
import '../widgets/cart_icon_with_badge.dart';

class PaymentMethodScreen extends StatefulWidget {
  static const routeName = 'payment-method';
  static final _dateFormat = DateFormat('yyyy-MM-dd');

  final double? paymentAmount;
  final String? paymentDescription;
  final PackageItemModel? paymentPackage;
  final List<PaymentMethodModel> paymentMethods;
  // final List<PaymentItem>? paymentItems;
  final void Function({
    required PaymentMethod paymentMethod,
    required String transactionID,
  })? onPaymentSuccess;

  /// تحديد ما إذا كان المستخدم قادمًا من صفحة الاشتراكات
  final bool isFromPackages;

  const PaymentMethodScreen({
    Key? key,
    required this.paymentAmount,
    required this.onPaymentSuccess,
    // required this.paymentItems,
    required this.paymentDescription,
    this.paymentPackage,
    this.isFromPackages = false, // قيمة افتراضية false
    this.paymentMethods = const [
      // K.paymentMethodApplePay, // تم إخفاء زر Apple Pay
      K.paymentMethodWallet,
      K.paymentMethodCard,
      K.paymentMethodCash,
    ],
  }) : super(key: key);

// تحديث دالة createOrder في PaymentMethodScreen
// استبدل دالة createOrder بالنسخة الجديدة
  static void createOrder(
      {required String paymentMethod,
      required String transactionID,
      required BuildContext context,
      required bool includeCart}) async {
    BotToast.showLoading();
    final createOrderProvider = context.read<CreateOrderProvider>();
    final address = createOrderProvider.selectedAddress;
    final car = createOrderProvider.selectedCarType;
    final category = createOrderProvider.selectedOrderCategory;
    final coupon = createOrderProvider.coupon;
    final scheduledDateTime = createOrderProvider.scheduledDateTime;
    final cart = createOrderProvider.cart;

    if (category == null || car == null || address == null) {
      return NotificationUtils.showError(tr('error'));
    }

    final orderModel = await UserAPI().createOrder(
      address: address,
      car: car,
      category: category,
      coupon: coupon,
      cityName: createOrderProvider.cityOrNeighborhoodName,
      scheduledAt: scheduledDateTime != null
          ? DateFormat('yyyy-MM-dd HH:mm') //
              .format(scheduledDateTime)
          : null,
      cartId: (includeCart && cart != null) ? cart.id : null,
    );
    if (orderModel == null) {
      BotToast.closeAllLoading();
      return NotificationUtils.showError(tr('error'));
    } else if (orderModel.errorMessage != null) {
      BotToast.closeAllLoading();
      return NotificationUtils.showError(tr(orderModel.errorMessage!));
    }
    final user = context.read<UserProvider>().userModel;
    await UserAPI().pay(
      userID: user!.id!,
      paymentAmount: orderModel.calculateOrderTotalPrice.toInt() ?? 0,
      purchasedItemID: orderModel.id,
      paymentMethod: paymentMethod,
      purchasedItemType: kMapPaymentTypeToForableType[paymentMethod] ??
          K.purchasedItemTypeOrder,
      transactionID: transactionID,
      receivedID: null,
    );
    BotToast.closeAllLoading();
    sl.get<OrderService>().addOrderCreated(orderModel.id);
    Navigator.pushAndRemoveUntil(
      context,
      CupertinoPageRoute(
        builder: (cxt) => OrderConfirmationScreen(order: orderModel),
        settings: const RouteSettings(name: OrderConfirmationScreen.routeName),
      ),
      (route) => route.settings.name == MainTabController.routeName,
    );
  }

  static Future<bool> startCardPayment({
    required double paymentAmount,
    required String paymentDescription,
    required BuildContext context,
    // required PaymentCardModel selectdPaymentCard,
    required CardPaymentBrowserService cardPaymentService,
    String currencyCode = 'SAR',
    // required List<PaymentItem> paymentItems,
    // required List<Shipping> shippingAddresses,
  }) async {
    BotToast.showLoading();
    final myFatoorahAPI = MyFatoorahAPI();
    final initPaymentResponse = await myFatoorahAPI.initPayment(
      paymentAmount: paymentAmount,
      currencyCode: currencyCode,
    );
    if (initPaymentResponse == null || !initPaymentResponse.isSuccess) {
      BotToast.closeAllLoading();
      NotificationUtils.showError(tr('error'));
      return false;
    }
    final user = context.read<UserProvider>().userModel;
    final currentLocale = EasyLocalization.of(context)?.locale.languageCode;
    try {
      final executePaymentResponse = await myFatoorahAPI.executePayment(
        customerEmail: user?.email,
        customerName: user?.name,
        paymentAmount: paymentAmount,
        languageCode: currentLocale,
        currencyCode: currencyCode,
        paymentMethodID:
            initPaymentResponse.getMasterVisaPaymentMethod!.paymentMethodID,
      );
      if (executePaymentResponse == null ||
          !(executePaymentResponse.isSuccess)) {
        NotificationUtils.showError(tr('error'));
        return false;
      }
      await cardPaymentService.openUrlRequest(
        urlRequest: URLRequest(
          url: WebUri(executePaymentResponse.data.paymentUrl),
        ),
        options: InAppBrowserClassOptions(
          crossPlatform: InAppBrowserOptions(
            hideUrlBar: false,
            hideProgressBar: false,
            hideToolbarTop: false,
            hidden: false,
            toolbarTopBackgroundColor: Colors.white,
          ),
          inAppWebViewGroupOptions: InAppWebViewGroupOptions(
            crossPlatform: InAppWebViewOptions(
              javaScriptEnabled: true,
            ),
          ),
          ios: IOSInAppBrowserOptions(
            hideToolbarBottom: true,
            toolbarBottomBackgroundColor: Colors.white,
            toolbarBottomTranslucent: true,
            toolbarTopTranslucent: true,
          ),
        ),
      );
      return true;
    } catch (e) {
      NotificationUtils.showError(tr('error'));
      return false;
    } finally {
      BotToast.closeAllLoading();
    }
  }

  @override
  State<PaymentMethodScreen> createState() => _PaymentMethodScreenState();
}

class _PaymentMethodScreenState extends State<PaymentMethodScreen>
    with RouteAware {
  // final _applePayService = ApplePayService();
  PaymentMethod? _selectedPaymentMethod;
  var _loadingMFApplePay = false;
  bool _isApplePayButtonCreated = false;
  // var _savedPaymentCards = <PaymentCardModel>[];
  MFApplePayButton? _mfApplePayButton;
  CardPaymentBrowserService? _cardPaymentService;
  bool _isSDKInitialized = false;
  // PaymentCardModel? _selectdPaymentCard;
  DateTime? _scheduledDate;
  bool _includeCart = false;
  CartResponse? _cart;
  late FocusNode _focusNode;
  late RouteObserver<PageRoute> routeObserver;
  late StreamSubscription _cartUpdateSubscription;
  MFInitiateSessionResponse? _mfSessionResponse;

  bool _isLoadingTimeSlots = false;
  String? _selectedTimeSlot;
  List<TimeSlot> _availableTimeSlots = [];

// إضافة دالة جديدة لتحميل الفترات المتاحة
// أضف هذه الدالة في فئة _PaymentMethodScreenState
  Future<void> _loadAvailableTimeSlots(DateTime date) async {
    if (!mounted) return;

    setState(() {
      _isLoadingTimeSlots = true;
      _availableTimeSlots = [];
      _selectedTimeSlot = null;
    });

    try {
      final dateString = DateFormat('yyyy-MM-dd').format(date);
      final response = await UserAPI().getAvailableTimeSlots(date: dateString);

      if (response != null && mounted) {
        setState(() {
          _availableTimeSlots = response.availableSlots;
          _isLoadingTimeSlots = false;
        });
      } else if (mounted) {
        setState(() {
          _isLoadingTimeSlots = false;
        });
        NotificationUtils.showError(tr('error_loading_time_slots'));
      }
    } catch (e) {
      print('Error loading time slots: $e');
      if (mounted) {
        setState(() {
          _isLoadingTimeSlots = false;
        });
        NotificationUtils.showError(tr('error_loading_time_slots'));
      }
    }
  }

// تحديث _onTimeSlotSelected في _PaymentMethodScreenState
  void _onTimeSlotSelected(String time) {
    setState(() {
      _selectedTimeSlot = time;
    });

    // تحديث تاريخ الجدولة مع الوقت المحدد
    if (_scheduledDate != null) {
      // تعامل مع تنسيق الوقت "HH:MM:SS"
      final timeComponents = time.split(':');
      if (timeComponents.length >= 2) {
        // نتأكد من وجود ساعة ودقيقة على الأقل
        final scheduledDateTime = DateTime(
          _scheduledDate!.year,
          _scheduledDate!.month,
          _scheduledDate!.day,
          int.parse(timeComponents[0]),
          int.parse(timeComponents[1]),
        );

        // تخزين التاريخ والوقت في Provider
        context
            .read<CreateOrderProvider>()
            .setScheduledDateTime(scheduledDateTime);
      }
    }
  }

// تحديث دالة _selectDate في فئة _PaymentMethodScreenState
  Future<void> _selectDate(BuildContext context) async {
    // Start date should be tomorrow
    final tomorrow = DateTime.now().add(const Duration(days: 1));

    final DateTime? pickedDate = await showDatePicker(
      context: context,
      initialDate: _scheduledDate ?? tomorrow,
      firstDate: tomorrow,
      lastDate: DateTime.now().add(const Duration(days: 30)),
    );

    if (pickedDate != null) {
      setState(() {
        _scheduledDate = pickedDate;
        _selectedTimeSlot = null; // Reset time slot when date changes
      });

      // Save only the date part to provider
      context.read<CreateOrderProvider>().setScheduledDate(pickedDate);

      // Load available time slots for the selected date
      await _loadAvailableTimeSlots(pickedDate);
    }
  }

// استبدل دالة _buildScheduleDateTimeCard بالنسخة الجديدة
  Widget _buildScheduleDateTimeCard() {
    final dateFormatter = DateFormat('EEEE, MMM dd, yyyy');

    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Column(
        children: [
          ListTile(
            leading:
                const Icon(Icons.calendar_today, color: Styles.primaryColor),
            title: Text(tr('schedule_delivery_date')),
            trailing: Switch(
              value: _scheduledDate != null,
              onChanged: (value) {
                if (!value) {
                  setState(() {
                    _scheduledDate = null;
                    _selectedTimeSlot = null;
                    _availableTimeSlots = [];
                  });
                  context.read<CreateOrderProvider>().setScheduledDate(null);
                  context
                      .read<CreateOrderProvider>()
                      .setScheduledDateTime(null);
                } else {
                  _selectDate(context);
                }
              },
              activeColor: Styles.primaryColor,
            ),
          ),
          if (_scheduledDate != null) ...[
            const Divider(height: 1),
            Padding(
              padding: const EdgeInsets.all(16),
              child: Row(
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          dateFormatter.format(_scheduledDate!),
                          style: const TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        if (_selectedTimeSlot != null)
                          Padding(
                            padding: const EdgeInsets.only(top: 4),
                            child: Text(
                              '${tr('time')}: $_selectedTimeSlot',
                              style: const TextStyle(
                                fontSize: 14,
                                color: Styles.primaryColor,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ),
                      ],
                    ),
                  ),
                  TextButton.icon(
                    onPressed: () => _selectDate(context),
                    icon: const Icon(Icons.edit),
                    label: Text(tr('change')),
                  ),
                ],
              ),
            ),

            // عرض الفترات الزمنية المتاحة
            if (_scheduledDate != null) ...[
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16),
                child: Align(
                  alignment: Alignment.centerLeft,
                  child: Text(
                    tr('available_time_slots'),
                    style: const TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ),
              Padding(
                padding: const EdgeInsets.fromLTRB(16, 8, 16, 16),
                child: TimeSlotSelector(
                  availableSlots: _availableTimeSlots,
                  selectedTime: _selectedTimeSlot,
                  onTimeSelected: _onTimeSlotSelected,
                  isLoading: _isLoadingTimeSlots,
                ),
              ),
            ],

            Padding(
              padding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
              child: Text(
                tr('scheduled_delivery_note'),
                style: const TextStyle(
                  color: Colors.grey,
                  fontSize: 12,
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }

// تحديث دالة initState في _PaymentMethodScreenState
// استبدل دالة initState بالنسخة الجديدة
  @override
  void initState() {
    super.initState();
    routeObserver = RouteObserver<PageRoute>();
    _focusNode = FocusNode();
    _cartUpdateSubscription = CartUpdateService().onCartUpdate.listen((_) {
      _handleCartUpdate();
    });

    // استرجاع القيم المحفوظة من Provider إذا كانت موجودة
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final createOrderProvider = context.read<CreateOrderProvider>();

      // تحديث التاريخ المحدد من Provider
      final providedDate = createOrderProvider.scheduledDate;
      if (providedDate != null && mounted) {
        setState(() {
          _scheduledDate = providedDate;
        });

        // تحميل الفترات الزمنية المتاحة
        _loadAvailableTimeSlots(providedDate);

        // تحديث الوقت المختار من Provider إذا كان متاحًا
        final providedDateTime = createOrderProvider.scheduledDateTime;
        if (providedDateTime != null) {
          // تنسيق لإظهار الساعات والدقائق فقط
          final timeString = DateFormat('HH:mm').format(providedDateTime);
          setState(() {
            _selectedTimeSlot = timeString;
          });
        }
      }

      if (Platform.isIOS) {
        setState(() {
          _loadingMFApplePay = true;
        });
      }
      _initializePaymentServices();
    });
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    routeObserver.subscribe(this, ModalRoute.of(context) as PageRoute);
  }

  @override
  void dispose() {
    print('🚨 Disposing payment screen...');
    _cartUpdateSubscription.cancel();
    routeObserver.unsubscribe(this);
    _focusNode.dispose();

    // إلغاء أي عمليات دفع معلقة
    if (_cardPaymentService != null) {
      try {
        _cardPaymentService!.close();
      } catch (e) {
        print('❌ Error closing payment service: $e');
      }
    }

    // تنظيف زر Apple Pay وإعادة تعيين المتغيرات
    print('💳 [Apple Pay] Starting cleanup in dispose...');
    _mfApplePayButton = null;
    _isApplePayButtonCreated = false;
    _isSDKInitialized = false;
    _loadingMFApplePay = false;
    print('💳 [Apple Pay] Cleanup in dispose completed');

    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    if (state == AppLifecycleState.resumed) {
      // Reload cart when app comes to foreground
      _loadCart();
    }
  }

  bool _isLoadingCart = false;
  DateTime? _lastCartLoadTime;

  Future<void> _loadCart() async {
    // Prevent multiple simultaneous cart loads
    if (_isLoadingCart) return;

    // Don't reload if we just loaded recently (within last 2 seconds)
    if (_lastCartLoadTime != null &&
        DateTime.now().difference(_lastCartLoadTime!) <
            const Duration(seconds: 2)) {
      return;
    }

    _isLoadingCart = true;
    try {
      print('Reloading cart data...');
      final cart = await sl.get<CartService>().getCart();

      if (mounted) {
        final bool shouldUpdate = _cart == null ||
            _cart!.items.length != cart.items.length ||
            _cart!.total != cart.total;

        if (shouldUpdate) {
          setState(() {
            _cart = cart;
            // Reset include cart if cart is empty
            if (cart.items.isEmpty) {
              _includeCart = false;
            }
          });
          context.read<CreateOrderProvider>().cart = cart;
        }
      }
      _lastCartLoadTime = DateTime.now();
    } catch (e, stackTrace) {
      print('❌ Error loading cart:');
      print('Error: $e');
      print('Stack trace: $stackTrace');
    } finally {
      _isLoadingCart = false;
    }
  }

  double get _totalPrice {
    // سعر الطلب (غسيل السيارة)
    final orderPrice = widget.paymentAmount ?? 0.0;

    // سعر منتجات السلة
    double cartTotal = 0.0;
    if (_includeCart && _cart != null) {
      // استخدام calculatedTotal لضمان حساب دقيق
      cartTotal = _cart!.calculatedTotal;
    }

    // المجموع الكلي
    return orderPrice + cartTotal;
  }

  bool _isInitializingPayment = false;

  Future<void> _initializePaymentServices() async {
    if (!mounted || _isInitializingPayment) {
      print(
          '💳 [Apple Pay] Skipping initialization - already in progress or unmounted');
      if (mounted) {
        setState(() {
          _loadingMFApplePay = false;
        });
      }
      return;
    }

    _isInitializingPayment = true;
    try {
      print('💳 [Apple Pay] Starting payment services initialization...');

      // تهيئة خدمة الدفع بالبطاقة
      _cardPaymentService = CardPaymentBrowserService(
        context: context,
        onPaymentCompleted: _handleCardPaymentCompleted,
      );

      // تحميل بيانات السلة أولاً
      await _loadCart();

      if (Platform.isIOS) {
        print('💳 [Apple Pay] Platform is iOS, initializing MyFatoorah...');

        // Initialize SDK first
        print('💳 [Apple Pay] Calling MFSDK.init...');
        MFSDK.init(
          K.myFatoorahToken,
          MFCountry.SAUDIARABIA,
          K.paymentLiveMode ? MFEnvironment.LIVE : MFEnvironment.TEST,
        );

        if (!mounted) {
          print('💳 [Apple Pay] Widget unmounted after SDK init');
          return;
        }

        setState(() {
          _isSDKInitialized = true;
        });
        print('💳 [Apple Pay] SDK initialized successfully');

        // Start session regardless of Apple Pay availability
        print('💳 [Apple Pay] Starting MF session...');
        await _initMFSession();
      }
    } catch (e, stackTrace) {
      print('💳 [Apple Pay] Error initializing payment services:');
      print('💳 [Apple Pay] Error: $e');
      print('💳 [Apple Pay] Stack trace: $stackTrace');
      if (mounted) {
        NotificationUtils.showError(tr('payment_initialization_failed'));
        setState(() {
          _isSDKInitialized = false;
          _loadingMFApplePay = false;
          _mfApplePayButton = null;
          _isApplePayButtonCreated = false;
        });
      }
    } finally {
      _isInitializingPayment = false;
      if (mounted) {
        setState(() {
          _loadingMFApplePay = !_isSDKInitialized;
        });
      }
      print('💳 [Apple Pay] Initialization process completed');
    }
  }

  Future<void> _initMFSession() async {
    print('💳 [Apple Pay] Initiating session...');
    try {
      await MFSDK.initiateSession(
        MFInitiateSessionRequest(),
        (String result) async {
          if (!mounted) {
            print('💳 [Apple Pay] Widget unmounted during session callback');
            return;
          }

          try {
            print('💳 [Apple Pay] Session initiated successfully');
            if (Platform.isIOS) {
              await _loadMFApplePay();
            } else {
              // Reset loading state if not iOS
              if (mounted) {
                setState(() {
                  _loadingMFApplePay = false;
                });
              }
            }
          } catch (e) {
            print('💳 [Apple Pay] Session init failed: $e');
            if (mounted) {
              setState(() {
                _loadingMFApplePay = false;
              });
            }
          }
        },
      );
    } catch (e) {
      print('💳 [Apple Pay] Session init error: $e');
      if (mounted) {
        setState(() {
          _loadingMFApplePay = false;
        });
      }
    }
  }

  Future<void> _loadMFApplePay() async {
    try {
      await _loadCart();

      if (Platform.isIOS) {
        print('💳 [Apple Pay] Platform is iOS, initializing MyFatoorah...');

        // Reset states
        if (mounted) {
          setState(() {
            _isSDKInitialized = false;
            _loadingMFApplePay = true;
            _mfApplePayButton = null;
          });
        }

        // Initialize SDK first
        print('💳 [Apple Pay] Calling MFSDK.init...');
        MFSDK.init(
          K.myFatoorahToken,
          MFCountry.SAUDIARABIA,
          K.paymentLiveMode ? MFEnvironment.LIVE : MFEnvironment.TEST,
        );

        if (!mounted) {
          print('💳 [Apple Pay] Widget unmounted after SDK init');
          return;
        }

        setState(() {
          _isSDKInitialized = true;
        });
        print('💳 [Apple Pay] SDK initialized successfully');

        // Create Apple Pay button
        print('💳 [Apple Pay] Creating Apple Pay button...');
        _mfApplePayButton = MFApplePayButton();

        print('💳 [Apple Pay] Starting new session for button...');
        await MFSDK.initiateSession(
          MFInitiateSessionRequest(),
          (String result) async {
            if (!mounted) return;

            try {
              print('💳 [Apple Pay] Loading button...');
              final request = MFExecutePaymentRequest(
                invoiceValue: _totalPrice,
                displayCurrencyIso: MFCurrencyISO.SAUDIARABIA_SAR,
              );

              await _mfApplePayButton?.applePayPayment(
                request,
                MFLanguage.ENGLISH,
                (String invoiceId) {
                  print(
                      '💳 [Apple Pay] Payment completed with invoice ID: $invoiceId');
                  if (invoiceId.isNotEmpty) {
                    _handlePaymentSuccess(
                      paymentMethod: K.paymentTypeApplePay,
                      transactionID: invoiceId,
                    );
                  } else {
                    print('💳 [Apple Pay] Payment failed: Empty invoice ID');
                    NotificationUtils.showError(tr('payment_failed'));
                  }
                },
              );

              if (mounted) {
                setState(() {
                  _loadingMFApplePay = false;
                });
              }
            } catch (e) {
              print('💳 [Apple Pay] Error setting up button: $e');
              if (mounted) {
                setState(() {
                  _loadingMFApplePay = false;
                  _mfApplePayButton = null;
                });
              }
            }
          },
        );
      }
    } catch (e) {
      print('💳 [Apple Pay] Error during initialization: $e');
      if (mounted) {
        setState(() {
          _loadingMFApplePay = false;
          _mfApplePayButton = null;
        });
      }
    }
  }

  Widget _buildApplePayWidget(PaymentMethodModel method, Widget child) {
    if (!Platform.isIOS) return const SizedBox();
    const xPadding = EdgeInsets.symmetric(horizontal: 20);

    return Padding(
      padding: xPadding,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            tr('pay_using_apple_pay'),
            style: Theme.of(context).textTheme.labelLarge,
          ),
          SizedBox(
            height: 60, // Fixed height for the Apple Pay button container
            child: Stack(
              key: const ValueKey('apple-pay-payment-method-widget'),
              children: [
                // Show Apple Pay button when ready
                if (_mfApplePayButton != null && !_loadingMFApplePay)
                  Positioned.fill(
                    child: Container(
                      padding: const EdgeInsets.symmetric(vertical: 10),
                      child: _mfApplePayButton!,
                    ),
                  ),
                // Show loading indicator only during active loading
                if (_loadingMFApplePay)
                  const Positioned.fill(
                    child: Center(child: CircularProgressIndicator()),
                  ),
                // Show placeholder when not loading and no button
                if (_mfApplePayButton == null && !_loadingMFApplePay)
                  Positioned.fill(
                    child: Container(
                      decoration: BoxDecoration(
                        color: Colors.grey[200],
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Center(
                        child: Text(
                          'Apple Pay not available',
                          style: TextStyle(
                            color: Colors.grey[600],
                            fontSize: 14,
                          ),
                        ),
                      ),
                    ),
                  ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  void _handleCardPaymentCompleted(PaymentCompletedModel payment) {
    if (payment.success && payment.transactionID != null) {
      _handlePaymentSuccess(
        paymentMethod:
            kMapPaymentMethodEnumToString[_selectedPaymentMethod] ?? 'visa',
        transactionID: payment.transactionID!,
      );
    }
  }

  void _selectPaymentMethod(PaymentMethod paymentMethod) {
    if (paymentMethod == _selectedPaymentMethod) return;
    setState(() {
      _selectedPaymentMethod = paymentMethod;
    });
  }

  void _handlePaymentSuccess({
    required String paymentMethod,
    required String transactionID,
  }) async {
    if (widget.onPaymentSuccess == null) {
      PaymentMethodScreen.createOrder(
        includeCart: _includeCart,
        context: context,
        paymentMethod: paymentMethod,
        transactionID: transactionID,
      );
    } else {
      Navigator.pop(context);
      widget.onPaymentSuccess!(
        paymentMethod: paymentMethod == K.paymentTypeApplePay
            ? PaymentMethod.applePay
            : _selectedPaymentMethod!,
        transactionID: transactionID,
      );
    }
  }

  Future<MFApplePayButton?> _createMDApplePayButton() async {
    print('💳 [Apple Pay] Starting button creation...');
    if (_isApplePayButtonCreated) {
      print(
          '💳 [Apple Pay] Button already exists (_isApplePayButtonCreated=true)');
      return null;
    }

    // Check if Apple Pay is available on the device
    if (!Platform.isIOS) {
      print('💳 [Apple Pay] Not an iOS device');
      return null;
    }

    try {
      // Try creating the button - if it fails, Apple Pay is likely not available
      _isApplePayButtonCreated = true;
      final button = MFApplePayButton(
        applePayStyle: MFApplePayStyle(),
      );
      print('💳 [Apple Pay] Button created successfully');
      return button;
    } catch (e, stackTrace) {
      print('💳 [Apple Pay] Error creating button: $e');
      print('💳 [Apple Pay] Stack trace: $stackTrace');
      _isApplePayButtonCreated = false;
      return null;
    }
  }

  Widget _buildWalletWidget(PaymentMethodModel method) {
    final createOrderProvider = context.read<CreateOrderProvider>();
    final car = createOrderProvider.selectedCarType;
    final price = widget.paymentAmount ?? (car?.price ?? 0);
    return Padding(
      key: const ValueKey('wallet-payment-method-widget'),
      padding: const EdgeInsets.only(left: 20, right: 20, bottom: 20),
      child: FutureBuilder<WalletModel?>(
        future: UserAPI().getWallet(),
        builder: (context, snapshot) {
          final currenBalance = snapshot.data?.balance ?? 0;
          final hasEnoughBalance = currenBalance >= price;
          return PaymentMethodListItem(
            name: tr('pay_using_wallet'),
            icon: method.svgPath == null
                ? null
                : SvgPicture.asset(method.svgPath!),
            selected: _selectedPaymentMethod == method.method,
            onPressed: hasEnoughBalance
                ? () => _selectPaymentMethod(method.method)
                : null,
            subtitle: snapshot.data?.balance == null
                ? null
                : Text(
                    '${tr('balance')}: ${snapshot.data?.balance.toInt()} ${tr('saudi_riyal_full')}',
                    style: const TextStyle(color: Colors.grey),
                  ),
          );
        },
      ),
    );
  }

  Widget _buildPaymentMethodWidget(PaymentMethodModel method) {
    // إخفاء زر Apple Pay
    if (method.method == PaymentMethod.applePay) {
      return const SizedBox.shrink(); // إرجاع عنصر فارغ لإخفاء زر Apple Pay
    }

    const xPadding = EdgeInsets.symmetric(horizontal: 20);
    final child = Padding(
      key: ValueKey('$method-widget'),
      padding: xPadding.copyWith(bottom: 20),
      child: PaymentMethodListItem(
        name: method.method == PaymentMethod.cash
            ? tr('pay_cash')
            : tr(method.name),
        icon: method.svgPath == null
            ? null
            : SvgPicture.asset(
                method.svgPath!,
                width: 20,
                height: 20,
              ),
        selected: _selectedPaymentMethod == method.method,
        onPressed: () => _selectPaymentMethod(method.method),
        bottom: null,
        expanded: false,
        subtitle: null,
      ),
    );
    if (method.method == PaymentMethod.card) {
      return _buildCardPaymentWidget(method);
    } else if (method.method == PaymentMethod.wallet) {
      return _buildWalletWidget(method);
    } else if (method.method == PaymentMethod.package) {
      return _buildPackagePaymentWidget(method);
    }
    return child;
  }

  Widget _buildPackagePaymentWidget(PaymentMethodModel method) {
    const xPadding = EdgeInsets.symmetric(horizontal: 20);
    return Padding(
      key: ValueKey('$method-widget'),
      padding: xPadding.copyWith(bottom: 20),
      child: PaymentMethodListItem(
        name: tr(method.name),
        icon: method.svgPath == null
            ? null
            : SvgPicture.asset(
                method.svgPath!,
                width: 30,
                height: 30,
              ),
        selected: _selectedPaymentMethod == method.method,
        onPressed: () => _selectPaymentMethod(method.method),
        expanded: false,
        bottom: null,
        subtitle: Wrap(
          // استخدام Wrap بدلاً من Row لمنع تجاوز الحدود
          spacing: 4, // المسافة بين العناصر
          runSpacing: 4, // المسافة بين الصفوف
          children: [
            Text(
              '${widget.paymentPackage?.price.toInt() ?? 0} ${tr('sr')}',
              style: const TextStyle(color: Styles.primaryColor, fontSize: 12),
            ),
            Text(
              '/ ${tr('monthly')}',
              style: const TextStyle(fontSize: 12),
            ),
            Text(
              '${tr('remaining')} ${widget.paymentPackage?.remainingWashes ?? 0} ${tr('wash')}',
              style: const TextStyle(color: Colors.grey, fontSize: 12),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCardPaymentWidget(PaymentMethodModel method) {
    const xPadding = EdgeInsets.symmetric(horizontal: 20);
    final child = Padding(
      key: ValueKey('$method-widget'),
      padding: xPadding.copyWith(bottom: 20),
      child: PaymentMethodListItem(
        name: tr('pay_using_card'),
        icon: method.svgPath == null
            ? null
            : SvgPicture.asset(
                method.svgPath!,
                width: 20,
                height: 20,
              ),
        selected: _selectedPaymentMethod == method.method,
        onPressed: () => _selectPaymentMethod(method.method),
        // bottom: SelectPaymentCard(
        //   savedCards: _savedPaymentCards,
        //   selectedCard: _selectdPaymentCard,
        //   onCardSelected: _onPaymentCardSelected,
        //   onNewCardAdded: _onNewPaymentCardAdded,
        // ),
        // expanded: _selectedPaymentMethod == method.method,
        subtitle: null,
      ),
    );
    return child;
  }

  // void _onNewPaymentCardAdded(PaymentCardModel card) {
  //   _savedPaymentCards.add(card);
  //   _selectdPaymentCard = card;
  //   setState(() {});
  // }

  Widget _buildSectionHeader(String title) {
    return Padding(
      padding: const EdgeInsets.fromLTRB(18, 24, 18, 12),
      child: Text(
        title,
        style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: Colors.black87,
            ),
      ),
    );
  }

  Widget _buildOrderSummaryCard() {
    final createOrderProvider = context.watch<CreateOrderProvider>();
    final car = createOrderProvider.selectedCarType;
    final category = createOrderProvider.selectedOrderCategory;

    return Card(
      margin: const EdgeInsets.all(16),
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              tr('order_summary'),
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 12),
            if (category != null)
              ListTile(
                contentPadding: EdgeInsets.zero,
                leading: const Icon(Icons.cleaning_services),
                title: Text(category.name),
                dense: true,
              ),
            if (car != null)
              ListTile(
                contentPadding: EdgeInsets.zero,
                leading: const Icon(Icons.directions_car),
                title: Text(car.name),
                dense: true,
              ),
            if (_scheduledDate != null)
              ListTile(
                contentPadding: EdgeInsets.zero,
                leading: const Icon(Icons.calendar_today),
                title: Text(
                    PaymentMethodScreen._dateFormat.format(_scheduledDate!)),
                dense: true,
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildCartSection() {
    if (_cart == null || (_cart!.items.isEmpty)) {
      return const SizedBox.shrink();
    }

    final cartTotal = _cart!.total;
    final orderPrice = widget.paymentAmount ?? 0.0;

    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Padding(
          //   padding: const EdgeInsets.all(16),
          //   child: Text(
          //     tr('cart_items'),
          //     style: Theme.of(context).textTheme.titleMedium?.copyWith(
          //           fontWeight: FontWeight.bold,
          //         ),
          //   ),
          // ),
          ListView.separated(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: _cart!.items.length,
            separatorBuilder: (context, index) => const Divider(height: 1),
            itemBuilder: (context, index) {
              final item = _cart!.items[index];
              final product = item.fullProduct;

              return ListTile(
                contentPadding:
                    const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                leading: product?.image != null && product!.image.isNotEmpty
                    ? ClipRRect(
                        borderRadius: BorderRadius.circular(8),
                        child: Image.network(
                          product.image,
                          width: 60,
                          height: 60,
                          fit: BoxFit.cover,
                          errorBuilder: (context, error, stackTrace) {
                            return Container(
                              width: 60,
                              height: 60,
                              color: Colors.grey[200],
                              child: const Icon(Icons.image_not_supported),
                            );
                          },
                        ),
                      )
                    : Container(
                        width: 60,
                        height: 60,
                        decoration: BoxDecoration(
                          color: Colors.grey[200],
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: const Icon(Icons.image_not_supported),
                      ),
                title: Text(
                  product?.name ?? tr('product_not_found'),
                  style: const TextStyle(fontWeight: FontWeight.w500),
                ),
                subtitle: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const SizedBox(height: 4),
                    Text(
                      '${tr('quantity')}: ${item.quantity}',
                      style: TextStyle(color: Colors.grey[600]),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      '${tr('price')}: ${product?.price ?? 0} ${tr('saudi_riyal')}',
                      style: TextStyle(
                        color: Theme.of(context).primaryColor,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              );
            },
          ),
          Theme(
            data: Theme.of(context).copyWith(
              checkboxTheme: CheckboxThemeData(
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(4),
                ),
              ),
            ),
            child: CheckboxListTile(
              value: _includeCart,
              onChanged: (value) {
                setState(() {
                  _includeCart = value ?? false;
                });
              },
              title: Text(
                tr('activate_cart_with_order'),
                style: const TextStyle(
                  fontWeight: FontWeight.w600,
                  fontSize: 16,
                ),
              ),
              subtitle: Text(
                '${_cart!.items.length} ${tr('items')}',
                style: TextStyle(color: Colors.grey[600]),
              ),
              activeColor: Styles.primaryColor,
              contentPadding:
                  const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            ),
          ),
          if (_includeCart) ...[
            const Divider(height: 1),
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.grey[50],
                borderRadius: const BorderRadius.only(
                  bottomLeft: Radius.circular(12),
                  bottomRight: Radius.circular(12),
                ),
              ),
              child: Column(
                children: [
                  // Cart items summary with improved UI
                  // ListView.builder(
                  //   shrinkWrap: true,
                  //   physics: const NeverScrollableScrollPhysics(),
                  //   itemCount: min(_cart!.items.length, 3),
                  //   itemBuilder: (context, index) {
                  //     final item = _cart?.items[index];
                  //     return Container(
                  //       margin: const EdgeInsets.only(bottom: 12),
                  //       child: Row(
                  //         children: [
                  //           Container(
                  //             width: 45,
                  //             height: 45,
                  //             decoration: BoxDecoration(
                  //               color: Colors.white,
                  //               borderRadius: BorderRadius.circular(10),
                  //               boxShadow: [
                  //                 BoxShadow(
                  //                   color: Colors.grey.withOpacity(0.1),
                  //                   spreadRadius: 1,
                  //                   blurRadius: 2,
                  //                 ),
                  //               ],
                  //             ),
                  //             child: Center(
                  //               child: Text(
                  //                 '${item!.quantity}x',
                  //                 style: const TextStyle(
                  //                   fontWeight: FontWeight.bold,
                  //                   fontSize: 14,
                  //                 ),
                  //               ),
                  //             ),
                  //           ),
                  //           const SizedBox(width: 12),
                  //           // Expanded(
                  //           //   child: Column(
                  //           //     crossAxisAlignment: CrossAxisAlignment.start,
                  //           //     children: [
                  //           //       Text(
                  //           //         item.fullProduct?.name ?? '',
                  //           //         style: const TextStyle(
                  //           //           fontSize: 14,
                  //           //           fontWeight: FontWeight.w500,
                  //           //         ),
                  //           //         maxLines: 1,
                  //           //         overflow: TextOverflow.ellipsis,
                  //           //       ),
                  //           //       const SizedBox(height: 4),
                  //           //       Text(
                  //           //         '${((item.fullProduct?.price as num?) ?? 0)} ${tr('sr')}',
                  //           //         style: TextStyle(
                  //           //           fontSize: 13,
                  //           //           color: Colors.grey[600],
                  //           //         ),
                  //           //       ),
                  //           //     ],
                  //           //   ),
                  //           // ),
                  //         ],
                  //       ),
                  //     );
                  //   },
                  // ),

                  if ((_cart!.items.length) > 3) ...[
                    const Divider(),
                    Text(
                      '+ ${_cart!.items.length - 3} ${tr('more_items')}',
                      style: const TextStyle(
                        color: Colors.grey,
                        fontStyle: FontStyle.italic,
                      ),
                    ),
                  ],
                  const Divider(height: 24),
                  // Price breakdown
                  _buildPriceRow(tr('order_price'), orderPrice),
                  const SizedBox(height: 8),
                  _buildPriceRow(tr('cart_total'), cartTotal),
                  const Padding(
                    padding: EdgeInsets.symmetric(vertical: 12),
                    child: Divider(thickness: 1),
                  ),
                  _buildTotalRow(tr('total_to_pay'), _totalPrice),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildPriceRow(String label, double amount) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          label,
          style: TextStyle(
            fontSize: 14,
            color: Colors.grey[700],
          ),
        ),
        Text(
          '${amount.toStringAsFixed(2)} ${tr('sr')}',
          style: const TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w500,
          ),
        ),
      ],
    );
  }

  Widget _buildTotalRow(String label, double amount) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(10),
        border: Border.all(color: Styles.primaryColor.withAlpha(51)), // 0.2 * 255 = 51
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: const TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: 16,
            ),
          ),
          Text(
            '${amount.toStringAsFixed(2)} ${tr('sr')}',
            style: const TextStyle(
              fontWeight: FontWeight.bold,
              color: Styles.primaryColor,
              fontSize: 16,
            ),
          ),
        ],
      ),
    );
  }

  void _payWithCard() async {
    if (_cardPaymentService == null) {
      print('❌ Card payment service not initialized');
      NotificationUtils.showError(tr('payment_service_not_ready'));
      return;
    }

    final createOrderProvider = context.read<CreateOrderProvider>();
    final car = createOrderProvider.selectedCarType;
    final category = createOrderProvider.selectedOrderCategory;
    final price = _totalPrice;

    try {
      final success = await PaymentMethodScreen.startCardPayment(
        paymentAmount: price,
        paymentDescription: category?.name ?? '',
        context: context,
        cardPaymentService: _cardPaymentService!,
      );

      if (!success) {
        NotificationUtils.showError(tr('payment_failed'));
      }
    } catch (e) {
      print('❌ Card payment error: $e');
      NotificationUtils.showError(tr('payment_failed'));
    }
  }

// تحديث دالة _onPayPressed للتحقق من اختيار الوقت
// استبدل دالة _onPayPressed بالنسخة الجديدة
  void _onPayPressed() async {
    // التحقق من اختيار الوقت إذا تم اختيار التاريخ
    if (_scheduledDate != null && _selectedTimeSlot == null) {
      NotificationUtils.showError(tr('please_select_time_slot'));
      return;
    }

    final amount = _totalPrice; // Use combined total
    if (_selectedPaymentMethod == PaymentMethod.card) {
      _payWithCard();
    } else if (_selectedPaymentMethod == PaymentMethod.cash) {
      final user = context.read<UserProvider>().userModel;
      _handlePaymentSuccess(
        paymentMethod: K.paymentTypeCash,
        transactionID: '${user?.id}-${DateTime.now().millisecondsSinceEpoch}',
      );
    } else if (_selectedPaymentMethod == PaymentMethod.wallet) {
      _handleWalletPayment();
    } else if (_selectedPaymentMethod == PaymentMethod.package) {
      _handlePackagePayment();
    }
  }

  void _handlePackagePayment() async {
    BotToast.showLoading();
    final userPackages = await UserAPI().getUserPackages();
    BotToast.closeAllLoading();
    final createOrderProvider = context.read<CreateOrderProvider>();
    final selectedCarType = createOrderProvider.selectedCarType;
    final selectedOrderCategory = createOrderProvider.selectedOrderCategory;
    PackageItemModel? paymentPackage;
    try {
      paymentPackage = userPackages.firstWhere((element) {
        return element.category?.id == selectedOrderCategory?.id &&
            element.car?.id == selectedCarType?.id &&
            element.washes > 0;
      });
    } catch (_) {}
    if (paymentPackage == null) {
      NotificationUtils.showError(tr('error'));
    } else {
      final user = context.read<UserProvider>().userModel;
      _handlePaymentSuccess(
        paymentMethod: K.paymentTypePackage,
        transactionID: '${user?.id}-${DateTime.now().millisecondsSinceEpoch}',
      );
    }
  }

  void _handleWalletPayment() async {
    BotToast.showLoading();
    final wallet = await UserAPI().getWallet();
    BotToast.closeAllLoading();

    // Use combined total for wallet payment check
    final price = _totalPrice;
    if ((wallet?.balance ?? 0) < price) {
      return NotificationUtils.showError(tr('no_enough_wallet_balance'));
    }
    final user = context.read<UserProvider>().userModel;
    _handlePaymentSuccess(
      paymentMethod: K.paymentTypeWallet,
      transactionID: '${user?.id}-${DateTime.now().millisecondsSinceEpoch}',
    );
  }

  Future<void> _cleanupApplePayButton() async {
    if (_isApplePayButtonCreated && _mfApplePayButton != null) {
      print('🍏 Cleaning up Apple Pay button...');
      setState(() {
        _mfApplePayButton = null;
        _isApplePayButtonCreated = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final createOrderProvider = context.watch<CreateOrderProvider>();
    // final car = createOrderProvider.selectedCarType;
    final orderTotalPrice = createOrderProvider.orderTotalPrice;
    const xPadding = EdgeInsets.symmetric(horizontal: 18);
    final listItems = <Widget>[
      Padding(
        padding: xPadding,
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(tr('select_payment_method')),
            TextButton.icon(
              onPressed: () => _selectDate(context),
              icon: const Icon(Icons.calendar_today),
              label: Text(_scheduledDate != null
                  ? PaymentMethodScreen._dateFormat.format(_scheduledDate!)
                  : tr('schedule_order')),
            ),
          ],
        ),
      ),
      const SizedBox(height: 20),
      for (final method in widget.paymentMethods)
        _buildPaymentMethodWidget(method),
    ];

    // استخدام المتغيرات المهمة فقط
    final disablePaymentButton = _selectedPaymentMethod == null;
    return PopScope(
      canPop: true,
      onPopInvokedWithResult: (bool didPop, dynamic result) async {
        if (!didPop) return;
        if (ModalRoute.of(context)?.settings.name == '/cart') {
          await _loadCart(); // سيقوم بتحديث MyFatoorah إذا لزم الأمر
          if (mounted) setState(() {});
        }
      },
      child: Focus(
        focusNode: _focusNode,
        child: Scaffold(
          body: SafeArea(
            bottom: false,
            child: Column(
              children: [
                Expanded(
                  child: CustomScrollView(
                    slivers: [
                      SliverAppBar(
                        pinned: true,
                        floating: true,
                        elevation: 5,
                        shadowColor: Colors.grey[100],
                        backgroundColor: Colors.white,
                        centerTitle: false,
                        leading: IconButton(
                          icon: const Icon(Icons.arrow_back),
                          onPressed: () {
                            Navigator.pop(context);
                          },
                        ),
                        actions: [
                          _buildCartButton(), // Replace CartIconWithBadge with this
                        ],
                        automaticallyImplyLeading: false,
                        bottom: AppBar(
                          title: Text(
                            tr('payment'),
                            style: TextStyle(
                              color: Colors.black,
                              fontFamily: GoogleFonts.almarai().fontFamily,
                            ),
                          ),
                          centerTitle: false,
                          automaticallyImplyLeading: false,
                        ),
                      ),
                      // إخفاء ملخص الطلب إذا كان المستخدم قادمًا من صفحة الاشتراكات
                      if (!widget.isFromPackages)
                        SliverToBoxAdapter(
                          child: _buildOrderSummaryCard(),
                        ),
                      // إخفاء جدولة الطلب إذا كان المستخدم قادمًا من صفحة الاشتراكات
                      if (!widget.isFromPackages)
                        SliverToBoxAdapter(
                          child: _buildScheduleDateTimeCard(),
                        ),
                      SliverToBoxAdapter(
                        child: _buildSectionHeader(tr('payment_methods')),
                      ),
                      SliverList(
                        delegate: SliverChildBuilderDelegate(
                          (cxt, index) => Padding(
                            padding: const EdgeInsets.symmetric(horizontal: 16),
                            child: _buildPaymentMethodWidget(
                                widget.paymentMethods[index]),
                          ),
                          childCount: widget.paymentMethods.length,
                        ),
                      ),
                      if (_cart != null)
                        SliverToBoxAdapter(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              // _buildSectionHeader(tr('cart_items')),
                              _buildCartSection(),
                            ],
                          ),
                        ),
                      const SliverToBoxAdapter(
                        child: SizedBox(height: 100), // Bottom padding
                      ),
                    ],
                  ),
                ),
                Container(
                  decoration: BoxDecoration(
                    color: Colors.white,
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withAlpha(13), // 0.05 * 255 = 13
                        blurRadius: 10,
                        offset: const Offset(0, -5),
                      ),
                    ],
                  ),
                  child: SafeArea(
                    child: Padding(
                      padding: const EdgeInsets.all(16),
                      child: Row(
                        children: [
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Text(
                                  tr('total_amount'),
                                  style: const TextStyle(color: Colors.grey),
                                ),
                                const SizedBox(height: 4),
                                Text(
                                  '${_totalPrice.toStringAsFixed(2)} ${tr('currency')}',
                                  style: const TextStyle(
                                    fontSize: 20,
                                    fontWeight: FontWeight.bold,
                                    color: Styles.primaryColor,
                                  ),
                                ),
                              ],
                            ),
                          ),
                          SizedBox(
                            height: 48,
                            child: ElevatedButton(
                              onPressed:
                                  disablePaymentButton ? null : _onPayPressed,
                              style: ElevatedButton.styleFrom(
                                backgroundColor: Styles.secondaryColor,
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(12),
                                ),
                              ),
                              child: Text(tr('proceed_to_pay')),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildCartButton() {
    return IconButton(
      icon: const CartIconWithBadge(),
      onPressed: () async {
        // Navigate to cart screen and wait for pop with refresh flag
        final shouldRefresh =
            await Navigator.pushNamed(context, '/cart') as bool?;

        // Only refresh if flag is true and widget is mounted
        if (shouldRefresh == true && mounted) {
          BotToast.showLoading();
          try {
            await _handleCartUpdate();
            setState(() {});
          } catch (e) {
            print('Error refreshing cart: $e');
          } finally {
            BotToast.closeAllLoading();
          }
        }
      },
    );
  }

  Future<void> _handleCartUpdate() async {
    if (!mounted) return;

    BotToast.showLoading();
    try {
      await _loadCart();
      if (Platform.isIOS && _isSDKInitialized) {
        await _cleanupApplePayButton();
        await _initMFSession();
      }
      if (mounted) setState(() {});
    } catch (e) {
      print('❌ Error updating cart: $e');
    } finally {
      if (mounted) BotToast.closeAllLoading();
    }
  }
}

class PaymentMethodModel {
  final String name;
  final String? svgPath;
  final PaymentMethod method;

  const PaymentMethodModel(
      {required this.name, required this.svgPath, required this.method});
}
