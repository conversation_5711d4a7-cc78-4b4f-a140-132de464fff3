import 'dart:io';

import 'package:bot_toast/bot_toast.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:one_drop/helpers.dart';
import 'package:one_drop/providers/user_provider.dart';
import 'package:one_drop/styles.dart';
import 'package:provider/provider.dart';

import '../api/user_api.dart';
import '../constants.dart';
import '../models/country_phone_code_model.dart';
import '../widgets/border_input_form_field_with_label.dart';
import '../widgets/delete_account_dialog.dart';
import '../widgets/my_account_tab.dart';
import '../widgets/searchable_items_list_bottom_sheet.dart';

class EditAccountScreen extends StatefulWidget {
  static const routeName = 'edit-account';

  const EditAccountScreen({Key? key}) : super(key: key);

  @override
  State<EditAccountScreen> createState() => _EditAccountScreenState();
}

class _EditAccountScreenState extends State<EditAccountScreen> {
  final _formKey = GlobalKey<FormState>();
  final _nameFieldController = TextEditingController();
  final _phoneFieldController = TextEditingController();
  final _emailFieldController = TextEditingController();
  String? _phoneCountryCode;
  File? _selectedProfileImage;

  @override
  void initState() {
    super.initState();

    Future.delayed(Duration.zero, () {
      final userModel = context.read<UserProvider>().userModel;
      _nameFieldController.text = userModel?.name ?? '';
      _phoneFieldController.text = userModel?.phone ?? '';
      _emailFieldController.text = userModel?.email ?? '';
      _phoneCountryCode = userModel?.phoneCountryCode;
      setState(() {});
    });
  }

  @override
  void dispose() {
    _nameFieldController.dispose();
    _phoneFieldController.dispose();
    _emailFieldController.dispose();
    super.dispose();
  }

  void _onCountryCodePressed() async {
    final selectedCountryCode = await showModalBottomSheet<CountryPhoneCodeModel?>(
      context: context,
      isScrollControlled: true,
      builder: (cxt) => SearchableItemsListBottomSheet(
        filterList: (searchQ) async {
          return countryPhoneCodes.where((element) {
            return element.getName(context).toLowerCase().contains(searchQ.toLowerCase());
          }).toList();
        },
        hintText: '',
      ),
    );
    if (selectedCountryCode == null) return;
    _phoneCountryCode = selectedCountryCode.code;
    setState(() {});
  }

  void _onSavePressed() async {
    FocusScope.of(context).unfocus();
    final validForm = _formKey.currentState?.validate() ?? false;
    if (!validForm) return;
    final userModel = context.read<UserProvider>().userModel;
    BotToast.showLoading();
    final userMdoel = await UserAPI().updateProfile(
      name: _nameFieldController.text,
      email: _emailFieldController.text,
      cityID: userModel?.cityId,
      neighborhood: null,
      profileImage: _selectedProfileImage,
    );
    if (userMdoel == null) {
      BotToast.closeAllLoading();
      return Helpers.showErrorNotification();
    }
    await context.read<UserProvider>().setUserModel(userMdoel);
    BotToast.closeAllLoading();
    Helpers.showSuccessNotification(text: tr('profile_update_successfully'));
  }

  void _pickImagePressed() async {
    final selectedImage = await Helpers.showPickImageBottomSheet(context);
    if (selectedImage == null) return;
    _selectedProfileImage = selectedImage;
    setState(() {});
  }

  void _onDeleteAccountPressed() async {
    final agree = await showDialog<bool>(
      context: context,
      builder: (cxt) => const DeleteAccountDialog(),
    );
    if (agree ?? false) {
      _handleDeleteAccount();
    }
  }

  void _handleDeleteAccount() async {
    BotToast.showLoading();
    final success = await UserAPI().deleteAccount();
    BotToast.closeAllLoading();
    if (success) {
      await MyAccountTab.logoutPressed(context);
      Helpers.showSuccessNotification(text: tr('account_delete_success'));
    } else {
      Helpers.showErrorNotification();
    }
  }

  @override
  Widget build(BuildContext context) {
    final user = context.watch<UserProvider>().userModel;
    Widget profileImage;
    if (_selectedProfileImage != null) {
      profileImage = Image.file(
        _selectedProfileImage!,
        width: 100,
        height: 100,
        fit: BoxFit.cover,
      );
    } else if (user?.image != null) {
      profileImage = kReleaseMode
          ? CachedNetworkImage(
              imageUrl: user!.image!,
              width: 100,
              height: 100,
              errorWidget: (_, __, ___) => placeholderUserImage,
              placeholder: (_, __) => placeholderUserImage,
              fit: BoxFit.cover,
            )
          : SizedBox(
              width: 100,
              height: 100,
              child: placeholderUserImage,
            );
    } else {
      profileImage = const SizedBox(
        width: 100,
        height: 100,
        child: Icon(Icons.person, size: 60),
      );
    }
    const xPadding = EdgeInsets.symmetric(horizontal: 20);
    final listItems = <Widget>[
      Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Stack(
            children: [
              Padding(
                padding: const EdgeInsets.all(15.0),
                child: ClipOval(
                  child: profileImage,
                ),
              ),
              Positioned(
                top: 0,
                right: 0,
                child: SizedBox(
                  width: 40,
                  height: 40,
                  child: ElevatedButton(
                    style: ButtonStyle(
                      padding: MaterialStateProperty.all(const EdgeInsets.all(0)),
                      shape: MaterialStateProperty.all(
                        const CircleBorder(),
                      ),
                    ),
                    onPressed: _pickImagePressed,
                    child: const Icon(Icons.camera_alt_outlined),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
      const SizedBox(height: 40),
      Padding(
        padding: xPadding,
        child: BorderInputFormFieldWithLabel(
          controller: _nameFieldController,
          label: tr('name'),
          validator: Helpers.validateName,
        ),
      ),
      const SizedBox(height: 20),
      Padding(
        padding: xPadding,
        child: BorderInputFormFieldWithLabel(
          controller: _phoneFieldController,
          label: tr('phone'),
          validator: (value) {
            if (_phoneFieldController.text.trim().length >= 9) {
              return null;
            }
            return tr('please_enter_your_phone_number');
          },
          prefix: TextButton(
            child: Text(_phoneCountryCode ?? ''),
            onPressed: _onCountryCodePressed,
          ),
        ),
      ),
      const SizedBox(height: 20),
      Padding(
        padding: xPadding,
        child: BorderInputFormFieldWithLabel(
          controller: _emailFieldController,
          label: tr('email'),
          validator: Helpers.validateEmail,
        ),
      ),
      const SizedBox(height: 40),
      Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          SizedBox(
            width: 240,
            height: 40,
            child: ElevatedButton(
              style: ButtonStyle(
                shape: MaterialStateProperty.all(
                  RoundedRectangleBorder(
                    side: const BorderSide(color: Styles.primaryColor),
                    borderRadius: BorderRadius.circular(7),
                  ),
                ),
              ),
              onPressed: _onSavePressed,
              child: Text(tr('save')),
            ),
          ),
        ],
      ),
      const SizedBox(height: 20),
      Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          SizedBox(
            width: 240,
            height: 40,
            child: TextButton(
              onPressed: _onDeleteAccountPressed,
              child: Text(tr('delete_account')),
              style: ButtonStyle(
                foregroundColor: MaterialStateProperty.all(Colors.red),
                shape: MaterialStateProperty.all(
                  RoundedRectangleBorder(
                    side: const BorderSide(color: Colors.red),
                    borderRadius: BorderRadius.circular(7),
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
      const SizedBox(height: 20),
    ];
    return Scaffold(
      body: SafeArea(
        child: Form(
          key: _formKey,
          child: CustomScrollView(
            slivers: [
              SliverAppBar(
                pinned: true,
                floating: true,
                elevation: 5,
                shadowColor: Colors.grey[100],
                backgroundColor: Colors.white,
                title: Text(
                  tr('edit_account'),
                  style: Theme.of(context).textTheme.titleLarge,
                ),
                centerTitle: true,
                leading: IconButton(
                  icon: const Icon(Icons.arrow_back),
                  onPressed: () => Navigator.pop(context),
                ),
                automaticallyImplyLeading: false,
              ),
              SliverList(
                delegate: SliverChildListDelegate(listItems),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
