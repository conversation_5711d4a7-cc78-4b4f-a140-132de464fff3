import 'package:bot_toast/bot_toast.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:one_drop/helpers.dart';
import 'package:one_drop/services/prefs_service.dart';
import 'package:one_drop/services/service_locator.dart';

import '../models/payment_card_model.dart';
import '../styles.dart';
import '../widgets/border_input_form_field_with_label.dart';

class AddPaymentCardScreen extends StatefulWidget {
  static const routeName = 'add-payment-card';

  final bool showRememberCardButton;

  const AddPaymentCardScreen({Key? key, required this.showRememberCardButton}) : super(key: key);

  @override
  State<AddPaymentCardScreen> createState() => _AddPaymentCardScreenState();
}

class _AddPaymentCardScreenState extends State<AddPaymentCardScreen> {
  final _formkey = GlobalKey<FormState>();
  final _cardNumerFieldController = TextEditingController();
  final _cvvFieldController = TextEditingController();
  var _rememberCard = true;
  String? _selectedExpiryMonth;
  String? _selectedExpiryYear;

  @override
  void dispose() {
    _cardNumerFieldController.dispose();
    _cvvFieldController.dispose();
    super.dispose();
  }

  void _addCardPressed() async {
    final validForm = _formkey.currentState?.validate() ?? false;
    if (!validForm) return;
    final card = PaymentCardModel(
      cardNumber: _cardNumerFieldController.text.trim(),
      cvv: _cvvFieldController.text.trim(),
      expiryMonth: _selectedExpiryMonth!,
      expiryYear: _selectedExpiryYear!,
    );
    if (_rememberCard) {
      BotToast.showLoading();
      final success = await sl.get<PrefsService>().savePaymentCard(card);
      BotToast.closeAllLoading();
      if (!success) {
        return Helpers.showErrorNotification();
      }
    }
    Helpers.showSuccessNotification(text: tr('card_added_successfully'));
    Navigator.pop(context, card);
  }

  @override
  Widget build(BuildContext context) {
    const xPadding = EdgeInsets.symmetric(horizontal: 20);
    final listItems = <Widget>[
      const SizedBox(height: 10),
      Padding(
        padding: xPadding,
        child: Row(
          children: [
            Image.asset(
              'assets/images/33.png',
              width: 30,
              height: 20,
            ),
            const SizedBox(width: 20),
            Image.asset(
              'assets/images/32.png',
              width: 30,
              height: 20,
            ),
          ],
        ),
      ),
      const SizedBox(height: 20),
      Padding(
        padding: xPadding,
        child: BorderInputFormFieldWithLabel(
          controller: _cardNumerFieldController,
          label: tr('card_number'),
          hint: tr('enter_card_number'),
          keyboardType: TextInputType.number,
          validator: (value) {
            if (value?.trim().isEmpty ?? true) {
              return tr('please_enter_card_number');
            } else if ((value?.trim().length ?? 0) < 14) {
              return tr('please_enter_valid_card_number');
            }
            return null;
          },
        ),
      ),
      const SizedBox(height: 20),
      Padding(
        padding: xPadding,
        child: Row(
          children: [
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(tr('expiry_date')),
                  const SizedBox(height: 10),
                  Row(
                    children: [
                      Expanded(
                        child: DropdownButtonFormField<String>(
                          validator: (value) {
                            if (value?.isEmpty ?? true) {
                              return tr('please_select_expiry_month');
                            }
                            return null;
                          },
                          decoration: InputDecoration(
                            labelText: tr('month'),
                            border: kInputOutlineBorder,
                            enabledBorder: kInputOutlineBorder,
                            focusedBorder: kInputOutlineFocusedBorder,
                            errorMaxLines: 3,
                          ),
                          value: _selectedExpiryMonth,
                          onChanged: (value) {
                            setState(() {
                              _selectedExpiryMonth = value;
                            });
                          },
                          items: List.generate(12, (index) {
                            index = index + 1;
                            return DropdownMenuItem<String>(
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.end,
                                children: <Widget>[
                                  index > 9 ? Text('$index') : Text('0$index'),
                                ],
                              ),
                              value: index > 9 ? '$index' : '0$index',
                            );
                          }),
                        ),
                      ),
                      const SizedBox(width: 10),
                      Expanded(
                        child: DropdownButtonFormField<String>(
                          validator: (value) {
                            if (value?.isEmpty ?? true) {
                              return tr('please_select_expiry_year');
                            }
                            return null;
                          },
                          decoration: InputDecoration(
                            labelText: tr('year'),
                            border: kInputOutlineBorder,
                            enabledBorder: kInputOutlineBorder,
                            focusedBorder: kInputOutlineFocusedBorder,
                            errorMaxLines: 3,
                          ),
                          value: _selectedExpiryYear,
                          onChanged: (value) {
                            setState(() {
                              _selectedExpiryYear = value;
                            });
                          },
                          items: List.generate(20, (index) {
                            final year = DateTime.now().year + index;
                            final yearStr = year.toString().substring(2);
                            return DropdownMenuItem<String>(
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.end,
                                children: <Widget>[
                                  Text(yearStr),
                                ],
                              ),
                              value: yearStr,
                            );
                          }),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
              // child: BorderInputFormFieldWithLabel(
              //   label: tr('expiry-date'),
              //   hint: tr('month/year'),
              // ),
            ),
            const SizedBox(width: 20),
            Expanded(
              child: BorderInputFormFieldWithLabel(
                controller: _cvvFieldController,
                label: tr('cvv'),
                hint: tr('cvv_security_code'),
                maxLength: 3,
                validator: (value) {
                  if ((value?.trim().length ?? 0) < 3) {
                    return tr('please_enter_cvv');
                  }
                  return null;
                },
              ),
            ),
          ],
        ),
      ),
      if (widget.showRememberCardButton) const SizedBox(height: 20),
      if (widget.showRememberCardButton)
        Padding(
          padding: xPadding,
          child: Row(
            children: [
              Text(
                tr('remember_this_card'),
                style: const TextStyle(
                  color: Styles.secondaryColor,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const Spacer(),
              Switch.adaptive(
                value: _rememberCard,
                onChanged: (value) {
                  _rememberCard = value;
                  setState(() {});
                },
                trackColor: MaterialStateProperty.all(Styles.primaryColor),
                activeColor: Styles.primaryColor,
              ),
            ],
          ),
        ),
    ];
    return Scaffold(
      body: GestureDetector(
        onTap: () => FocusScope.of(context).unfocus(),
        child: SafeArea(
          child: Column(
            children: [
              Form(
                key: _formkey,
                child: Expanded(
                  child: CustomScrollView(
                    slivers: [
                      SliverAppBar(
                        pinned: true,
                        floating: true,
                        elevation: 5,
                        shadowColor: Colors.grey[100],
                        backgroundColor: Colors.white,
                        title: Text(
                          tr('add_new_card'),
                          style: Theme.of(context).textTheme.titleLarge,
                        ),
                        centerTitle: true,
                        leading: IconButton(
                          icon: const Icon(Icons.arrow_back),
                          onPressed: () => Navigator.pop(context),
                        ),
                        automaticallyImplyLeading: false,
                      ),
                      SliverList(
                        delegate: SliverChildListDelegate(listItems),
                      ),
                    ],
                  ),
                ),
              ),
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 20),
                child: SizedBox(
                  height: 50,
                  width: double.infinity,
                  child: ElevatedButton(
                    onPressed: _addCardPressed,
                    child: Text(tr('add_card')),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
