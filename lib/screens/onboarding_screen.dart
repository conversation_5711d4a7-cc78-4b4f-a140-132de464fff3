import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:one_drop/styles.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:one_drop/utils/notification_utils.dart';

import 'main_tab_controller.dart';
import 'sign_up_phone_number_screen.dart';

class OnboardingScreen extends StatelessWidget {
  static const routeName = 'onboarding';

  const OnboardingScreen({Key? key}) : super(key: key);

  Future<bool> _checkInternetConnection() async {
    var connectivityResult = await Connectivity().checkConnectivity();
    return connectivityResult != ConnectivityResult.none;
  }

  @override
  Widget build(BuildContext context) {
    const xPadding = EdgeInsets.symmetric(horizontal: 20);
    return Scaffold(
      backgroundColor: Styles.secondaryColor,
      body: AnnotatedRegion(
        value: SystemUiOverlayStyle.light,
        child: SafeArea(
          child: Column(
            children: [
              Expanded(
                child: Image.asset(
                  'assets/images/24.png',
                  width: double.infinity,
                  fit: BoxFit.contain,
                ),
              ),
              const SizedBox(height: 10),
              Padding(
                padding: xPadding,
                child: Row(
                  children: [
                    Text(
                      tr('easy_wash_your_car'),
                      style: Theme.of(context)
                          .textTheme
                          .headlineSmall
                          ?.copyWith(color: Colors.white),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 10),
              // Padding(
              //   padding: xPadding,
              //   child: Row(
              //     children: const [
              //       Text(
              //         'Some other text here',
              //         style: TextStyle(color: Colors.white),
              //       ),
              //     ],
              //   ),
              // ),
              // const SizedBox(height: 20),
              InkWell(
                onTap: () {
                  Navigator.pushNamed(
                      context, SignUpPhoneNumberScreen.routeName);
                },
                child: Container(
                  decoration: const BoxDecoration(
                    border: Border(
                      bottom: BorderSide(color: Colors.white, width: 1),
                    ),
                  ),
                  padding: const EdgeInsets.symmetric(vertical: 15),
                  margin: const EdgeInsets.symmetric(horizontal: 20),
                  child: Row(
                    children: [
                      const Text(
                        '+966',
                        style: TextStyle(color: Colors.white),
                      ),
                      const SizedBox(width: 20),
                      Text(
                        tr('enter_your_mobile_number_here'),
                        style: const TextStyle(color: Colors.grey),
                      ),
                    ],
                  ),
                ),
              ),
              const SizedBox(height: 20),
              Padding(
                padding: xPadding,
                child: Row(
                  children: [
                    Text(
                      tr('or'),
                      style: const TextStyle(color: Colors.white),
                    ),
                    TextButton(
                      child: Text(
                        tr('continue_as_guest'),
                        style: const TextStyle(
                          decoration: TextDecoration.underline,
                          color: Colors.white,
                        ),
                      ),
                      onPressed: () async {
                        try {
                          // تحقق من الاتصال بالإنترنت قبل المتابعة
                          final hasInternet = await _checkInternetConnection();

                          if (!hasInternet) {
                            // إذا لم يكن هناك اتصال بالإنترنت، أظهر رسالة للمستخدم
                            NotificationUtils.showError(
                                tr('no_internet_connection'));
                            return;
                          }

                          // في حالة وجود اتصال بالإنترنت، انتقل إلى الشاشة الرئيسية
                          Navigator.pushReplacementNamed(
                              context, MainTabController.routeName);
                        } catch (e) {
                          // التعامل مع أي أخطاء أخرى قد تحدث
                          NotificationUtils.showError(tr('error_occurred'));
                        }
                      },
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
