import 'package:bot_toast/bot_toast.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:one_drop/api/google_api.dart';
import 'package:one_drop/helpers.dart';
import 'package:one_drop/providers/create_order_provider.dart';
import 'package:one_drop/providers/user_provider.dart';
import 'package:provider/provider.dart';

import '../api/user_api.dart';
import '../constants.dart';
import '../models/car_type_model.dart';
import '../models/price_model.dart';
import '../styles.dart';
import '../widgets/sign_in_for_order_dialog.dart';
import 'create_order_select_location_map_screen.dart';
import 'create_order_summary_screen.dart';
import 'sign_up_phone_number_screen.dart';

class CreateOrderDetailsScreen extends StatefulWidget {
  static const routeName = 'create-order-details';

  const CreateOrderDetailsScreen({Key? key}) : super(key: key);

  @override
  State<CreateOrderDetailsScreen> createState() =>
      _CreateOrderDetailsScreenState();
}

class _CreateOrderDetailsScreenState extends State<CreateOrderDetailsScreen> {
  var _loading = true;
  List<PriceModel>? _prices;

  @override
  void initState() {
    super.initState();

    Future.delayed(Duration.zero, _initState);
  }

  void _initState() {
    _getCarTypes();
    _addAddressForUnauthUser();
  }

  void _getCarTypes() async {
    final createOrderProvider = context.read<CreateOrderProvider>();
    // createOrderProvider.clearAll();
    if (createOrderProvider.selectedOrderCategory == null) {
      _loading = false;
      setState(() {});
      return;
    }
    _prices = await UserAPI()
        .getPrices(categoryID: createOrderProvider.selectedOrderCategory!.id);
    _loading = false;
    setState(() {});
  }

  void _addAddressForUnauthUser() async {
    final isAuth = context.read<UserProvider>().isAuth;
    if (!isAuth) return;
    final selectedAddress = context.read<CreateOrderProvider>().selectedAddress;
    if (selectedAddress == null) {
      return Helpers.showErrorNotification();
    }
    if (selectedAddress.id != null) return;
    BotToast.showLoading();
    final googleAddress = await GoogleAPI().geocode(
      lat: double.parse(selectedAddress.lat),
      lng: double.parse(selectedAddress.lng),
    );
    final userAddress = await UserAPI().addAddress(
      lat: double.parse(selectedAddress.lat),
      lng: double.parse(selectedAddress.lng),
      name: selectedAddress.name,
      address: selectedAddress.address,
      city: (googleAddress?.isEmpty ?? true)
          ? ''
          : googleAddress?.first.cityOrNeighborhoodName ?? '',
    );
    BotToast.closeAllLoading();
    context.read<CreateOrderProvider>().selectedAddress = userAddress;
  }

  void _onEditAddressPressed() {
    Navigator.push(
      context,
      CupertinoPageRoute(
        builder: (cxt) =>
            const CreateOrderSelectLocationMapScreen(popOnConformPressed: true),
        settings: const RouteSettings(
            name: CreateOrderSelectLocationMapScreen.routeName),
      ),
    );
  }

  void _onContinuePressed() async {
    // Navigator.pushNamed(context, CreateOrderSummaryScreen.routeName);
    // return;
    final isAuth = context.read<UserProvider>().isAuth;
    if (isAuth) {
      Navigator.pushNamed(context, CreateOrderSummaryScreen.routeName);
    } else {
      final doSignIn = await showDialog<bool>(
        context: context,
        builder: (cxt) => const SignInForOrderDialog(),
      );
      if (doSignIn ?? false) {
        context.read<UserProvider>().nextRoute =
            CreateOrderDetailsScreen.routeName;
        Navigator.pushNamed(context, SignUpPhoneNumberScreen.routeName);
      }
    }
  }

  void _selectCarType(CarTypeModel? carType) {
    if (carType == null) return;
    final createOrderProvider = context.read<CreateOrderProvider>();
    createOrderProvider.selectedCarType = carType;
  }

  @override
  Widget build(BuildContext context) {
    final currentLang = EasyLocalization.of(context)?.locale.languageCode;
    final createOrderProvider = context.watch<CreateOrderProvider>();
    final selectedAddress = createOrderProvider.selectedAddress;
    final selectedCarType = createOrderProvider.selectedCarType;
    const padding = EdgeInsets.symmetric(horizontal: 20.0, vertical: 0);
    Widget child;
    if (_loading) {
      child = const Center(child: CircularProgressIndicator());
    } else if (_prices == null) {
      child = Center(child: Text(tr('error')));
    } else if (_prices!.isEmpty) {
      child = Center(child: Text(tr('no-car-types-found')));
    } else {
      child = Wrap(
        // padding: const EdgeInsets.symmetric(horizontal: 10),
        // scrollDirection: Axis.horizontal,
        // mainAxisAlignment: MainAxisAlignment.spaceAround,
        direction: Axis.horizontal,
        spacing: 10,
        runSpacing: 10,
        alignment: WrapAlignment.start,
        children: (currentLang == 'ar'
                ? (_prices ?? []).reversed.toList()
                : (_prices ?? []))
            .map<Widget>((e) {
          return OrderCarTypeItem(
            price: e.price.toInt().toString(),
            time:
                '${e.durationFromMinutes} - ${e.durationToMinutes} ${tr('minute')}',
            name: e.car?.name ?? '',
            selected: selectedCarType?.id == e.car?.id,
            image: e.car?.image ?? '',
            onPressed: () => _selectCarType(e.car),
          );
        }).toList(),
      );
    }
    return Scaffold(
      appBar: AppBar(
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => Navigator.pop(context),
        ),
      ),
      body: selectedAddress == null
          ? Center(child: Text(tr('error')))
          : Column(
              children: [
                const SizedBox(height: 20),
                Padding(
                  padding: padding,
                  child: Row(
                    children: [
                      Text(
                        tr('order_details'),
                        style: Theme.of(context).textTheme.titleLarge,
                      ),
                    ],
                  ),
                ),
                Expanded(
                  child: SingleChildScrollView(
                    padding: const EdgeInsets.symmetric(vertical: 20),
                    child: Column(
                      children: [
                        Padding(
                          padding: padding,
                          child: Container(
                            decoration: BoxDecoration(
                              border: Border.all(
                                  color: Colors.grey[300]!, width: 1),
                              borderRadius: BorderRadius.circular(10),
                            ),
                            padding: const EdgeInsets.symmetric(
                                vertical: 10, horizontal: 10),
                            child: Row(
                              children: [
                                Container(
                                  width: 40,
                                  height: 40,
                                  decoration: BoxDecoration(
                                    shape: BoxShape.circle,
                                    color: Styles.primaryColor.withOpacity(0.1),
                                  ),
                                  child: const Icon(Icons.location_on,
                                      color: Styles.primaryColor),
                                ),
                                const SizedBox(width: 20),
                                Expanded(
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        selectedAddress.name,
                                        style: Theme.of(context)
                                            .textTheme
                                            .labelLarge,
                                      ),
                                      const SizedBox(height: 10),
                                      Text(selectedAddress.address),
                                    ],
                                  ),
                                ),
                                TextButton(
                                  onPressed: _onEditAddressPressed,
                                  child: Text(tr('edit')),
                                  style: ButtonStyle(
                                    foregroundColor:
                                        MaterialStateProperty.all(Colors.black),
                                    textStyle: MaterialStateProperty.all(
                                      TextStyle(
                                        color: Colors.black,
                                        decoration: TextDecoration.underline,
                                        fontFamily:
                                            GoogleFonts.almarai().fontFamily,
                                      ),
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                        const SizedBox(height: 30),
                        Padding(
                          padding: padding,
                          child: Row(
                            children: [
                              Text(
                                tr('select_car_type'),
                                style: Theme.of(context).textTheme.labelLarge,
                              ),
                            ],
                          ),
                        ),
                        const SizedBox(height: 20),
                        child,
                      ],
                    ),
                  ),
                ),
                // const Spacer(),
                Padding(
                  padding: padding,
                  child: SizedBox(
                    width: 300,
                    child: ElevatedButton(
                      onPressed:
                          selectedCarType == null ? null : _onContinuePressed,
                      child: Text(tr('continue')),
                      style: ButtonStyle(
                        backgroundColor: MaterialStateProperty.all(
                          selectedCarType == null
                              ? Styles.secondaryColor.withOpacity(0.5)
                              : Styles.secondaryColor,
                        ),
                      ),
                    ),
                  ),
                ),
                const SizedBox(height: 20),
              ],
            ),
    );
  }
}

class OrderCarTypeItem extends StatelessWidget {
  const OrderCarTypeItem({
    Key? key,
    required this.name,
    required this.time,
    required this.price,
    required this.image,
    this.selected = false,
    this.onPressed,
  }) : super(key: key);

  final bool selected;
  final String time;
  final String price;
  final String image;
  final String name;
  final void Function()? onPressed;

  @override
  Widget build(BuildContext context) {
    final createOrderProvider = context.watch<CreateOrderProvider>();
    return InkWell(
      onTap: onPressed,
      child: Container(
        width: 120,
        height: 190,
        decoration: BoxDecoration(
          color: selected
              ? Styles.primaryColor.withOpacity(0.1)
              : Colors.grey[100],
          borderRadius: BorderRadius.circular(10),
          border: Border.all(
              color: selected ? Styles.primaryColor : Colors.transparent),
        ),
        padding: const EdgeInsets.symmetric(horizontal: 13, vertical: 10),
        child: Column(
          children: [
            SizedBox(
              width: 80,
              height: 80,
              child: ClipRRect(
                borderRadius: BorderRadius.circular(10),
                child: kReleaseMode
                    ? CachedNetworkImage(
                        imageUrl: image,
                        placeholder: (_, __) => kImagePlaceholder,
                        errorWidget: (_, __, ___) => kImagePlaceholder,
                      )
                    : SvgPicture.asset('assets/images/27.svg'),
              ),
            ),
            const SizedBox(height: 10),
            Text(name),
            const SizedBox(height: 5),
            Row(
              children: [
                const Icon(
                  Icons.timer_outlined,
                  color: Colors.grey,
                  size: 16,
                ),
                Text(
                  time,
                  style: Theme.of(context)
                      .textTheme
                      .labelSmall
                      ?.copyWith(color: Colors.grey),
                ),
              ],
            ),
            const SizedBox(height: 10),
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  price,
                  style: Theme.of(context).textTheme.labelLarge?.copyWith(
                        color: createOrderProvider.hasFreeWash
                            ? Colors.grey
                            : Styles.primaryColor,
                        decoration: createOrderProvider.hasFreeWash
                            ? TextDecoration.lineThrough
                            : null,
                      ),
                ),
                const SizedBox(width: 3),
                Text(
                  tr('sr'),
                  style: Theme.of(context).textTheme.labelSmall?.copyWith(
                        color: createOrderProvider.hasFreeWash
                            ? Colors.grey
                            : Styles.primaryColor,
                      ),
                ),
              ],
            ),
            if (createOrderProvider.hasFreeWash)
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    '0.0',
                    style: Theme.of(context).textTheme.labelLarge?.copyWith(
                          color: Styles.primaryColor,
                        ),
                  ),
                  const SizedBox(width: 3),
                  Text(
                    tr('sr'),
                    style: Theme.of(context).textTheme.labelSmall?.copyWith(
                          color: Styles.primaryColor,
                        ),
                  ),
                ],
              ),
          ],
        ),
      ),
    );
  }
}
