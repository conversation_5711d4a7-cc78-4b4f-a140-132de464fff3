import 'package:bot_toast/bot_toast.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:one_drop/api/user_api.dart';
import 'package:one_drop/helpers.dart';
import 'package:one_drop/styles.dart';

import '../constants.dart';
import '../models/order_model.dart';
import '../widgets/cancel_order_dialog.dart';
import '../widgets/order_cancelled_success_dialog.dart';
import '../widgets/order_progress_bar.dart';

class OrderDetailsScreen extends StatefulWidget {
  static const routeName = 'order-details';

  final OrderModel? order;

  const OrderDetailsScreen({
    Key? key,
    required this.order,
  }) : super(key: key);

  @override
  State<OrderDetailsScreen> createState() => _OrderDetailsScreenState();
}

const kPaymentMethodImage = <String, String>{
  K.paymentTypeWallet: 'assets/images/15.svg',
  K.paymentTypeCash: 'assets/images/30.svg',
  K.paymentTypeMada: 'assets/images/31.svg',
  K.paymentTypeApplePay: 'assets/images/applepay.svg',
  K.paymentTypeTarget: 'assets/images/22.svg',
  'card': 'assets/images/31.svg',
  'online': 'assets/images/31.svg',
  'mastercard': 'assets/images/31.svg',
};

class _OrderDetailsScreenState extends State<OrderDetailsScreen> {
  OrderModel? _orderDetails;

  @override
  void initState() {
    super.initState();

    _initState();
  }

  void _initState() async {
    if (widget.order?.id == null) return;
    _orderDetails = await UserAPI().getOrderDetails(orderID: widget.order!.id);
    // print(_orderDetails);
    setState(() {});
  }

  void _showCancelOrderDialog() async {
    final cancelOrder = await showDialog<bool>(
      context: context,
      builder: (cxt) => const CancelOrderDialog(),
    );
    if (cancelOrder ?? false) {
      _cancelOrder();
    }
  }

  void _cancelOrder() async {
    BotToast.showLoading();
    final order = await UserAPI().updateOrderStatus(
      orderID: widget.order!.id,
      status: 'cancelled',
    );
    BotToast.closeAllLoading();
    if (order != null) {
      showDialog<bool>(
        context: context,
        barrierDismissible: false,
        builder: (cxt) => OrderCancelledSuccessDialog(
          orderNumber: widget.order?.orderNumber ?? '',
        ),
      );
    } else {
      Helpers.showErrorNotification();
    }
  }

  @override
  Widget build(BuildContext context) {
    print(6454546);

    final category = widget.order?.category;
    final carType = widget.order?.carType;
    final discountAmount = widget.order?.coupon?.discount ?? 0;
    final discountPercentage = widget.order?.coupon?.percentage ?? 0;
    // print('--- widget.order?.paymentType ${widget.order?.id} ${widget.order?.paymentType}');
    final listItems = <Widget>[
      const SizedBox(height: 20),
      Container(
        width: double.infinity,
        margin: const EdgeInsets.symmetric(horizontal: 20),
        decoration: BoxDecoration(
          boxShadow: [kBoxShadow],
          color: Colors.white,
          borderRadius: BorderRadius.circular(10),
        ),
        padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 20),
        child: Column(
          children: [
            Row(
              children: [
                Text('${tr('order_number')}: '),
                Text(
                  '#${widget.order?.orderNumber}',
                  style: const TextStyle(color: Colors.grey),
                ),
              ],
            ),
            const SizedBox(height: 20),
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                SizedBox(
                  width: 45,
                  height: 45,
                  child: kReleaseMode
                      ? CachedNetworkImage(
                          imageUrl: category?.image ?? '',
                          placeholder: (_, __) => kImagePlaceholder,
                          errorWidget: (_, __, ___) => kImagePlaceholder,
                        )
                      : SvgPicture.asset('assets/images/5.svg'),
                ),
                const SizedBox(width: 20),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        category?.name ?? '',
                        style: Theme.of(context).textTheme.labelLarge,
                      ),
                      const SizedBox(height: 10),
                      Text(category?.desc ?? ''),
                    ],
                  ),
                ),
              ],
            ),
            const Divider(height: 40),
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Container(
                  decoration: BoxDecoration(
                    color: Styles.primaryColor.withOpacity(0.2),
                    shape: BoxShape.circle,
                  ),
                  width: 40,
                  height: 40,
                  alignment: Alignment.center,
                  child: kReleaseMode
                      ? CachedNetworkImage(
                          imageUrl: carType?.image ?? '',
                          placeholder: (_, __) => kImagePlaceholder,
                          errorWidget: (_, __, ___) => kImagePlaceholder,
                        )
                      : SvgPicture.asset(
                          'assets/images/26.svg',
                          width: 15,
                          height: 15,
                        ),
                ),
                const SizedBox(width: 20),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '${tr('select_car_type')}: ${carType?.name ?? ''}',
                      style: Theme.of(context).textTheme.labelLarge,
                    ),
                    const SizedBox(height: 10),
                    Text(
                      '${tr('from')} ${carType?.durationFromMinutes} ${tr('to')} ${carType?.durationToMinutes} ${tr('minute')}',
                      style: Theme.of(context)
                          .textTheme
                          .labelSmall
                          ?.copyWith(color: Colors.grey),
                    ),
                  ],
                ),
              ],
            ),
            const SizedBox(height: 30),
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Container(
                  decoration: BoxDecoration(
                    color: Styles.primaryColor.withOpacity(0.2),
                    shape: BoxShape.circle,
                  ),
                  width: 40,
                  height: 40,
                  alignment: Alignment.center,
                  child: const Icon(
                    Icons.location_on,
                    color: Styles.primaryColor,
                  ),
                ),
                const SizedBox(width: 20),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        tr('address_name'),
                        style: Theme.of(context).textTheme.labelLarge,
                      ),
                      const SizedBox(height: 10),
                      Text(widget.order?.address ?? ''),
                    ],
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
      const SizedBox(height: 40),
      OrderProgressBar(order: widget.order!),
      const SizedBox(height: 80),
      Padding(
        padding: const EdgeInsets.symmetric(horizontal: 20.0),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(tr('sub_total')),
            Text(
                '${widget.order?.costBeforeDiscount.toInt() ?? '0'} ${tr('saudi_riyal')}'),
          ],
        ),
      ),
      const SizedBox(height: 20),
      Padding(
        padding: const EdgeInsets.symmetric(horizontal: 20.0),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(tr('discount')),
            if (discountAmount > 0)
              Text('${discountAmount.toInt()} ${tr('saudi_riyal')}')
            else if (discountPercentage > 0)
              Text('${discountPercentage.toInt()} %')
            else
              Text('${discountAmount.toInt()} ${tr('saudi_riyal')}'),
          ],
        ),
      ),
      Divider(endIndent: 20, indent: 20, color: Colors.grey[400]!, height: 30),
      Padding(
        padding: const EdgeInsets.symmetric(horizontal: 20.0),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(tr('overall_total')),
            Text(
                '${widget.order?.finalCost.toInt() ?? '0'} ${tr('saudi_riyal')}'),
          ],
        ),
      ),
      const SizedBox(height: 30),
      Padding(
        padding: const EdgeInsets.symmetric(horizontal: 20.0),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              '${tr('paid_using')} ${tr(_orderDetails?.paymentType ?? '')}',
              style: Theme.of(context).textTheme.labelLarge,
            ),
            SizedBox(
              width: 40,
              height: 40,
              child: kPaymentMethodImage[_orderDetails?.paymentType] == null
                  ? null
                  : SvgPicture.asset(
                      kPaymentMethodImage[_orderDetails?.paymentType] ?? ''),
            ),
          ],
        ),
      ),
      const SizedBox(height: 30),
      if (widget.order?.status != K.orderStatusCancelled)
        Container(
          height: 45,
          padding: const EdgeInsets.symmetric(horizontal: 20.0),
          child: TextButton(
            onPressed: _showCancelOrderDialog,
            style: ButtonStyle(
              backgroundColor:
                  MaterialStateProperty.all(Colors.red.withOpacity(0.2)),
              foregroundColor: MaterialStateProperty.all(Colors.red),
              shape: MaterialStateProperty.all(
                RoundedRectangleBorder(borderRadius: BorderRadius.circular(7)),
              ),
            ),
            child: Text(tr('cancel_order')),
          ),
        ),
      const SizedBox(height: 30),
    ];
    return Scaffold(
      body: SafeArea(
        child: widget.order == null
            ? Center(child: Text(tr('error')))
            : CustomScrollView(
                slivers: [
                  SliverAppBar(
                    pinned: true,
                    floating: true,
                    elevation: 5,
                    shadowColor: Colors.grey[100],
                    backgroundColor: Colors.white,
                    title: Text(
                      tr('order_details'),
                      style: Theme.of(context).textTheme.titleLarge,
                    ),
                    centerTitle: true,
                    leading: IconButton(
                      icon: const Icon(Icons.arrow_back),
                      onPressed: () => Navigator.pop(context),
                    ),
                    automaticallyImplyLeading: false,
                  ),
                  SliverList(
                    delegate: SliverChildBuilderDelegate(
                      (cxt, index) {
                        return listItems[index];
                      },
                      childCount: listItems.length,
                    ),
                  ),
                ],
              ),
      ),
    );
  }
}
