import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:one_drop/providers/create_order_provider.dart';
import 'package:one_drop/screens/main_tab_controller.dart';
import 'package:one_drop/styles.dart';
import 'package:provider/provider.dart';

import '../models/order_model.dart';
import 'order_details_screen.dart';

class OrderConfirmationScreen extends StatelessWidget {
  static const routeName = 'order-confirmation';

  final OrderModel? order;

  const OrderConfirmationScreen({
    Key? key,
    required this.order,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final createOrderProvider = context.watch<CreateOrderProvider>();
    final hasFreeWash = createOrderProvider.hasFreeWash;
    return Scaffold(
      appBar: AppBar(
        title: Text(
          tr('order_confirmation'),
          style: Theme.of(context).textTheme.titleLarge?.copyWith(color: Colors.black),
        ),
        centerTitle: true,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => Navigator.pop(context),
        ),
      ),
      body: SafeArea(
        child: Center(
          child: Column(
            children: [
              SizedBox(
                width: 200,
                height: 200,
                child: Image.asset('assets/images/25.png'),
              ),
              const Spacer(),
              Text(
                tr('order_confirmed_successfully'),
                style: Theme.of(context).textTheme.titleLarge,
              ),
              const SizedBox(height: 10),
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text('${tr('order_number')}: '),
                  Text(
                    '#${order?.orderNumber}',
                    style: const TextStyle(color: Colors.grey),
                  ),
                ],
              ),
              const Spacer(),
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(Icons.location_on, color: Styles.primaryColor),
                  Text(
                    tr('your_order_comes_to'),
                    style: Theme.of(context).textTheme.bodyMedium,
                  ),
                ],
              ),
              const SizedBox(height: 10),
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 20),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    // const SizedBox(width: 10),
                    Expanded(
                      child: Text(
                        order?.address ?? '',
                        textAlign: TextAlign.center,
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 50),
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    hasFreeWash ? '0.0 ' : '${order?.finalCost.toInt() ?? 0} ',
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                          color: Styles.primaryColor,
                        ),
                  ),
                  Text(tr('sr')),
                ],
              ),
              const Spacer(flex: 2),
              SizedBox(
                width: 240,
                child: ElevatedButton(
                  onPressed: () {
                    Navigator.pushAndRemoveUntil(
                      context,
                      CupertinoPageRoute(
                        builder: (cxt) => OrderDetailsScreen(order: order),
                        settings: const RouteSettings(name: OrderDetailsScreen.routeName),
                      ),
                      (route) => route.settings.name == MainTabController.routeName,
                    );
                  },
                  child: Text(tr('track_order')),
                  style: ButtonStyle(
                    backgroundColor: MaterialStateProperty.all(Styles.secondaryColor),
                    shape: MaterialStateProperty.all(
                      RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(7),
                      ),
                    ),
                  ),
                ),
              ),
              const SizedBox(height: 10),
              TextButton(
                onPressed: () {
                  Navigator.pushNamedAndRemoveUntil(
                    context,
                    MainTabController.routeName,
                    (route) => false,
                  );
                },
                child: Text(tr('go_home')),
                style: ButtonStyle(
                  foregroundColor: MaterialStateProperty.all(Styles.secondaryColor),
                  textStyle: MaterialStateProperty.all(
                    TextStyle(
                      decoration: TextDecoration.underline,
                      fontFamily: GoogleFonts.almarai().fontFamily,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ),
              const SizedBox(height: 10),
            ],
          ),
        ),
      ),
    );
  }
}
