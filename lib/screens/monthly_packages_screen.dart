import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:one_drop/api/user_api.dart';

import '../models/package_item_model.dart';
import '../widgets/package_list_item.dart';

class MonthlyPackagesScreen extends StatefulWidget {
  static const routeName = 'monthly_packages';

  final PackageItemModel? package;

  const MonthlyPackagesScreen({
    Key? key,
    required this.package,
  }) : super(key: key);

  @override
  State<MonthlyPackagesScreen> createState() => _MonthlyPackagesScreenState();
}

class _MonthlyPackagesScreenState extends State<MonthlyPackagesScreen> {
  var _loading = true;
  List<PackageItemModel>? _packages;

  @override
  void initState() {
    super.initState();

    _initState();
  }

  void _initState() async {
    if (widget.package != null) {
      _loading = false;
      setState(() {});
      return;
    }
    _packages = await UserAPI().getPackages();
    _loading = false;
    setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    List<Widget> listItems;
    if (widget.package != null) {
      listItems = <Widget>[
        PackageListItem(package: widget.package!),
        const SizedBox(height: 20),
      ];
    } else if (_loading) {
      listItems = <Widget>[
        const SizedBox(height: 20),
        const Center(child: CircularProgressIndicator()),
      ];
    } else if (_packages == null) {
      listItems = <Widget>[
        const SizedBox(height: 20),
        Center(child: Text(tr('error'))),
      ];
    } else if (_packages?.isEmpty ?? true) {
      listItems = <Widget>[
        const SizedBox(height: 20),
        Center(child: Text(tr('no_packages_found'))),
      ];
    } else {
      listItems = <Widget>[
        const SizedBox(height: 20),
        for (final package in _packages!) PackageListItem(package: package),
      ];
    }
    return Scaffold(
      body: SafeArea(
        child: CustomScrollView(
          slivers: [
            SliverAppBar(
              pinned: true,
              floating: true,
              elevation: 5,
              shadowColor: Colors.grey[100],
              backgroundColor: Colors.white,
              centerTitle: false,
              leading: IconButton(
                icon: const Icon(Icons.arrow_back),
                onPressed: () {
                  Navigator.pop(context);
                },
              ),
              automaticallyImplyLeading: false,
              bottom: AppBar(
                title: Text(
                  tr('monthly_packages'),
                  style: Theme.of(context).textTheme.titleLarge,
                ),
                centerTitle: false,
                automaticallyImplyLeading: false,
              ),
            ),
            SliverList(
              delegate: SliverChildBuilderDelegate(
                (cxt, index) {
                  return listItems[index];
                },
                childCount: listItems.length,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
