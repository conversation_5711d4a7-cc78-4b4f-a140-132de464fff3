import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:one_drop/api/user_api.dart';
import 'package:one_drop/models/wallet_model.dart';
import 'package:one_drop/screens/add_balance_screen.dart';
import 'package:one_drop/styles.dart';

import '../constants.dart';
import '../widgets/wallet_card_button.dart';
import 'all_transactions_screen.dart';
import 'send_gift_screen.dart';

class WalletScreen extends StatefulWidget {
  static const routeName = 'wallet';

  const WalletScreen({Key? key}) : super(key: key);

  @override
  State<WalletScreen> createState() => _WalletScreenState();
}

class _WalletScreenState extends State<WalletScreen> {
  var _loading = true;
  WalletModel? _wallet;

  @override
  void initState() {
    super.initState();

    _initState();
  }

  void _initState() async {
    _wallet = await UserAPI().getWallet();
    _loading = false;
    if (!mounted) return;
    setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    List<Widget> listItems;
    if (_loading) {
      listItems = [
        const SizedBox(height: 30),
        const Center(child: CircularProgressIndicator()),
      ];
    } else if (_wallet == null) {
      listItems = [
        const SizedBox(height: 30),
        Center(child: Text(tr('error'))),
      ];
    } else {
      final locale = EasyLocalization.of(context)?.currentLocale?.languageCode;
      listItems = <Widget>[
        const SizedBox(height: 10),
        Container(
          decoration: BoxDecoration(
            gradient: const LinearGradient(
              begin: Alignment.topRight,
              end: Alignment.bottomLeft,
              // stops: [0.5, 1],
              colors: [
                Styles.secondaryColor,
                Color(0xff00c2be),
              ],
            ),
            borderRadius: BorderRadius.circular(20),
            boxShadow: [kBoxShadow],
          ),
          padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 20),
          margin: const EdgeInsets.symmetric(horizontal: 20),
          child: Column(
            children: [
              Row(
                children: [
                  Text(
                    tr('current_wallet_balance'),
                    style: Theme.of(context)
                        .textTheme
                        .labelLarge
                        ?.copyWith(color: Colors.white),
                  ),
                ],
              ),
              const SizedBox(height: 30),
              Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Text(
                    _wallet?.balance.toStringAsFixed(2) ?? '0.0',
                    style: Theme.of(context)
                        .textTheme
                        .headlineLarge
                        ?.copyWith(color: Colors.white),
                  ),
                  const SizedBox(width: 10),
                  Text(
                    tr('saudi_riyal'),
                    style: Theme.of(context)
                        .textTheme
                        .labelMedium
                        ?.copyWith(color: Colors.white),
                  ),
                ],
              ),
              const SizedBox(height: 30),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceAround,
                children: [
                  WalletCardButton(
                    icon: SvgPicture.asset('assets/images/21.svg'),
                    text: tr('add_balance'),
                    onPressed: () {
                      Navigator.pushNamed(context, AddBalanceScreen.routeName);
                    },
                  ),
                  WalletCardButton(
                    icon: SvgPicture.asset('assets/images/20.svg'),
                    text: tr('send_gift'),
                    onPressed: () {
                      Navigator.pushNamed(context, SendGiftScreen.routeName);
                    },
                  ),
                ],
              ),
            ],
          ),
        ),
        // const SizedBox(height: 30),
        // const UserSavedCards(),
        const SizedBox(height: 30),
        Container(
          decoration: BoxDecoration(
            color: Colors.white,
            boxShadow: [kBoxShadow],
            borderRadius: BorderRadius.circular(10),
          ),
          padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 10),
          margin: const EdgeInsets.symmetric(horizontal: 20),
          child: Column(
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    tr('finacial_transactions'),
                    style: Theme.of(context)
                        .textTheme
                        .labelLarge
                        ?.copyWith(color: Colors.grey),
                  ),
                  TextButton(
                    onPressed: () {
                      Navigator.push(
                        context,
                        CupertinoPageRoute(
                          builder: (cxt) => AllTransactionsScreen(
                              transactions: _wallet?.transactions ?? []),
                          settings: const RouteSettings(
                              name: AllTransactionsScreen.routeName),
                        ),
                      );
                    },
                    child: Text(tr('show_all')),
                  ),
                ],
              ),
              ...List<Widget>.generate(_wallet?.transactions.length ?? 0,
                  (index) {
                final trans = _wallet!.transactions[index];
                return Column(
                  children: [
                    Padding(
                      padding: const EdgeInsets.symmetric(vertical: 5),
                      child: WalletFinancialTransactionItem(
                        amount: trans.amount?.toString() ?? '0',
                        date: trans.date == null
                            ? ''
                            : DateFormat.yMMMEd(locale).format(trans.date!),
                        icon: SvgPicture.asset(
                          trans.reason == K.transactionAddBalanaceReason
                              ? 'assets/images/32.svg'
                              : 'assets/images/33.svg',
                          width: 30,
                          height: 30,
                        ),
                        text: trans.reason == K.transactionAddBalanaceReason
                            ? tr('add_balance')
                            : tr('send_gift'),
                      ),
                    ),
                    if (index != (_wallet?.transactions.length ?? 0) - 1)
                      const Divider(),
                  ],
                );
              }),
            ],
          ),
        ),
        const SizedBox(height: 20),
      ];
    }
    return Scaffold(
      body: SafeArea(
        child: CustomScrollView(
          slivers: [
            SliverAppBar(
              pinned: true,
              floating: true,
              elevation: 5,
              shadowColor: Colors.grey[100],
              backgroundColor: Colors.white,
              title: Text(
                tr('wallet'),
                style: Theme.of(context).textTheme.titleLarge,
              ),
              leading: IconButton(
                icon: const Icon(Icons.arrow_back),
                onPressed: () => Navigator.pop(context),
              ),
              centerTitle: true,
              automaticallyImplyLeading: false,
            ),
            SliverList(
              delegate: SliverChildBuilderDelegate(
                (cxt, index) {
                  return listItems[index];
                },
                childCount: listItems.length,
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class WalletFinancialTransactionItem extends StatelessWidget {
  const WalletFinancialTransactionItem({
    Key? key,
    required this.text,
    required this.date,
    required this.amount,
    required this.icon,
  }) : super(key: key);

  final String text;
  final String date;
  final String amount;
  final Widget icon;

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Container(
          width: 50,
          height: 50,
          decoration: BoxDecoration(
            color: Styles.primaryColor.withOpacity(0.1),
            shape: BoxShape.circle,
          ),
          alignment: Alignment.center,
          child: icon,
        ),
        const SizedBox(width: 20),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                text,
                style: Theme.of(context).textTheme.labelLarge,
              ),
              const SizedBox(height: 10),
              Text(
                date,
                style: const TextStyle(color: Colors.grey),
              ),
            ],
          ),
        ),
        Text(
          amount,
          style: Theme.of(context).textTheme.headlineSmall,
        ),
        const SizedBox(width: 5),
        Text(tr('sr')),
      ],
    );
  }
}
