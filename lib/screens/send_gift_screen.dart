import 'package:bot_toast/bot_toast.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:one_drop/helpers.dart';
import 'package:one_drop/models/user_model.dart';
import 'package:one_drop/styles.dart';
import 'package:provider/provider.dart';

import '../api/user_api.dart';
import '../constants.dart';
import '../models/country_phone_code_model.dart';
import '../providers/user_provider.dart';
import '../widgets/searchable_items_list_bottom_sheet.dart';
import 'main_tab_controller.dart';
import 'payment_method_screen.dart';
import 'send_gift_success_screen.dart';

class SendGiftScreen extends StatefulWidget {
  static const routeName = 'send-gift';

  const SendGiftScreen({Key? key}) : super(key: key);

  @override
  State<SendGiftScreen> createState() => _SendGiftScreenState();
}

class _SendGiftScreenState extends State<SendGiftScreen> {
  final _formKey = GlobalKey<FormState>();
  final _phoneFieldController = TextEditingController();
  final _amountFieldController = TextEditingController();
  var _phoneCountryCode = '+966';
  UserModel? _receiverUser;

  @override
  void dispose() {
    _phoneFieldController.dispose();
    _amountFieldController.dispose();
    super.dispose();
  }

  void _onNextPressed() async {
    FocusScope.of(context).unfocus();
    final isValidForm = _formKey.currentState?.validate() ?? false;
    if (!isValidForm) return;
    final currentUser = context.read<UserProvider>().userModel;
    if (currentUser?.phone == _phoneFieldController.text.trim() && currentUser?.phoneCountryCode == _phoneCountryCode) {
      return Helpers.showErrorNotification(tr('please_enter_valid_receiver_phone'));
    }
    BotToast.showLoading();
    _receiverUser = await UserAPI().getUserByPhone(
      phone: _phoneFieldController.text.trim(),
      countryCode: _phoneCountryCode,
    );
    BotToast.closeAllLoading();
    if (_receiverUser == null) {
      return Helpers.showErrorNotification(tr('send_gift_user_not_found'));
    }
    final amount = double.tryParse(_amountFieldController.text) ?? 0;
    Navigator.push(
      context,
      CupertinoPageRoute(
        builder: (cxt) => PaymentMethodScreen(
          onPaymentSuccess: _handlePaymentSuccess,
          paymentAmount: amount,
          paymentDescription: '${tr('send_gift')} ${amount.toInt()} ${tr('to')} $_phoneCountryCode ${_phoneFieldController.text}',
          paymentMethods: const [
            K.paymentMethodWallet,
            K.paymentMethodCard,
            K.paymentMethodApplePay,
          ],
        ),
        settings: const RouteSettings(name: PaymentMethodScreen.routeName),
      ),
    );
    // final paymentSuccess = await PaymentMethodScreen.payWithCard(
    //   paymentAmount: amount,
    //   paymentDescription: tr('send_gift'),
    //   context: context,
    // );
    // if (paymentSuccess) {
    //   _handlePaymentSuccess();
    // } else {
    //   Helpers.showErrorNotification();
    // }
    // showModalBottomSheet<AddBalanceBottomSheetActions>(
    //   context: context,
    //   builder: (cxt) => AddBalanceBottomSheet(
    //     parentContext: context,
    //     title: tr('send_gift'),
    //     submitButtonText: tr('send_gift'),
    //     onSubmitButtonPressed: () {
    //       Navigator.pushNamedAndRemoveUntil(
    //         context,
    //         SendGiftSuccessScreen.routeName,
    //         (route) => route.settings.name == MainTabController.routeName,
    //       );
    //     },
    //   ),
    // );
  }

  void _handlePaymentSuccess({
    required PaymentMethod paymentMethod,
    required String transactionID,
  }) async {
    BotToast.showLoading();
    final user = context.read<UserProvider>().userModel;
    if (user == null || user.id == null) {
      BotToast.closeAllLoading();
      BotToast.showText(text: tr('something_went_wrong'));
      return;
    }
    
    try {
      final userAPI = UserAPI();
      await Future.wait<dynamic>([
        if (paymentMethod != PaymentMethod.wallet)
          userAPI.pay(
            userID: user.id!,
            paymentAmount: int.tryParse(_amountFieldController.text) ?? 0,
            purchasedItemID: null,
            paymentMethod: kMapPaymentMethodEnumToString[paymentMethod]!,
            purchasedItemType: K.purchasedItemTypeSendGift,
            transactionID: transactionID,
            receivedID: _receiverUser?.id.toString(),
          ),
        if (paymentMethod == PaymentMethod.wallet)
          userAPI.sendGift(
            phoneCountyCode: _phoneCountryCode,
            phone: _phoneFieldController.text,
            amount: double.tryParse(_amountFieldController.text) ?? 0,
          ),
      ]);
    } catch (_) {}
    BotToast.closeAllLoading();
    Navigator.pushAndRemoveUntil(
      context,
      CupertinoPageRoute(
        builder: (cxt) => SendGiftSuccessScreen(amount: double.tryParse(_amountFieldController.text) ?? 0),
        settings: const RouteSettings(name: SendGiftSuccessScreen.routeName),
      ),
      (route) => route.settings.name == MainTabController.routeName,
    );
  }

  void _onCountryCodePressed() async {
    final selectedCountryCode = await showModalBottomSheet<CountryPhoneCodeModel?>(
      context: context,
      isScrollControlled: true,
      builder: (cxt) => SearchableItemsListBottomSheet(
        filterList: (searchQ) async {
          return countryPhoneCodes.where((element) {
            return element.getName(context).toLowerCase().contains(searchQ.toLowerCase());
          }).toList();
        },
        hintText: '',
      ),
    );
    if (selectedCountryCode == null) return;
    _phoneCountryCode = selectedCountryCode.code;
    setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        centerTitle: true,
        title: Text(
          tr('send_gift'),
          style: TextStyle(fontFamily: GoogleFonts.almarai().fontFamily),
        ),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => Navigator.pop(context),
        ),
      ),
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 20.0, vertical: 20),
          child: Form(
            key: _formKey,
            child: Column(
              children: [
                const Spacer(),
                Container(
                  decoration: BoxDecoration(
                    color: Colors.white,
                    boxShadow: [kBoxShadow],
                    borderRadius: BorderRadius.circular(10),
                  ),
                  padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 20),
                  child: Column(
                    children: [
                      TextFormField(
                        controller: _amountFieldController,
                        autofocus: true,
                        keyboardType: const TextInputType.numberWithOptions(
                          decimal: true,
                          signed: false,
                        ),
                        textAlign: TextAlign.center,
                        style: Theme.of(context).textTheme.displaySmall?.copyWith(
                              color: Colors.black,
                              fontWeight: FontWeight.bold,
                            ),
                        decoration: const InputDecoration(
                          border: InputBorder.none,
                          enabledBorder: InputBorder.none,
                          focusedBorder: InputBorder.none,
                          disabledBorder: InputBorder.none,
                          errorBorder: InputBorder.none,
                        ),
                        validator: (value) {
                          final number = double.tryParse(value ?? '') ?? 0;
                          if (number == 0) {
                            return tr('please_enter_valid_number');
                          }
                          return null;
                        },
                      ),
                      Text(tr('saudi_riyal')),
                      const Divider(height: 40),
                      SizedBox(
                        width: 220,
                        child: TextFormField(
                          controller: _phoneFieldController,
                          autofocus: true,
                          keyboardType: TextInputType.number,
                          style: Theme.of(context).textTheme.bodyMedium,
                          validator: (value) {
                            if (value == null) {
                              return tr('please_enter_phone_number');
                            } else if (value.length < 9) {
                              return tr('please_enter_valid_phone_number');
                            }
                            return null;
                          },
                          onChanged: (_) {
                            setState(() {});
                          },
                          decoration: InputDecoration(
                            border: InputBorder.none,
                            enabledBorder: InputBorder.none,
                            focusedBorder: InputBorder.none,
                            disabledBorder: InputBorder.none,
                            errorBorder: InputBorder.none,
                            hintText: tr('enter_user_phone'),
                            hintStyle: Theme.of(context).textTheme.bodyMedium?.copyWith(color: Colors.grey),
                            prefixIcon: TextButton(
                              onPressed: _onCountryCodePressed,
                              child: Text(_phoneCountryCode),
                              style: ButtonStyle(
                                foregroundColor: MaterialStateProperty.all(Colors.black),
                              ),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                const Spacer(),
                SizedBox(
                  width: 300,
                  child: ElevatedButton(
                    onPressed: _phoneFieldController.text.trim().length >= 9 ? _onNextPressed : null,
                    child: Text(tr('next')),
                    style: ButtonStyle(
                      backgroundColor: MaterialStateProperty.all(
                        _phoneFieldController.text.trim().length >= 9 ? Styles.secondaryColor : Styles.secondaryColor.withOpacity(0.5),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
