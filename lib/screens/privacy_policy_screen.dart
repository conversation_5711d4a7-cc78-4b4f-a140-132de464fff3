import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:one_drop/api/user_api.dart';
import 'package:one_drop/models/privacy_policy_model.dart';

class PrivacyPolicyScreen extends StatefulWidget {
  const PrivacyPolicyScreen({Key? key}) : super(key: key);

  static const routeName = 'privacy-policy';

  @override
  State<PrivacyPolicyScreen> createState() => _PrivacyPolicyScreenState();
}

class _PrivacyPolicyScreenState extends State<PrivacyPolicyScreen> {
  var _loading = true;
  PrivacyPolicyModel? _model;

  @override
  void initState() {
    super.initState();

    _initState();
  }

  void _initState() async {
    _model = await UserAPI().getPrivacyPolicy();
    _loading = false;
    setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    Widget child;
    if (_loading) {
      child = const Center(child: CircularProgressIndicator());
    } else if (_model == null) {
      child = Center(child: Text(tr('error')));
    } else {
      child = SingleChildScrollView(
        padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 20),
        child: Row(
          children: [
            Expanded(
              child: Text(_model!.getLocalizedText(context)),
            ),
          ],
        ),
      );
    }
    return Scaffold(
      appBar: AppBar(
        title: Text(tr('terms_and_conditions')),
      ),
      body: SafeArea(child: child),
    );
  }
}
