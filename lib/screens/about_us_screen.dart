import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:one_drop/api/user_api.dart';
import 'package:one_drop/models/about_us_model.dart';

class AboutUsScreen extends StatefulWidget {
  const AboutUsScreen({Key? key}) : super(key: key);

  static const routeName = 'about-us';

  @override
  State<AboutUsScreen> createState() => _AboutUsScreenState();
}

class _AboutUsScreenState extends State<AboutUsScreen> {
  var _loading = true;
  AboutUsModel? _model;

  @override
  void initState() {
    super.initState();

    _initState();
  }

  void _initState() async {
    _model = await UserAPI().getAboutUs();
    _loading = false;
    setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    Widget child;
    if (_loading) {
      child = const Center(child: CircularProgressIndicator());
    } else if (_model == null) {
      child = Center(child: Text(tr('error')));
    } else {
      child = SingleChildScrollView(
        padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 20),
        child: Row(
          children: [
            Expanded(
              child: Text(_model!.getLocalizedTerms(context)),
            ),
          ],
        ),
      );
    }
    return Scaffold(
      appBar: AppBar(
        title: Text(tr('terms-and-conditions')),
      ),
      body: SafeArea(child: child),
    );
  }
}
