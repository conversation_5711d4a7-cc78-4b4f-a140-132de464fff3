import 'package:flutter/material.dart';
import 'package:one_drop/providers/user_provider.dart';
import 'package:one_drop/screens/onboarding_screen.dart';
import 'package:one_drop/styles.dart';
import 'package:provider/provider.dart';

import 'main_tab_controller.dart';

class SplashScreen extends StatefulWidget {
  static const routeName = 'splash';

  const SplashScreen({Key? key}) : super(key: key);

  @override
  State<SplashScreen> createState() => _SplashScreenState();
}

const kSplashAnimationDuration = Duration(milliseconds: 1500);

class _SplashScreenState extends State<SplashScreen> {
  var _startFirstAnimation = false;
  var _startSecondAnimation = false;
  var _firstAnimationOpacity = 0.0;
  var _secondAnimationOpacity = 0.0;

  @override
  void initState() {
    super.initState();

    // FirebaseMessaging.instance.getToken(); // .then((value) => print('--- token $value'));
    Future.delayed(Duration.zero, () {
      _startFirstAnimation = true;
      _firstAnimationOpacity = 1;
      setState(() {});
    });
    Future.delayed(kSplashAnimationDuration, () {
      _startSecondAnimation = true;
      _secondAnimationOpacity = 1;
      setState(() {});
    });
    Future.delayed(const Duration(seconds: 3), () {
      _goToInitialRoute();
    });
  }

  void _goToInitialRoute() async {
    final userProvider = context.read<UserProvider>();
    await userProvider.initUserModel();
    if (userProvider.userModel?.token == null) {
      Navigator.pushReplacementNamed(context, OnboardingScreen.routeName);
    } else {
      Navigator.pushReplacementNamed(context, MainTabController.routeName);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Styles.secondaryColor,
      body: LayoutBuilder(
        builder: (context, constraints) {
          const imageWidth = 200.0;
          const imageHeight = 100.0;
          final carTopOffset =
              (constraints.maxHeight * 0.5) - (imageHeight * 0.5);
          final carLeftOffset = (constraints.maxWidth * 0.5) -
              (imageWidth * 0.5); // constraints.maxWidth * 0.23;
          final nameStartTopOffset = carTopOffset + 150;
          final nameEndTopOffset = carTopOffset + 55;
          return Stack(
            children: [
              const SizedBox(
                width: double.infinity,
                height: double.infinity,
              ),
              AnimatedPositioned(
                curve: Curves.easeOutCubic,
                duration: kSplashAnimationDuration,
                top: carTopOffset,
                left: _startFirstAnimation ? carLeftOffset : -carLeftOffset,
                child: AnimatedOpacity(
                  curve: Curves.easeOutCubic,
                  duration: kSplashAnimationDuration,
                  opacity: _firstAnimationOpacity,
                  child: Image.asset(
                    'assets/images/logo-car.png',
                    width: imageWidth,
                    height: imageHeight,
                  ),
                ),
              ),
              AnimatedPositioned(
                curve: Curves.easeOutCubic,
                duration: kSplashAnimationDuration,
                top: _startSecondAnimation
                    ? nameEndTopOffset
                    : nameStartTopOffset,
                left: carLeftOffset,
                child: AnimatedOpacity(
                  curve: Curves.easeOutCubic,
                  duration: kSplashAnimationDuration,
                  opacity: _secondAnimationOpacity,
                  child: Image.asset(
                    'assets/images/logo-name.png',
                    width: imageWidth,
                    height: imageHeight,
                  ),
                ),
              ),
              AnimatedPositioned(
                curve: Curves.easeOutCubic,
                duration: kSplashAnimationDuration,
                bottom: _startSecondAnimation ? 0 : -100,
                left: 0,
                child: AnimatedOpacity(
                  curve: Curves.easeOutCubic,
                  duration: kSplashAnimationDuration,
                  opacity: _secondAnimationOpacity,
                  child: Image.asset(
                    'assets/images/splash-cloud.png',
                    width: imageWidth,
                  ),
                ),
              ),
            ],
          );
        },
      ),
    );
  }
}
