import 'package:bot_toast/bot_toast.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:one_drop/api/user_api.dart';
import 'package:one_drop/helpers.dart';
import 'package:one_drop/models/user_address_model.dart';
import 'package:one_drop/styles.dart';

import '../widgets/delete_address_dialog.dart';
import 'search_new_address_screen.dart';

class SavedAddressesScreen extends StatefulWidget {
  static const routeName = 'saved-addresses';

  const SavedAddressesScreen({Key? key}) : super(key: key);

  @override
  State<SavedAddressesScreen> createState() => _SavedAddressesScreenState();
}

class _SavedAddressesScreenState extends State<SavedAddressesScreen> {
  var _loading = true;
  List<UserAddressModel>? _addresses;

  @override
  void initState() {
    super.initState();

    _initState();
  }

  void _initState() async {
    if (!_loading) {
      _loading = true;
      setState(() {});
    }
    _addresses = await UserAPI().getUserAddresses();
    _loading = false;
    setState(() {});
  }

  void _addNewAddressPressed() {
    Navigator.pushNamed(context, SearchNewAddressScreen.routeName);
  }

  void _onDeleteAddressPressed(UserAddressModel address) async {
    final deleteAddress = await showDialog<bool>(
      context: context,
      builder: (cxt) => const DeleteAddressDialog(),
    );
    if (!(deleteAddress ?? false)) return;
    if (address.id == null) return;
    BotToast.showLoading();
    final success = await UserAPI().deleteAddress(id: address.id!);
    BotToast.closeAllLoading();
    if (success) {
      Helpers.showSuccessNotification(text: tr('address_deleted_successfully'));
      _initState();
    } else {
      Helpers.showErrorNotification();
    }
  }

  @override
  Widget build(BuildContext context) {
    List<Widget> listItems;
    if (_loading) {
      listItems = [
        const Padding(
          padding: EdgeInsets.only(top: 30.0),
          child: Center(child: CircularProgressIndicator()),
        )
      ];
    } else if (_addresses == null) {
      listItems = [
        Padding(
          padding: const EdgeInsets.only(top: 30.0),
          child: Center(child: Text(tr('error'))),
        )
      ];
    } else if (_addresses!.isEmpty) {
      listItems = [
        Padding(
          padding: const EdgeInsets.only(top: 30.0),
          child: Center(child: Text(tr('no_addresses_found'))),
        )
      ];
    } else {
      listItems = <Widget>[
        const SizedBox(height: 20),
        for (final address in (_addresses ?? <UserAddressModel>[]))
          Padding(
            padding: const EdgeInsets.only(bottom: 20.0, left: 20, right: 20),
            child: SavedAddressListItem(
              address: address,
              onDeletePressed: _onDeleteAddressPressed,
            ),
          ),
        Padding(
          padding: const EdgeInsets.only(bottom: 20.0, left: 20, right: 20),
          child: SizedBox(
            height: 50,
            child: TextButton.icon(
              onPressed: _addNewAddressPressed,
              icon: const Icon(Icons.add_circle_outline),
              label: Text(tr('add_new_address')),
              style: ButtonStyle(
                shape: MaterialStateProperty.all(
                  RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
                ),
                backgroundColor: MaterialStateProperty.all(Styles.primaryColor.withOpacity(0.1)),
              ),
            ),
          ),
        )
      ];
    }
    return Scaffold(
      body: SafeArea(
        child: CustomScrollView(
          slivers: [
            SliverAppBar(
              pinned: true,
              floating: true,
              elevation: 5,
              shadowColor: Colors.grey[100],
              backgroundColor: Colors.white,
              title: Text(
                tr('saved_addresses'),
                style: Theme.of(context).textTheme.titleLarge,
              ),
              centerTitle: true,
              leading: IconButton(
                icon: const Icon(Icons.arrow_back),
                onPressed: () => Navigator.pop(context),
              ),
              automaticallyImplyLeading: false,
            ),
            SliverList(
              delegate: SliverChildListDelegate(listItems),
            ),
          ],
        ),
      ),
    );
  }
}

class SavedAddressListItem extends StatelessWidget {
  const SavedAddressListItem({
    required this.address,
    required this.onDeletePressed,
    Key? key,
  }) : super(key: key);

  final UserAddressModel address;
  final void Function(UserAddressModel) onDeletePressed;

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 10),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(10),
        boxShadow: [kBoxShadow],
        color: Colors.white,
      ),
      child: Row(
        children: [
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: Styles.primaryColor.withOpacity(0.1),
            ),
            child: const Icon(Icons.location_on, color: Styles.primaryColor),
          ),
          const SizedBox(width: 10),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  address.name,
                  style: Theme.of(context).textTheme.labelLarge,
                ),
                const SizedBox(height: 5),
                Text(address.address),
              ],
            ),
          ),
          TextButton(
            onPressed: () => onDeletePressed(address),
            child: const Icon(Icons.delete_outline, color: Colors.red),
          ),
        ],
      ),
    );
  }
}
