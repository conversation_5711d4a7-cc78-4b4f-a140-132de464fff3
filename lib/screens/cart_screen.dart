import 'package:flutter/material.dart';
import 'package:one_drop/models/cart.dart';
import 'package:one_drop/services/cart_service.dart';
import 'package:one_drop/services/product_service.dart';
import 'package:one_drop/services/service_locator.dart';
import 'dart:math';
import 'package:easy_localization/easy_localization.dart';
import 'package:one_drop/services/cart_update_service.dart';

class CartScreen extends StatefulWidget {
  static const routeName = '/cart';

  const CartScreen({Key? key}) : super(key: key);

  @override
  State<CartScreen> createState() => _CartScreenState();
}

class _CartScreenState extends State<CartScreen> {
  final CartService _cartService = sl.get<CartService>();
  final ProductService _productService = sl.get<ProductService>();
  CartResponse? _cart;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadCart();
  }

  Future<void> _loadCart() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // Load cart
      final cart = await _cartService.getCart();

      // Load full product details for each cart item
      for (var item in cart.items) {
        try {
          final product = await _productService.getProduct(item.productId);
          item.fullProduct = product;
        } catch (e) {
          print('Failed to load product ${item.productId}: $e');
        }
      }

      setState(() {
        _cart = cart;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('error_loading_cart'.tr(args: [e.toString()])),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  Widget _buildBody() {
    if (_isLoading) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(),
            SizedBox(height: 16),
            Text('Loading your cart...'),
          ],
        ),
      );
    }

    if (_cart == null || _cart!.items.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.shopping_cart_outlined,
                size: 80, color: Colors.grey),
            const SizedBox(height: 24),
            Text(
              'cart_empty'.tr(),
              style: const TextStyle(fontSize: 18, fontWeight: FontWeight.w600),
            ),
            const SizedBox(height: 8),
            Text(
              'add_items_to_cart'.tr(),
              style: TextStyle(color: Colors.grey[600]),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            ElevatedButton.icon(
              onPressed: () => Navigator.of(context).pop(),
              icon: const Icon(Icons.shopping_bag_outlined),
              label: Text('continue_shopping'.tr()),
              style: ElevatedButton.styleFrom(
                padding:
                    const EdgeInsets.symmetric(horizontal: 32, vertical: 12),
              ),
            ),
          ],
        ),
      );
    }

    return Column(
      children: [
        _buildCartSummary(),
        Expanded(
          child: RefreshIndicator(
            onRefresh: _loadCart,
            child: ListView.separated(
              padding: const EdgeInsets.all(16),
              itemCount: _cart!.items.length,
              separatorBuilder: (context, index) => const SizedBox(height: 12),
              itemBuilder: (context, index) {
                final item = _cart!.items[index];
                return _buildCartItem(item);
              },
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildCartSummary() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'items_in_cart'.tr(args: [_cart!.items.length.toString()]),
                style:
                    const TextStyle(fontSize: 16, fontWeight: FontWeight.w500),
              ),
              Text(
                '${'total'.tr()}: ${_cart!.total.toStringAsFixed(2)} ${tr('saudi_riyal')}',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: Theme.of(context).primaryColor,
                ),
              ),
            ],
          ),
          // if (_cart!.discount != null && _cart!.discount! > 0) ...[
          //   const SizedBox(height: 8),
          //   Row(
          //     mainAxisAlignment: MainAxisAlignment.end,
          //     children: [
          //       const Icon(Icons.local_offer, size: 16, color: Colors.green),
          //       const SizedBox(width: 4),
          //       Text(
          //         '${'savings'.tr()}: \$${_cart!.discount!.toStringAsFixed(2)}',
          //         style: const TextStyle(
          //           color: Colors.green,
          //           fontWeight: FontWeight.w500,
          //         ),
          //       ),
          //     ],
          //   ),
        ],
      ),
    );
  }

  Widget _buildCartItem(CartItem item) {
    final product = item.fullProduct ?? item.product.toProduct();
    final bool isActive = true;

    return Dismissible(
      key: Key('cart_item_${item.productId}'),
      direction: DismissDirection.endToStart,
      background: Container(
        alignment: Alignment.centerRight,
        padding: const EdgeInsets.only(right: 20),
        color: Colors.red,
        child: const Icon(Icons.delete, color: Colors.white),
      ),
      confirmDismiss: (direction) async {
        return await showDialog<bool>(
              context: context,
              builder: (context) => AlertDialog(
                title: Text('remove_item'.tr()),
                content:
                    Text('remove_item_confirmation'.tr(args: [product.name])),
                actions: [
                  TextButton(
                    onPressed: () => Navigator.of(context).pop(false),
                    child: Text('cancel'.tr()),
                  ),
                  TextButton(
                    onPressed: () => Navigator.of(context).pop(true),
                    style: TextButton.styleFrom(foregroundColor: Colors.red),
                    child: Text('remove'.tr()),
                  ),
                ],
              ),
            ) ??
            false;
      },
      onDismissed: (direction) => _removeItem(item),
      child: Card(
        elevation: 2,
        margin: EdgeInsets.zero,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        child: Container(
          padding: const EdgeInsets.all(12),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              ClipRRect(
                borderRadius: BorderRadius.circular(8),
                child: Stack(
                  children: [
                    Image.network(
                      product.image,
                      width: 100,
                      height: 100,
                      fit: BoxFit.cover,
                      errorBuilder: (context, error, stackTrace) {
                        return Container(
                          width: 100,
                          height: 100,
                          color: Colors.grey[200],
                          child: const Icon(Icons.image_not_supported),
                        );
                      },
                    ),
                  ],
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                product.name,
                                style: const TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.bold,
                                ),
                                maxLines: 2,
                                overflow: TextOverflow.ellipsis,
                              ),
                              const SizedBox(height: 4),
                              Text(
                                product.brand.name,
                                style: TextStyle(
                                  color: Colors.grey[600],
                                  fontSize: 14,
                                ),
                              ),
                            ],
                          ),
                        ),
                        IconButton(
                          icon: const Icon(Icons.delete_outline),
                          color: Colors.red,
                          onPressed: () => _removeItem(item),
                          padding: EdgeInsets.zero,
                          constraints: const BoxConstraints(
                            minWidth: 32,
                            minHeight: 32,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              '${(product.price * item.quantity).toStringAsFixed(2)} ${tr('saudi_riyal')}',
                              style: const TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                                color: Colors.blue, // Use your theme color
                              ),
                            ),
                            if (item.quantity > 1)
                              Text(
                                '${product.price.toStringAsFixed(2)} ${tr('saudi_riyal')} ${tr('each')}',
                                style: TextStyle(
                                  color: Colors.grey[600],
                                  fontSize: 12,
                                ),
                              ),
                          ],
                        ),
                        Container(
                          decoration: BoxDecoration(
                            color: Colors.grey[100],
                            borderRadius: BorderRadius.circular(20),
                          ),
                          child: Row(
                            children: [
                              _buildQuantityButton(
                                icon: Icons.remove,
                                onPressed: isActive && !_isLoading
                                    ? () => _updateCartItemQuantity(
                                          item: item,
                                          increase: false,
                                        )
                                    : null,
                              ),
                              Container(
                                constraints: const BoxConstraints(minWidth: 40),
                                alignment: Alignment.center,
                                child: Text(
                                  '${item.quantity}',
                                  style: const TextStyle(
                                    fontSize: 16,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ),
                              _buildQuantityButton(
                                icon: Icons.add,
                                onPressed: isActive && !_isLoading
                                    ? () => _updateCartItemQuantity(
                                          item: item,
                                          increase: true,
                                        )
                                    : null,
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildQuantityButton({
    required IconData icon,
    VoidCallback? onPressed,
  }) {
    return Material(
      type: MaterialType.transparency,
      child: InkWell(
        onTap: onPressed,
        borderRadius: BorderRadius.circular(20),
        child: Padding(
          padding: const EdgeInsets.all(8.0),
          child: Icon(
            icon,
            size: 20,
            color: onPressed == null ? Colors.grey : Colors.black87,
          ),
        ),
      ),
    );
  }

  Future<void> _updateCartItemQuantity({
    required CartItem item,
    required bool increase,
  }) async {
    if (_isLoading) return;

    try {
      final currentQuantity = item.quantity;
      final newQuantity =
          increase ? currentQuantity + 1 : max(0, currentQuantity - 1);

      if (newQuantity <= 0) {
        await _removeItem(item);
        return;
      }

      // Calculate price change
      final product = item.fullProduct ?? item.product.toProduct();
      final priceChange = product.price * (newQuantity - currentQuantity);

      // Update locally first for immediate feedback
      setState(() {
        item.quantity = newQuantity;
        // Update cart total
        if (_cart != null) {
          _cart!.total = (_cart!.total + priceChange);
        }
      });

      // Update in backend
      await _cartService.updateItem(
        cartId: _cart!.id,
        productId: item.productId,
        quantity: newQuantity,
      );
    } catch (e) {
      // Revert local changes on error
      setState(() {
        final oldQuantity = item.quantity;
        final product = item.fullProduct ?? item.product.toProduct();

        // Revert quantity
        item.quantity = oldQuantity;

        // Revert total price
        if (_cart != null) {
          final priceDifference = product.price * (oldQuantity - item.quantity);
          _cart!.total = _cart!.total + priceDifference;
        }
      });

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('error_updating_cart'.tr()),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: () async {
        CartUpdateService().notifyCartUpdated();
        Navigator.pop(context, true); // Pass refresh flag
        return false;
      },
      child: Scaffold(
        appBar: AppBar(
          title: Text('cart'.tr()),
          centerTitle: true,
          leading: IconButton(
            icon: const Icon(Icons.arrow_back),
            onPressed: () {
              CartUpdateService().notifyCartUpdated();
              Navigator.pop(context, true); // Pass refresh flag
            },
          ),
          actions: [
            if (!_isLoading && (_cart?.items.length ?? 0) > 0)
              IconButton(
                icon: const Icon(Icons.refresh),
                onPressed: _loadCart,
                tooltip: 'refresh_cart'.tr(),
              ),
          ],
        ),
        body: _buildBody(),
        bottomNavigationBar: _cart != null ? _buildBottomBar() : null,
      ),
    );
  }

  Future<void> _removeItem(CartItem item) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Remove Item'),
        content: Text(
            'Are you sure you want to remove ${item.product.toProduct().name} from your cart?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: const Text('Remove'),
          ),
        ],
      ),
    );

    if (confirmed != true) return;

    setState(() => _isLoading = true);

    try {
      await _cartService.removeItem(item.productId);
      await _loadCart();
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Failed to remove item: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  Widget _buildBottomBar() {
    return SafeArea(
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.white,
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.05),
              blurRadius: 10,
              offset: const Offset(0, -5),
            ),
          ],
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'total'.tr(),
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  '${_cart!.total.toStringAsFixed(2)} ${tr('saudi_riyal')}',
                  style: TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                    color: Theme.of(context).primaryColor,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            // SizedBox(
            //   width: double.infinity,
            //   child: ElevatedButton(
            //     onPressed: _cart!.items.isNotEmpty ? _proceedToCheckout : null,
            //     style: ElevatedButton.styleFrom(
            //       padding: const EdgeInsets.symmetric(vertical: 16),
            //     ),
            //     child: const Text(
            //       'Proceed to Checkout',
            //       style: TextStyle(fontSize: 16),
            //     ),
            //   ),
            // ),
          ],
        ),
      ),
    );
  }

  void _proceedToCheckout() {
    // TODO: Implement checkout navigation
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Proceeding to checkout...')),
    );
  }
}
