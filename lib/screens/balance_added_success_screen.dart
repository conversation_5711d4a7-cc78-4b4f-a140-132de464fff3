import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:one_drop/screens/wallet_screen.dart';

import '../styles.dart';

class BalanceAddedSuccessScreen extends StatelessWidget {
  static const routeName = 'balance-added-success';

  final double? amount;

  const BalanceAddedSuccessScreen({
    Key? key,
    required this.amount,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => Navigator.pop(context),
        ),
      ),
      body: SafeArea(
        child: Center(
          child: Column(
            children: [
              const Spacer(),
              Sized<PERSON>ox(
                width: 200,
                height: 200,
                child: SvgPicture.asset('assets/images/22.svg'),
              ),
              const Spacer(),
              Text(
                tr('balance_added_successfully'),
                style: Theme.of(context).textTheme.titleLarge,
              ),
              const SizedBox(height: 10),
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    amount?.toInt().toString() ?? '0.0',
                    style: Theme.of(context).textTheme.titleLarge,
                  ),
                  const SizedBox(width: 10),
                  Text(tr('saudi_riyal')),
                ],
              ),
              const Spacer(),
              SizedBox(
                width: 300,
                child: ElevatedButton(
                  onPressed: () {
                    Navigator.pushReplacementNamed(context, WalletScreen.routeName);
                  },
                  child: Text(tr('back_to_wallet')),
                  style: ButtonStyle(
                    backgroundColor: MaterialStateProperty.all(Styles.secondaryColor),
                  ),
                ),
              ),
              TextButton(
                onPressed: () {
                  Navigator.pop(context);
                },
                child: Text(tr('go_home')),
                style: ButtonStyle(
                  foregroundColor: MaterialStateProperty.all(Styles.secondaryColor),
                  textStyle: MaterialStateProperty.all(
                    TextStyle(
                      decoration: TextDecoration.underline,
                      fontWeight: FontWeight.bold,
                      fontFamily: GoogleFonts.almarai().fontFamily,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
