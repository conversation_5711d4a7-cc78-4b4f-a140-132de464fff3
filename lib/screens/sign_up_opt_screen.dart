import 'package:bot_toast/bot_toast.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:one_drop/helpers.dart';
import 'package:one_drop/providers/user_provider.dart';
import 'package:one_drop/screens/main_tab_controller.dart';
import 'package:one_drop/styles.dart';
import 'package:pinput/pinput.dart';
import 'package:provider/provider.dart';

import '../API/auth_api.dart';

class SignUpOTPScreen extends StatefulWidget {
  static const routeName = 'sign-up-otp';

  const SignUpOTPScreen({
    Key? key,
    required this.phone,
    required this.countryCode,
    required this.successRoute,
  }) : super(key: key);

  final String? phone;
  final String? countryCode;
  final String? successRoute;

  @override
  State<SignUpOTPScreen> createState() => _SignUpOTPScreenState();
}

class _SignUpOTPScreenState extends State<SignUpOTPScreen> {
  final _otpPinController = TextEditingController();

  @override
  void dispose() {
    _otpPinController.dispose();
    super.dispose();
  }

  void _resendOTPCodePressed() async {
    BotToast.showLoading();
    final responseMessage = await AuthAPI().sendOTPCode(
      phone: widget.phone!,
      countryCode: widget.countryCode!,
    );
    BotToast.closeAllLoading();
    if (responseMessage != null) {
      Helpers.showSuccessNotification(text: tr('otp-code-sent-successfully'));
    } else {
      Helpers.showErrorNotification();
    }
  }

  void _onPinCodeCompleted(String value) async {
    BotToast.showLoading();
    final userModel = await AuthAPI().activateAccount(
      phone: widget.phone!,
      countryCode: widget.countryCode!,
      otpCode: value,
    );
    debugPrint(userModel.toString() + ''' userModel''');

    if (userModel != null) {
      await context.read<UserProvider>().setUserModel(userModel);
      if (widget.successRoute == MainTabController.routeName) {
        Navigator.pushNamedAndRemoveUntil(
          context,
          MainTabController.routeName,
          (route) => false,
        );
      } else {
        Navigator.pushReplacementNamed(context, widget.successRoute!);
      }
    } else {
      _otpPinController.clear();
      Helpers.showErrorNotification(tr('invalid-otp-code'));
    }
    BotToast.closeAllLoading();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        leading: IconButton(
          icon: const Icon(Icons.arrow_back_sharp),
          iconSize: 30,
          onPressed: () => Navigator.pop(context),
        ),
      ),
      body: SafeArea(
        child: widget.phone == null ||
                widget.countryCode == null ||
                widget.successRoute == null
            ? Center(child: Text(tr('error')))
            : Column(
                children: [
                  // Padding(
                  //   padding: const EdgeInsets.symmetric(horizontal: 8.0),
                  //   child: Row(
                  //     children: [
                  //       IconButton(
                  //         icon: const Icon(Icons.arrow_back_sharp),
                  //         iconSize: 30,
                  //         onPressed: () => Navigator.pop(context),
                  //       ),
                  //     ],
                  //   ),
                  // ),
                  Expanded(
                    child: SingleChildScrollView(
                      padding: const EdgeInsets.symmetric(horizontal: 20),
                      child: Column(
                        children: [
                          const SizedBox(height: 20),
                          Row(
                            children: [
                              Text(
                                tr('enter_otp'),
                                style: Theme.of(context)
                                    .textTheme
                                    .headlineSmall
                                    ?.copyWith(
                                      fontWeight: FontWeight.bold,
                                    ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 20),
                          Row(
                            children: [
                              Expanded(
                                child:
                                    Text(tr('enter_otp_sent_to_you_on_number')),
                              ),
                            ],
                          ),
                          const SizedBox(height: 10),
                          Row(
                            children: [
                              Expanded(
                                child: Text(
                                    '${widget.countryCode} ${widget.phone}'),
                              ),
                            ],
                          ),
                          const SizedBox(height: 20),
                          Pinput(
                            controller: _otpPinController,
                            length: 4,
                            autofocus: true,
                            pinContentAlignment: Alignment.center,
                            onCompleted: _onPinCodeCompleted,
                            defaultPinTheme: const PinTheme(
                              width: 100,
                              height: 100,
                              textStyle: TextStyle(fontSize: 30),
                              decoration: BoxDecoration(
                                color: Colors.transparent,
                                border: Border(
                                  bottom:
                                      BorderSide(color: Colors.black, width: 1),
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                  TextButton(
                    onPressed: _resendOTPCodePressed,
                    style: linkButtonStyle,
                    child: Text(tr('resend_otp_code')),
                  ),
                  const SizedBox(height: 20),
                ],
              ),
      ),
    );
  }
}
