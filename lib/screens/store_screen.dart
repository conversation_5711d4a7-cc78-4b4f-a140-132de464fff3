import 'dart:ui';
import 'package:flutter/material.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:one_drop/models/brand.dart';
import 'package:one_drop/models/product.dart';
import 'package:one_drop/services/service_locator.dart' as sl;
import 'package:one_drop/services/store_service.dart';
import 'package:intl/intl.dart';

class StoreScreen extends StatefulWidget {
  const StoreScreen({super.key});

  @override
  State<StoreScreen> createState() => _StoreScreenState();
}

class _StoreScreenState extends State<StoreScreen> {
  final _storeService = sl.sl.get<StoreService>();
  List<Product> _products = [];
  List<Brand> _brands = [];
  bool _isLoading = true;
  String? _searchQuery;
  Brand? _selectedBrand;
  String _sortBy = 'name';
  String _sortOrder = 'asc';

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final futures = await Future.wait([
        _storeService.getProducts(
          search: _searchQuery,
          brandId: _selectedBrand?.id,
          sortBy: _sortBy,
          sortOrder: _sortOrder,
          limit: 50,
        ),
        _storeService.getBrands(),
      ]);

      setState(() {
        _products = futures[0] as List<Product>;
        _brands = futures[1] as List<Brand>;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'store_error_loading'.tr(args: [e.toString()]),
            ),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _clearFilters() {
    setState(() {
      _searchQuery = null;
      _selectedBrand = null;
      _sortBy = 'name';
      _sortOrder = 'asc';
    });
    _loadData();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          'store_title'.tr(),
          style: const TextStyle(
            color: Colors.black,
            fontWeight: FontWeight.bold,
          ),
        ),
        backgroundColor: Colors.white,
        elevation: 0,
        bottom: PreferredSize(
          preferredSize: const Size.fromHeight(60),
          child: Padding(
            padding: const EdgeInsets.all(8.0),
            child: Row(
              children: [
                Expanded(
                  child: TextField(
                    textAlign: TextAlign.right,
                    decoration: InputDecoration(
                      hintText: 'store_search_hint'.tr(),
                      prefixIcon: const Icon(Icons.search),
                      border: const OutlineInputBorder(),
                    ),
                    onChanged: (value) {
                      setState(() {
                        _searchQuery = value.isEmpty ? null : value;
                      });
                      _loadData();
                    },
                  ),
                ),
                const SizedBox(width: 8),
                DropdownButton<Brand>(
                  value: _selectedBrand,
                  hint: Text('store_select_brand'.tr()),
                  alignment: AlignmentDirectional.centerEnd,
                  items: [
                    DropdownMenuItem<Brand>(
                      value: null,
                      child: Text('store_all_brands'.tr()),
                    ),
                    ..._brands
                        .toSet()
                        .toList()
                        .map((brand) => DropdownMenuItem<Brand>(
                              value: brand,
                              child: Text(brand.name),
                            )),
                  ],
                  onChanged: (Brand? brand) {
                    setState(() {
                      _selectedBrand = brand;
                    });
                    _loadData();
                  },
                ),
                const SizedBox(width: 8),
                PopupMenuButton<String>(
                  icon: const Icon(Icons.sort),
                  tooltip: 'store_sort_tooltip'.tr(),
                  onSelected: (String value) {
                    setState(() {
                      if (_sortBy == value) {
                        _sortOrder = _sortOrder == 'asc' ? 'desc' : 'asc';
                      } else {
                        _sortBy = value;
                        _sortOrder = 'asc';
                      }
                    });
                    _loadData();
                  },
                  itemBuilder: (BuildContext context) => [
                    PopupMenuItem(
                      value: 'name',
                      child: Text('store_sort_by_name'.tr()),
                    ),
                    PopupMenuItem(
                      value: 'price',
                      child: Text('store_sort_price_low_to_high'.tr()),
                    ),
                  ],
                ),
                if (_searchQuery != null || _selectedBrand != null)
                  IconButton(
                    icon: const Icon(Icons.clear),
                    tooltip: 'store_clear_filters'.tr(),
                    onPressed: _clearFilters,
                  ),
              ],
            ),
          ),
        ),
      ),
      body: _isLoading
          ? Center(child: CircularProgressIndicator())
          : _products.isEmpty
              ? Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const Icon(Icons.shopping_bag_outlined, size: 64),
                      const SizedBox(height: 16),
                      Text(
                        'store_no_products'.tr(),
                        style: Theme.of(context).textTheme.titleMedium,
                      ),
                    ],
                  ),
                )
              : GridView.builder(
                  padding: const EdgeInsets.all(16),
                  gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                    crossAxisCount: 2,
                    childAspectRatio: 0.75,
                    crossAxisSpacing: 16,
                    mainAxisSpacing: 16,
                  ),
                  itemCount: _products.length,
                  itemBuilder: (context, index) {
                    final product = _products[index];
                    return Card(
                      clipBehavior: Clip.antiAlias,
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          AspectRatio(
                            aspectRatio: 1,
                            child: Image.network(
                              product.image,
                              fit: BoxFit.cover,
                              errorBuilder: (context, error, stackTrace) =>
                                  const Center(
                                child: Icon(
                                  Icons.error_outline,
                                  size: 40,
                                  color: Colors.red,
                                ),
                              ),
                            ),
                          ),
                          Padding(
                            padding: const EdgeInsets.all(8.0),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  product.name,
                                  style:
                                      Theme.of(context).textTheme.titleMedium,
                                  maxLines: 1,
                                  overflow: TextOverflow.ellipsis,
                                ),
                                const SizedBox(height: 4),
                                Row(
                                  children: [
                                    Text(
                                      'store_product_brand'.tr(),
                                      style:
                                          Theme.of(context).textTheme.bodySmall,
                                    ),
                                    const SizedBox(width: 4),
                                    Expanded(
                                      child: Text(
                                        product.brand.name,
                                        style: Theme.of(context)
                                            .textTheme
                                            .bodySmall,
                                        maxLines: 1,
                                        overflow: TextOverflow.ellipsis,
                                      ),
                                    ),
                                  ],
                                ),
                                const SizedBox(height: 4),
                                Row(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  children: [
                                    Row(
                                      children: [
                                        Text(
                                          'store_product_price'.tr(),
                                          style: Theme.of(context)
                                              .textTheme
                                              .bodySmall,
                                        ),
                                        const SizedBox(width: 4),
                                        Text(
                                          NumberFormat.currency(
                                            symbol: 'sr'.tr(),
                                            decimalDigits: 2,
                                            locale: context.locale.toString(),
                                          ).format(product.price),
                                          style: Theme.of(context)
                                              .textTheme
                                              .titleMedium
                                              ?.copyWith(
                                                color: Theme.of(context)
                                                    .primaryColor,
                                              ),
                                        ),
                                      ],
                                    ),
                                    Text(
                                      product.quantity > 0
                                          ? 'store_product_quantity'.tr(
                                              args: ['${product.quantity}'])
                                          : 'store_product_out_of_stock'.tr(),
                                      style: Theme.of(context)
                                          .textTheme
                                          .bodySmall
                                          ?.copyWith(
                                            color: product.quantity > 0
                                                ? null
                                                : Colors.red,
                                          ),
                                    ),
                                  ],
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    );
                  },
                ),
    );
  }
}
