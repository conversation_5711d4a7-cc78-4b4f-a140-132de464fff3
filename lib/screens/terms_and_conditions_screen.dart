import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:one_drop/api/user_api.dart';
import 'package:one_drop/models/terms_and_conditions_model.dart';

class TermsAndConditionsScreen extends StatefulWidget {
  const TermsAndConditionsScreen({Key? key}) : super(key: key);

  static const routeName = 'terms-and-conditions';

  @override
  State<TermsAndConditionsScreen> createState() => _TermsAndConditionsScreenState();
}

class _TermsAndConditionsScreenState extends State<TermsAndConditionsScreen> {
  var _loading = true;
  TermsAndConditionsModel? _model;

  @override
  void initState() {
    super.initState();

    _initState();
  }

  void _initState() async {
    _model = await UserAPI().getTermsAndConditions();
    _loading = false;
    setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    Widget child;
    if (_loading) {
      child = const Center(child: CircularProgressIndicator());
    } else if (_model == null) {
      child = Center(child: Text(tr('error')));
    } else {
      child = SingleChildScrollView(
        padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 20),
        child: Row(
          children: [
            Expanded(
              child: Text(_model!.getLocalizedTerms(context)),
            ),
          ],
        ),
      );
    }
    return Scaffold(
      appBar: AppBar(
        title: Text(tr('terms_and_conditions')),
      ),
      body: SafeArea(child: child),
    );
  }
}
