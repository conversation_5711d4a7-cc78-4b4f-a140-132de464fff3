import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:one_drop/api/google_api.dart';

import '../models/google_address_model.dart';
import '../widgets/app_bar_search_field.dart';
import '../widgets/saved_address_list_item.dart';
import 'add_new_address_screen.dart';

class SearchNewAddressScreen extends StatefulWidget {
  static const routeName = 'search-new-address';

  const SearchNewAddressScreen({Key? key}) : super(key: key);

  @override
  State<SearchNewAddressScreen> createState() => _SearchNewAddressScreenState();
}

class _SearchNewAddressScreenState extends State<SearchNewAddressScreen> {
  final _loading = <bool>[];
  final _searchFieldController = TextEditingController();
  String? _searchText;
  List<GoogleAddressModel>? _results;

  @override
  void dispose() {
    _searchFieldController.dispose();
    super.dispose();
  }

  void _onSearchFieldChanged(String value) async {
    _searchText = value;
    if (value.trim().isEmpty) {
      _loading.clear();
      _results = null;
      setState(() {});
    } else if (value.trim().length > 3) {
      _loading.add(true);
      setState(() {});
      final resutls = await GoogleAPI().geocode(address: value.trim());
      if (value == _searchText) {
        _results = resutls;
      }
      if (_loading.isNotEmpty) {
        _loading.removeLast();
      }
    }
    setState(() {});
  }

  void _onAddressSelected(GoogleAddressModel address) async {
    final addedAddress = await Navigator.push(
      context,
      CupertinoPageRoute(
        builder: (cxt) => AddNewAddressScreen(
          lat: address.lat,
          lng: address.lng,
          name: address.name,
          address: address.formattedAddress,
          googleAddress: address,
        ),
        settings: const RouteSettings(name: AddNewAddressScreen.routeName),
      ),
    );
    if (addedAddress == null) return;
  }

  @override
  Widget build(BuildContext context) {
    List<Widget> listItems;
    if (_loading.isNotEmpty) {
      listItems = [
        const Padding(
          padding: EdgeInsets.only(top: 20.0),
          child: Center(child: CircularProgressIndicator()),
        ),
      ];
    } else if (_results?.isNotEmpty ?? false) {
      listItems = <Widget>[
        Container(
          width: double.infinity,
          color: Colors.grey[100],
          padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 20),
          child: Text(tr('search_result')),
        ),
        for (final address in (_results ?? <GoogleAddressModel>[]))
          Padding(
            key: ValueKey(address.placeID),
            padding: const EdgeInsets.symmetric(horizontal: 20.0, vertical: 20),
            child: SavedAddressListItem(
              isSaved: false,
              onSelected: () => _onAddressSelected(address),
              address: address.formattedAddress,
              name: address.name,
            ),
          ),
      ];
    } else if (_searchText?.isNotEmpty ?? false) {
      listItems = [
        Padding(
          padding: const EdgeInsets.only(top: 20.0),
          child: Center(child: Text('${tr('no_addresses_found_for')} $_searchText')),
        ),
      ];
    } else {
      listItems = [];
    }
    return Scaffold(
      body: SafeArea(
        child: CustomScrollView(
          slivers: [
            SliverAppBar(
              pinned: true,
              floating: true,
              elevation: 5,
              shadowColor: Colors.grey[100],
              backgroundColor: Colors.white,
              title: Text(
                tr('select_location'),
                style: Theme.of(context).textTheme.titleLarge,
              ),
              collapsedHeight: 80,
              centerTitle: true,
              leading: IconButton(
                icon: const Icon(Icons.arrow_back),
                onPressed: () => Navigator.pop(context),
              ),
              automaticallyImplyLeading: false,
              bottom: AppBarSearchField(
                controller: _searchFieldController,
                showClearButton: _searchFieldController.text.trim().isNotEmpty,
                autoFocus: true,
                onClearPressed: () {
                  _searchFieldController.text = '';
                  _onSearchFieldChanged('');
                },
                onChanged: (value) => _onSearchFieldChanged(value ?? ''),
              ),
            ),
            SliverList(
              delegate: SliverChildListDelegate(listItems),
            ),
          ],
        ),
      ),
    );
  }
}
