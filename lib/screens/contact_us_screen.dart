import 'package:bot_toast/bot_toast.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:one_drop/api/user_api.dart';
import 'package:one_drop/helpers.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:connectivity_plus/connectivity_plus.dart';

import '../styles.dart';

class ContactUsScreen extends StatefulWidget {
  static const routeName = 'contact-us';

  const ContactUsScreen({Key? key}) : super(key: key);

  @override
  State<ContactUsScreen> createState() => _ContactUsScreenState();
}

class _ContactUsScreenState extends State<ContactUsScreen> {
  final _formKey = GlobalKey<FormState>();
  final _nameFieldController = TextEditingController();
  final _messageFieldController = TextEditingController();

  // Static social media links
  static const String _instagramLink = 'https://www.instagram.com/qatrah_one';
  static const String _tiktokLink = 'https://www.tiktok.com/@qatrah_one';
  // Keep Facebook as a placeholder for future use if needed
  static const String _facebookLink = '';

  @override
  void initState() {
    super.initState();
    _checkInternetConnection();
  }

  @override
  void dispose() {
    _nameFieldController.dispose();
    _messageFieldController.dispose();
    super.dispose();
  }

  Future<void> _checkInternetConnection() async {
    var connectivityResult = await Connectivity().checkConnectivity();
    if (connectivityResult == ConnectivityResult.none) {
      if (mounted) {
        Helpers.showErrorNotification(tr('no_internet_connection'));
      }
    }
  }

  void _onSendPressed() async {
    FocusScope.of(context).unfocus();
    BotToast.showLoading();
    final success = await UserAPI().sendContactUsMessage(
      name: _nameFieldController.text,
      message: _messageFieldController.text,
    );
    BotToast.closeAllLoading();
    if (success) {
      _nameFieldController.text = '';
      _messageFieldController.text = '';
      _formKey.currentState?.reset();
      Helpers.showSuccessNotification(text: tr('message_sent_successfully'));
    } else {
      Helpers.showErrorNotification();
    }
  }

  // طريقة آمنة لفتح الروابط
  Future<void> _launchUrl(String? url) async {
    if (url == null || url.isEmpty) {
      Helpers.showErrorNotification(tr('invalid_link'));
      return;
    }

    try {
      // تأكد من أن الرابط يبدأ بـ http:// أو https://
      if (!url.startsWith('http://') && !url.startsWith('https://')) {
        url = 'https://$url';
      }

      final uri = Uri.parse(url);
      if (await canLaunchUrl(uri)) {
        await launchUrl(uri, mode: LaunchMode.externalApplication);
      } else {
        Helpers.showErrorNotification(tr('could_not_open_link'));
      }
    } catch (e) {
      debugPrint('Error launching URL: $e');
      Helpers.showErrorNotification(tr('invalid_link'));
    }
  }

  @override
  Widget build(BuildContext context) {
    final validForm = _formKey.currentState?.validate() ?? false;

    return Scaffold(
      appBar: AppBar(
        title: Text(tr('contact_us')),
        centerTitle: true,
      ),
      body: GestureDetector(
        onTap: () => FocusScope.of(context).unfocus(),
        child: SafeArea(
          child: SingleChildScrollView(
            padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 20),
            child: Form(
              key: _formKey,
              child: Column(
                children: [
                  Row(
                    children: [
                      Expanded(
                        child: Text(
                            tr('leave_a_message_now_and_we_will_contact_you')),
                      ),
                    ],
                  ),
                  const SizedBox(height: 20),
                  Row(
                    children: [
                      Expanded(
                        child: Text(tr('name')),
                      ),
                    ],
                  ),
                  const SizedBox(height: 10),
                  TextFormField(
                    controller: _nameFieldController,
                    validator: (value) {
                      if (value?.trim().isEmpty ?? true) {
                        return tr('please_enter_name');
                      }
                      return null;
                    },
                    onChanged: (value) {
                      setState(() {});
                    },
                    decoration: InputDecoration(
                      border: kInputOutlineBorder,
                      enabledBorder: kInputOutlineBorder,
                      focusedBorder: kInputOutlineFocusedBorder,
                    ),
                  ),
                  const SizedBox(height: 20),
                  TextFormField(
                    controller: _messageFieldController,
                    validator: (value) {
                      if (value?.trim().isEmpty ?? true) {
                        return tr('please_enter_message');
                      }
                      return null;
                    },
                    onChanged: (value) {
                      setState(() {});
                    },
                    maxLines: 6,
                    decoration: InputDecoration(
                      hintText: tr('what_you_want_to_tell_us'),
                      border: kInputOutlineBorder,
                      enabledBorder: kInputOutlineBorder,
                      focusedBorder: kInputOutlineFocusedBorder,
                    ),
                  ),
                  const SizedBox(height: 30),
                  SizedBox(
                    width: double.infinity,
                    child: ElevatedButton(
                      style: ButtonStyle(
                        backgroundColor: MaterialStateProperty.all(validForm
                            ? Styles.secondaryColor
                            : Styles.secondaryColor.withOpacity(0.5)),
                      ),
                      onPressed: validForm ? _onSendPressed : null,
                      child: Text(tr('send')),
                    ),
                  ),
                  const SizedBox(height: 60),
                  Column(
                    children: [
                      Text(tr('or_contact_us_through')),
                      const SizedBox(height: 10),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          // Instagram Icon
                          IconButton(
                            iconSize: 30,
                            icon: const FaIcon(
                              FontAwesomeIcons.instagram,
                              color: Color(0xFFE1306C), // Instagram brand color
                              size: 30,
                            ),
                            onPressed: () {
                              _launchUrl(_instagramLink);
                            },
                          ),
                          const SizedBox(width: 20),
                          // TikTok Icon
                          IconButton(
                            iconSize: 30,
                            onPressed: () {
                              _launchUrl(_tiktokLink);
                            },
                            icon: const FaIcon(
                              FontAwesomeIcons.tiktok,
                              color: Colors.black,
                              size: 30,
                            ),
                          ),
                          // Conditionally show Facebook only if link is provided
                          if (_facebookLink.isNotEmpty) ...[
                            const SizedBox(width: 20),
                            IconButton(
                              iconSize: 30,
                              icon: const FaIcon(
                                FontAwesomeIcons.facebook,
                                color:
                                    Color(0xFF1877F2), // Facebook brand color
                                size: 30,
                              ),
                              onPressed: () {
                                _launchUrl(_facebookLink);
                              },
                            ),
                          ],
                        ],
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
