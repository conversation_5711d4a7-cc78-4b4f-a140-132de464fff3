import 'package:bot_toast/bot_toast.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:one_drop/API/auth_api.dart';
import 'package:one_drop/helpers.dart';
import 'package:one_drop/screens/main_tab_controller.dart';
import 'package:one_drop/widgets/searchable_items_list_bottom_sheet.dart';

import '../constants.dart';
import '../models/country_phone_code_model.dart';
import 'sign_up_form_screen.dart';
import 'sign_up_opt_screen.dart';

class SignUpPhoneNumberScreen extends StatefulWidget {
  static const routeName = 'sign-up-phone-number';

  const SignUpPhoneNumberScreen({Key? key}) : super(key: key);

  @override
  State<SignUpPhoneNumberScreen> createState() => _SignUpPhoneNumberScreenState();
}

class _SignUpPhoneNumberScreenState extends State<SignUpPhoneNumberScreen> {
  final _phoneFieldController = TextEditingController();
  var _phoneCountryCode = '+966';

  @override
  void dispose() {
    super.dispose();

    _phoneFieldController.dispose();
  }

  void _onContinuePressed() async {
    FocusScope.of(context).unfocus();
    BotToast.showLoading();
    final responseMessage = await AuthAPI().sendOTPCode(
      phone: _phoneFieldController.text,
      countryCode: _phoneCountryCode,
    );
    BotToast.closeAllLoading();
    if (responseMessage == null) {
      Helpers.showErrorNotification();
    } else {
      String successRoute;
      if (responseMessage == K.otpCodeSentResponseMessage) {
        successRoute = SignUpFormScreen.routeName;
      } else {
        successRoute = MainTabController.routeName;
      }
      Navigator.push(
        context,
        CupertinoPageRoute(
          builder: (cxt) => SignUpOTPScreen(
            countryCode: _phoneCountryCode,
            phone: _phoneFieldController.text,
            successRoute: successRoute,
          ),
          settings: const RouteSettings(name: SignUpOTPScreen.routeName),
        ),
      );
    }
  }

  void _onCountryCodePressed() async {
    final selectedCountryCode = await showModalBottomSheet<CountryPhoneCodeModel?>(
      context: context,
      isScrollControlled: true,
      builder: (cxt) => SearchableItemsListBottomSheet(
        filterList: (searchQ) async {
          return countryPhoneCodes.where((element) {
            return element.getName(context).toLowerCase().contains(searchQ.toLowerCase());
          }).toList();
        },
        hintText: '',
      ),
    );
    if (selectedCountryCode == null) return;
    _phoneCountryCode = selectedCountryCode.code;
    setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        leading: IconButton(
          icon: const Icon(Icons.arrow_back_sharp),
          iconSize: 30,
          onPressed: () => Navigator.pop(context),
        ),
      ),
      body: SafeArea(
        child: Column(
          children: [
            Expanded(
              child: SingleChildScrollView(
                padding: const EdgeInsets.symmetric(horizontal: 20),
                child: Column(
                  children: [
                    // Row(
                    //   children: [
                    //     IconButton(
                    //       icon: const Icon(Icons.arrow_back),
                    //       onPressed: () => Navigator.pop(context),
                    //     ),
                    //   ],
                    // ),
                    const SizedBox(height: 30),
                    Row(
                      children: [
                        Text(
                          tr('enter_your_phone_number'),
                          style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                                fontWeight: FontWeight.bold,
                              ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 10),
                    Row(
                      children: [
                        Text(tr('enter_your_mobile_number_to_register_or_login')),
                      ],
                    ),
                    const SizedBox(height: 30),
                    TextFormField(
                      controller: _phoneFieldController,
                      autofocus: true,
                      keyboardType: TextInputType.phone,
                      onEditingComplete: () {
                        if (_phoneFieldController.text.length >= 9) {
                          _onContinuePressed();
                        }
                      },
                      onChanged: (value) {
                        setState(() {});
                      },
                      decoration: InputDecoration(
                        prefixIcon: TextButton(
                          child: Text(_phoneCountryCode),
                          onPressed: _onCountryCodePressed,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
            SizedBox(
              width: 300,
              child: ElevatedButton(
                onPressed: _phoneFieldController.text.length >= 9 ? _onContinuePressed : null,
                child: Text(tr('continue')),
              ),
            ),
            const SizedBox(height: 20),
          ],
        ),
      ),
    );
  }
}
