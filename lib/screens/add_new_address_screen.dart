import 'package:bot_toast/bot_toast.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:one_drop/api/user_api.dart';
import 'package:one_drop/helpers.dart';
import 'package:one_drop/screens/main_tab_controller.dart';

import '../models/google_address_model.dart';
import '../styles.dart';
import '../widgets/border_input_form_field_with_label.dart';
import 'saved_addresses_screen.dart';

class AddNewAddressScreen extends StatefulWidget {
  static const routeName = 'add-new-address';

  const AddNewAddressScreen({
    Key? key,
    required this.lat,
    required this.lng,
    required this.name,
    required this.address,
    required this.googleAddress,
  }) : super(key: key);

  final double? lat;
  final double? lng;
  final String? name;
  final String? address;
  final GoogleAddressModel? googleAddress;

  @override
  State<AddNewAddressScreen> createState() => _AddNewAddressScreenState();
}

class _AddNewAddressScreenState extends State<AddNewAddressScreen> {
  final _nameFieldController = TextEditingController();
  final _addressFieldController = TextEditingController();

  @override
  void initState() {
    super.initState();

    _nameFieldController.text = widget.name ?? '';
    _addressFieldController.text = widget.address ?? '';
  }

  @override
  void dispose() {
    _nameFieldController.dispose();
    _addressFieldController.dispose();
    super.dispose();
  }

  void _onSavePressed() async {
    BotToast.showLoading();
    final address = await UserAPI().addAddress(
      lat: widget.lat!,
      lng: widget.lng!,
      name: _nameFieldController.text.trim(),
      address: _addressFieldController.text.trim(),
      city: widget.googleAddress?.cityOrNeighborhoodName ?? '',
    );
    BotToast.closeAllLoading();
    if (address != null) {
      Navigator.pushNamedAndRemoveUntil(
        context,
        SavedAddressesScreen.routeName,
        (route) => route.settings.name == MainTabController.routeName,
      );
    } else {
      Helpers.showErrorNotification();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(tr('add-new-address')),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => Navigator.pop(context),
        ),
      ),
      body: SafeArea(
        child: widget.address == null || widget.name == null || widget.lat == null || widget.lng == null
            ? Center(child: Text(tr('error')))
            : Padding(
                padding: const EdgeInsets.symmetric(vertical: 20.0, horizontal: 20),
                child: Column(
                  children: [
                    BorderInputFormFieldWithLabel(
                      controller: _nameFieldController,
                      label: tr('name'),
                    ),
                    const SizedBox(height: 20),
                    BorderInputFormFieldWithLabel(
                      controller: _addressFieldController,
                      label: tr('address'),
                    ),
                    const Spacer(),
                    SizedBox(
                      width: 300,
                      child: ElevatedButton(
                        onPressed: _onSavePressed,
                        child: Text(tr('save-address')),
                        style: ButtonStyle(
                          backgroundColor: MaterialStateProperty.all(Styles.secondaryColor),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
      ),
    );
  }
}
