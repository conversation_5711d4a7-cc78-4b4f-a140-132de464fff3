import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:one_drop/api/user_api.dart';
import 'package:one_drop/models/notification_model.dart';
import 'package:one_drop/styles.dart';

import 'order_details_screen.dart';

class NotificationsScreen extends StatefulWidget {
  static const routeName = 'notifications';

  const NotificationsScreen({Key? key}) : super(key: key);

  @override
  State<NotificationsScreen> createState() => _NotificationsScreenState();
}

class _NotificationsScreenState extends State<NotificationsScreen> {
  var _loading = true;
  List<NotificationModel>? _notifications;

  @override
  void initState() {
    super.initState();

    _initState();
  }

  void _initState() async {
    _notifications = await UserAPI().getNotifications();
    _loading = false;
    setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    List<Widget> listItems;
    if (_loading) {
      listItems = <Widget>[
        const Padding(
          padding: EdgeInsets.symmetric(vertical: 30),
          child: Center(child: CircularProgressIndicator()),
        ),
      ];
    } else if (_notifications == null) {
      listItems = <Widget>[
        Padding(
          padding: const EdgeInsets.symmetric(vertical: 30),
          child: Center(child: Text(tr('error'))),
        ),
      ];
    } else if (_notifications!.isEmpty) {
      listItems = <Widget>[
        Padding(
          padding: const EdgeInsets.symmetric(vertical: 30),
          child: Center(child: Text(tr('no_notifications_found'))),
        ),
      ];
    } else {
      listItems = <Widget>[
        const SizedBox(height: 20),
        for (final notification in _notifications!)
          Padding(
            padding: const EdgeInsets.only(bottom: 20.0, left: 20, right: 20),
            child: NotificationListItem(
              isSeen: true,
              notification: notification,
            ),
          ),
        const SizedBox(height: 20),
      ];
    }
    return Scaffold(
      body: SafeArea(
        bottom: false,
        child: CustomScrollView(
          slivers: [
            SliverAppBar(
              pinned: true,
              floating: true,
              elevation: 5,
              shadowColor: Colors.grey[100],
              backgroundColor: Colors.white,
              title: Text(
                tr('notifications'),
                style: Theme.of(context).textTheme.titleLarge,
              ),
              centerTitle: true,
              leading: IconButton(
                icon: const Icon(Icons.arrow_back),
                onPressed: () => Navigator.pop(context),
              ),
              automaticallyImplyLeading: false,
            ),
            SliverList(
              delegate: SliverChildBuilderDelegate(
                (cxt, index) {
                  return listItems[index];
                },
                childCount: listItems.length,
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class NotificationListItem extends StatelessWidget {
  const NotificationListItem({
    Key? key,
    required this.isSeen,
    required this.notification,
  }) : super(key: key);

  final bool isSeen;
  final NotificationModel notification;

  void _onPressed(BuildContext context) {
    if (notification.order == null) return;
    Navigator.push(
      context,
      CupertinoPageRoute(
        builder: (cxt) => OrderDetailsScreen(order: notification.order),
        settings: const RouteSettings(name: OrderDetailsScreen.routeName),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final decoration = BoxDecoration(
      color: isSeen ? Colors.white : Styles.primaryColor.withOpacity(0.1),
      borderRadius: BorderRadius.circular(10),
    );
    return InkWell(
      onTap: () => _onPressed(context),
      child: Container(
        decoration: decoration.copyWith(
          color: Colors.white,
          boxShadow: [kBoxShadow],
        ),
        child: Container(
          width: double.infinity,
          padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 10),
          decoration: decoration,
          child: Column(
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    notification.title,
                    style: Theme.of(context).textTheme.labelLarge,
                  ),
                  Text(
                    notification.time,
                    style: const TextStyle(color: Styles.primaryColor),
                  ),
                ],
              ),
              const SizedBox(height: 10),
              Row(
                children: [
                  Text(
                    notification.body,
                    style: const TextStyle(color: Colors.grey),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}
