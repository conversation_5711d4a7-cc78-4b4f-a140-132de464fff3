import 'package:bot_toast/bot_toast.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:one_drop/providers/user_provider.dart';
import 'package:one_drop/widgets/common_app_bar.dart';
import 'package:provider/provider.dart';

import '../api/google_api.dart';
import '../api/user_api.dart';
import '../helpers.dart';
import '../models/google_address_model.dart';
import '../models/user_address_model.dart';
import '../widgets/app_bar_search_field.dart';
import '../widgets/saved_address_list_item.dart';

class SelectLocationScreen extends StatefulWidget {
  static const routeName = 'selected-location';

  const SelectLocationScreen({Key? key}) : super(key: key);

  @override
  State<SelectLocationScreen> createState() => _SelectLocationScreenState();
}

class _SelectLocationScreenState extends State<SelectLocationScreen> {
  final _searchFieldController = TextEditingController();
  final _loading = <bool>[true];
  String? _searchText;
  List<GoogleAddressModel>? _searchResults;
  List<UserAddressModel>? _savedAddresses;

  @override
  void initState() {
    super.initState();

    Future.delayed(Duration.zero, _initState);
  }

  @override
  void dispose() {
    _searchFieldController.dispose();
    super.dispose();
  }

  void _initState() async {
    final isAuth = context.read<UserProvider>().isAuth;
    if (!isAuth) {
      _loading.removeLast();
      setState(() {});
      return;
    }
    _savedAddresses = await UserAPI().getUserAddresses();
    _loading.removeLast();
    if (!mounted) return;
    setState(() {});
  }

  void _onSavedAddressSelected(UserAddressModel address) {
    Navigator.pop(context, address);
  }

  void _onSearchFieldChanged(String value) async {
    _searchText = value;
    if (value.trim().isEmpty) {
      _loading.clear();
      _searchResults = null;
      // setState(() {});
    } else if (value.trim().length > 3) {
      _loading.add(true);
      // setState(() {});
      final resutls = await GoogleAPI().geocode(address: value.trim());
      if (value == _searchText) {
        _searchResults = resutls;
      }
      if (_loading.isNotEmpty) {
        _loading.removeLast();
      }
    }
    setState(() {});
  }

  void _onSearchResultSelected(GoogleAddressModel address) async {
    final userID = context.read<UserProvider>().userModel?.id;
    if (userID == null) {
      return Navigator.pop(
          context,
          UserAddressModel(
            id: null,
            name: address.name,
            address: address.formattedAddress,
            lat: address.lat.toString(),
            lng: address.lng.toString(),
          ));
    }
    BotToast.showLoading();
    final userAddres = await UserAPI().addAddress(
      lat: address.lat,
      lng: address.lng,
      name: address.name,
      address: address.formattedAddress.trim(),
      city: address.cityOrNeighborhoodName ?? '',
    );
    BotToast.closeAllLoading();
    if (userAddres != null) {
      Navigator.pop(context, userAddres);
    } else {
      Helpers.showErrorNotification();
    }
  }

  @override
  Widget build(BuildContext context) {
    List<Widget> listItems;
    if (_loading.isNotEmpty) {
      listItems = [
        const Padding(
          padding: EdgeInsets.only(top: 30.0),
          child: Center(child: CircularProgressIndicator()),
        )
      ];
    } else if (_searchText?.trim().isEmpty ?? true) {
      final isAuth = context.watch<UserProvider>().userModel?.id != null;
      if (!isAuth) {
        listItems = [];
      } else if (_savedAddresses == null) {
        listItems = [
          Padding(
            padding: const EdgeInsets.only(top: 30.0),
            child: Center(child: Text(tr('error'))),
          )
        ];
      } else if (_savedAddresses!.isEmpty) {
        listItems = [
          Padding(
            padding: const EdgeInsets.only(top: 30.0),
            child: Center(child: Text(tr('no-addresses-found'))),
          )
        ];
      } else {
        listItems = <Widget>[
          Container(
            width: double.infinity,
            color: Colors.grey[100],
            padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 20),
            child: Text(tr('saved-locations')),
          ),
          for (final address in (_savedAddresses ?? <UserAddressModel>[]))
            Padding(
              padding:
                  const EdgeInsets.symmetric(horizontal: 20.0, vertical: 20),
              child: SavedAddressListItem(
                isSaved: true,
                onSelected: () => _onSavedAddressSelected(address),
                address: address.address,
                name: address.name,
              ),
            ),
          // Padding(
          //   padding: const EdgeInsets.symmetric(horizontal: 20.0),
          //   child: TextButton(
          //     onPressed: () {},
          //     child: Row(
          //       mainAxisAlignment: MainAxisAlignment.spaceBetween,
          //       children: [
          //         Text(
          //           tr('show-all-saved-locations'),
          //           style: TextStyle(color: Colors.grey[600]!),
          //         ),
          //         Icon(Icons.keyboard_double_arrow_down, color: Colors.grey[600]!),
          //       ],
          //     ),
          //   ),
          // ),
        ];
      }
    } else {
      if (_searchResults == null) {
        listItems = [
          Padding(
            padding: const EdgeInsets.only(top: 30.0),
            child: Center(child: Text(tr('error'))),
          )
        ];
      } else if (_searchResults!.isEmpty) {
        listItems = [
          Padding(
            padding: const EdgeInsets.only(top: 30.0),
            child: Center(child: Text(tr('no-addresses-found'))),
          )
        ];
      } else {
        listItems = <Widget>[
          Container(
            width: double.infinity,
            color: Colors.grey[100],
            padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 20),
            child: Text(tr('saved-locations')),
          ),
          for (final address in (_searchResults ?? <GoogleAddressModel>[]))
            Padding(
              padding:
                  const EdgeInsets.symmetric(horizontal: 20.0, vertical: 20),
              child: SavedAddressListItem(
                isSaved: false,
                onSelected: () => _onSearchResultSelected(address),
                onSavePressed: () => _onSearchResultSelected(address),
                address: address.formattedAddress,
                name: address.name,
              ),
            ),
          // Padding(
          //   padding: const EdgeInsets.symmetric(horizontal: 20.0),
          //   child: TextButton(
          //     onPressed: () {},
          //     child: Row(
          //       mainAxisAlignment: MainAxisAlignment.spaceBetween,
          //       children: [
          //         Text(
          //           tr('show-all-saved-locations'),
          //           style: TextStyle(color: Colors.grey[600]!),
          //         ),
          //         Icon(Icons.keyboard_double_arrow_down, color: Colors.grey[600]!),
          //       ],
          //     ),
          //   ),
          // ),
        ];
      }
    }
    return Scaffold(
      appBar: CommonAppBar(
        title: tr('select-location'),
        showCartIcon: false,
      ),
      body: SafeArea(
        child: CustomScrollView(
          slivers: [
            SliverToBoxAdapter(
              child: AppBarSearchField(
                controller: _searchFieldController,
                showClearButton: _searchFieldController.text.isNotEmpty,
                onClearPressed: () {
                  _onSearchFieldChanged('');
                  _searchFieldController.text = '';
                },
                onChanged: (value) {
                  _onSearchFieldChanged(value ?? '');
                },
              ),
            ),
            SliverList(
              delegate: SliverChildListDelegate(listItems),
            ),
          ],
        ),
      ),
    );
  }
}
