import 'package:flutter/material.dart';
import 'package:one_drop/screens/splash_screen.dart';

import 'screens/about_us_screen.dart';
import 'screens/add_balance_screen.dart';
import 'screens/add_new_address_screen.dart';
import 'screens/add_payment_card_screen.dart';
import 'screens/all_transactions_screen.dart';
import 'screens/balance_added_success_screen.dart';
import 'screens/contact_us_screen.dart';
import 'screens/create_order_details_screen.dart';
import 'screens/create_order_select_location_map_screen.dart';
import 'screens/create_order_summary_screen.dart';
import 'screens/edit_account_screen.dart';
import 'screens/language_screen.dart';
import 'screens/main_tab_controller.dart';
import 'screens/monthly_packages_screen.dart';
import 'screens/notifications_screen.dart';
import 'screens/onboarding_screen.dart';
import 'screens/order_confirmation_screen.dart';
import 'screens/order_details_screen.dart';
import 'screens/payment_method_screen.dart';
import 'screens/privacy_policy_screen.dart';
import 'screens/saved_addresses_screen.dart';
import 'screens/search_new_address_screen.dart';
import 'screens/select_location_screen.dart';
import 'screens/send_gift_screen.dart';
import 'screens/send_gift_success_screen.dart';
import 'screens/sign_up_form_screen.dart';
import 'screens/sign_up_opt_screen.dart';
import 'screens/sign_up_phone_number_screen.dart';
import 'screens/terms_and_conditions_screen.dart';
import 'screens/wallet_screen.dart';

mixin Routes {
  static const initialRoute = SplashScreen.routeName;

  static final table = <String, Widget Function(BuildContext)>{
    SplashScreen.routeName: (_) => const SplashScreen(),
    OnboardingScreen.routeName: (_) => const OnboardingScreen(),
    SignUpPhoneNumberScreen.routeName: (_) => const SignUpPhoneNumberScreen(),
    SignUpOTPScreen.routeName: (_) => const SignUpOTPScreen(countryCode: null, phone: null, successRoute: null),
    SignUpFormScreen.routeName: (_) => const SignUpFormScreen(),
    MainTabController.routeName: (_) => const MainTabController(),
    MonthlyPackagesScreen.routeName: (_) => const MonthlyPackagesScreen(package: null),
    PaymentMethodScreen.routeName: (_) => const PaymentMethodScreen(
          paymentAmount: null,
          onPaymentSuccess: null,
          paymentDescription: null,
        ),
    OrderDetailsScreen.routeName: (_) => const OrderDetailsScreen(order: null),
    AddPaymentCardScreen.routeName: (_) => const AddPaymentCardScreen(showRememberCardButton: true),
    OrderConfirmationScreen.routeName: (_) => const OrderConfirmationScreen(order: null),
    NotificationsScreen.routeName: (_) => const NotificationsScreen(),
    EditAccountScreen.routeName: (_) => const EditAccountScreen(),
    SavedAddressesScreen.routeName: (_) => const SavedAddressesScreen(),
    SelectLocationScreen.routeName: (_) => const SelectLocationScreen(),
    WalletScreen.routeName: (_) => const WalletScreen(),
    AddBalanceScreen.routeName: (_) => const AddBalanceScreen(),
    BalanceAddedSuccessScreen.routeName: (_) => const BalanceAddedSuccessScreen(amount: null),
    SendGiftScreen.routeName: (_) => const SendGiftScreen(),
    SendGiftSuccessScreen.routeName: (_) => const SendGiftSuccessScreen(amount: null),
    CreateOrderSelectLocationMapScreen.routeName: (_) => const CreateOrderSelectLocationMapScreen(),
    CreateOrderDetailsScreen.routeName: (_) => const CreateOrderDetailsScreen(),
    CreateOrderSummaryScreen.routeName: (_) => const CreateOrderSummaryScreen(),
    SearchNewAddressScreen.routeName: (_) => const SearchNewAddressScreen(),
    AddNewAddressScreen.routeName: (_) => const AddNewAddressScreen(
          address: null,
          lat: null,
          lng: null,
          name: null,
          googleAddress: null,
        ),
    LanguageScreen.routeName: (_) => const LanguageScreen(),
    TermsAndConditionsScreen.routeName: (_) => const TermsAndConditionsScreen(),
    AboutUsScreen.routeName: (_) => const AboutUsScreen(),
    AllTransactionsScreen.routeName: (_) => const AllTransactionsScreen(transactions: []),
    PrivacyPolicyScreen.routeName: (_) => const PrivacyPolicyScreen(),
    ContactUsScreen.routeName: (_) => const ContactUsScreen(),
  };
}
