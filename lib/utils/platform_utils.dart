import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:logging/logging.dart';

class PlatformUtils {
  static final _logger = Logger('PlatformUtils');

  static bool get isIOSSimulator {
    final isSimulator = !Platform.isIOS ? false : !kReleaseMode;
    _logger.info('isIOSSimulator check - Platform.isIOS: ${Platform.isIOS}, kReleaseMode: $kReleaseMode, result: $isSimulator');
    return isSimulator;
  }
}
