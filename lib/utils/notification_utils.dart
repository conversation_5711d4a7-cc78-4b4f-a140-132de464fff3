import 'package:bot_toast/bot_toast.dart';
import 'package:flutter/material.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:one_drop/constants.dart';

/// Utility class for showing notifications
class NotificationUtils {
  const NotificationUtils._();

  /// Shows a success notification with optional custom text, duration and title
  static void showSuccess({
    String text = 'Success', 
    Duration? duration, 
    Widget? title
  }) {
    BotToast.showNotification(
      borderRadius: 10.0,
      title: (_) {
        if (title == null) {
          return Text(
            text,
            style: const TextStyle(color: Colors.black87),
          );
        }
        return title;
      },
      trailing: (_) => title == null ? const Icon(Icons.check, color: Colors.green) : const SizedBox(),
      duration: duration ?? K.notificationDuration,
    );
  }

  /// Shows an error notification with optional custom text
  static void showError([String? text]) {
    BotToast.showNotification(
      borderRadius: 10.0,
      title: (_) => Text(
        text ?? tr('error'),
        style: const TextStyle(color: Colors.black87),
      ),
      duration: K.notificationDuration,
      trailing: (_) => const Icon(Icons.error, color: Colors.red),
    );
  }

  /// Shows a generic notification with the given text
  static void show(String? text) {
    if (text == null) return;
    BotToast.showNotification(
      borderRadius: 10.0,
      duration: K.notificationDuration,
      title: (_) => Text(
        text,
        style: const TextStyle(color: Colors.black87),
      ),
    );
  }
}
