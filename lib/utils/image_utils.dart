import 'dart:io';
import 'package:flutter/material.dart';
import 'package:one_drop/widgets/pick_image_bottom_sheet.dart';

/// Utility class for image-related operations
class ImageUtils {
  const ImageUtils._();

  /// Shows a bottom sheet for picking an image
  /// Returns the selected image file or null if cancelled
  static Future<File?> showPickImageBottomSheet(BuildContext context) {
    return showModalBottomSheet<File>(
      context: context,
      backgroundColor: Theme.of(context).scaffoldBackgroundColor,
      builder: (cxt) {
        return const PickImageBottomSheet();
      },
    );
  }
}
