import 'dart:math' as math;

/// Utility class for formatting numbers, durations and other data types
class FormatUtils {
  const FormatUtils._();

  /// Formats a number with its fraction part if necessary
  static String humanizeNumberWithFraction(double number) {
    return '${number.toInt()}${getNumbersAfterDecimalPoint(number)}'.trim();
  }

  /// Gets the decimal part of a number as a string
  static String getNumbersAfterDecimalPoint(double number) {
    final fraction = number - number.toInt();
    if (fraction <= 0.01) {
      return '';
    }
    final fractionString = (fraction).toStringAsPrecision(3);
    return fractionString.substring(1, math.min(4, fractionString.length));
  }

  /// Parses a duration from a string in format "HH:mm" or "HH:mm:ss"
  static Duration parseDuration(String s) {
    try {
      int hours = 0;
      int minutes = 0;
      List<String> parts = s.split(':');
      if (parts.length > 2) {
        hours = int.parse(parts[parts.length - 3]);
      }
      if (parts.length > 1) {
        minutes = int.parse(parts[parts.length - 2]);
      }
      return Duration(hours: hours, minutes: minutes);
    } catch (e) {
      return Duration.zero;
    }
  }

  /// Formats a duration to string in format "HH:mm:ss"
  static String formatDuration(Duration duration) {
    String twoDigits(int n) => n.toString().padLeft(2, "0");
    String twoDigitMinutes = twoDigits(duration.inMinutes.remainder(60));
    String twoDigitSeconds = twoDigits(duration.inSeconds.remainder(60));
    return "${twoDigits(duration.inHours)}:$twoDigitMinutes:$twoDigitSeconds";
  }
}
