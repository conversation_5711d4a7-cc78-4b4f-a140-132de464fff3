import 'package:easy_localization/easy_localization.dart';

/// Utility class for input validation functions
class ValidationUtils {
  const ValidationUtils._();

  /// Validates a name input
  /// Returns an error message if invalid, null if valid
  static String? validateName(String? value) {
    if (value?.trim().isEmpty ?? true) {
      return tr('please_enter_name');
    }
    return null;
  }

  /// Validates an email address
  /// Returns an error message if invalid, null if valid
  static String? validateEmail(String? email) {
    final emailTrimed = email?.trim();
    if (emailTrimed == null || emailTrimed.trim().isEmpty) {
      return tr('enter_your_email');
    }
    final dotIndex = emailTrimed.trim().indexOf('.');
    final containsDot = dotIndex != -1 && dotIndex != (emailTrimed.length - 1);
    if (!emailTrimed.trim().contains('@') || !containsDot) {
      return tr('enter_valid_email');
    }
    return null;
  }

  /// Checks if a character is a capital letter
  static bool isCapitalLetter(int asciiValue) {
    return asciiValue >= 65 && asciiValue <= 90;
  }

  /// Checks if a character is a small letter
  static bool isSmallLetter(int asciiValue) {
    return asciiValue >= 97 && asciiValue <= 122;
  }

  /// Checks if a character is an alphabetic letter
  static bool isAlphabiticLetter(int asciiValue) {
    return isCapitalLetter(asciiValue) || isSmallLetter(asciiValue);
  }

  /// Checks if a character is a digit
  static bool isDigit(int asciiValue) {
    return asciiValue >= 48 && asciiValue <= 57;
  }
}
