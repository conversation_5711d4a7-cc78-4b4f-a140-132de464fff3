import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';

class AppBarSearchField extends StatelessWidget implements PreferredSizeWidget {
  const AppBarSearchField({
    Key? key,
    this.autoFocus = false,
    this.showClearButton = false,
    this.onChanged,
    this.onClearPressed,
    this.controller,
  }) : super(key: key);

  final bool showClearButton;
  final bool autoFocus;
  final TextEditingController? controller;
  final void Function()? onClearPressed;
  final void Function(String?)? onChanged;

  @override
  Widget build(BuildContext context) {
    final border = OutlineInputBorder(
      borderRadius: BorderRadius.circular(10),
      borderSide: const BorderSide(color: Colors.white, width: 0),
    );
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 20.0, vertical: 10),
      child: TextField(
        controller: controller,
        autofocus: autoFocus,
        onChanged: onChanged,
        decoration: InputDecoration(
          border: border,
          enabledBorder: border,
          focusedBorder: border,
          fillColor: Colors.grey[200],
          filled: true,
          prefixIcon: const Icon(Icons.search),
          suffixIconConstraints: const BoxConstraints(
            maxWidth: 50,
            maxHeight: 50,
          ),
          suffixIcon: showClearButton
              ? Padding(
                  padding: const EdgeInsetsDirectional.fromSTEB(10, 0, 10, 0),
                  child: SizedBox(
                    width: 20,
                    height: 20,
                    child: TextButton(
                      onPressed: onClearPressed,
                      child: const Icon(Icons.close, color: Colors.white, size: 16),
                      style: ButtonStyle(
                        backgroundColor: MaterialStateProperty.all(Colors.grey),
                        padding: MaterialStateProperty.all(const EdgeInsets.all(0)),
                        shape: MaterialStateProperty.all(
                          const CircleBorder(),
                        ),
                      ),
                    ),
                  ),
                )
              : const SizedBox(),
          hintText: tr('search_your_location'),
        ),
      ),
    );
  }

  @override
  Size get preferredSize => const Size(double.infinity, 45);
}
