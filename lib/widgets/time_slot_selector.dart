// widgets/time_slot_selector.dart
import 'package:flutter/material.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:one_drop/models/available_time_slot_model.dart';
import 'package:one_drop/styles.dart';

class TimeSlotSelector extends StatefulWidget {
  final List<TimeSlot> availableSlots;
  final Function(String) onTimeSelected;
  final String? selectedTime;
  final bool isLoading;

  const TimeSlotSelector({
    Key? key,
    required this.availableSlots,
    required this.onTimeSelected,
    this.selectedTime,
    this.isLoading = false,
  }) : super(key: key);

  @override
  State<TimeSlotSelector> createState() => _TimeSlotSelectorState();
}

class _TimeSlotSelectorState extends State<TimeSlotSelector> {
  String? _selectedTime;

  @override
  void initState() {
    super.initState();
    _selectedTime = widget.selectedTime;
  }

  @override
  void didUpdateWidget(TimeSlotSelector oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.selectedTime != widget.selectedTime) {
      _selectedTime = widget.selectedTime;
    }
  }

  @override
  Widget build(BuildContext context) {
    if (widget.isLoading) {
      return const Center(
        child: Padding(
          padding: EdgeInsets.all(16.0),
          child: CircularProgressIndicator(),
        ),
      );
    }

    if (widget.availableSlots.isEmpty) {
      return Center(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Text(
            tr('no_available_slots'),
            style: const TextStyle(
              fontSize: 16,
              color: Colors.grey,
              fontStyle: FontStyle.italic,
            ),
          ),
        ),
      );
    }

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Wrap(
        spacing: 8.0,
        runSpacing: 8.0,
        children: widget.availableSlots.map((slot) {
          final isSelected = _selectedTime == slot.hour;
          final hasAvailability = slot.availableCount > 0;
          return _buildTimeSlotChip(slot, isSelected, hasAvailability);
        }).toList(),
      ),
    );
  }

  Widget _buildTimeSlotChip(
      TimeSlot slot, bool isSelected, bool hasAvailability) {
    return InkWell(
      onTap: hasAvailability
          ? () {
              setState(() {
                _selectedTime = slot.hour;
              });
              widget.onTimeSelected(slot.hour);
            }
          : null, // عندما لا تكون هناك أماكن متاحة، نجعل onTap كـ null
      borderRadius: BorderRadius.circular(20),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
        decoration: BoxDecoration(
          color: isSelected
              ? Styles.primaryColor
              : (hasAvailability ? Colors.white : Colors.grey.shade200),
          borderRadius: BorderRadius.circular(20),
          border: Border.all(
            color: isSelected
                ? Styles.primaryColor
                : (hasAvailability
                    ? Colors.grey.shade300
                    : Colors.grey.shade300),
            width: 1.5,
          ),
          boxShadow: isSelected
              ? [
                  BoxShadow(
                    color: Styles.primaryColor.withOpacity(0.3),
                    blurRadius: 5,
                    offset: const Offset(0, 2),
                  ),
                ]
              : null,
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              slot.formattedHour,
              style: TextStyle(
                color: isSelected
                    ? Colors.white
                    : (hasAvailability ? Colors.black87 : Colors.grey.shade500),
                fontWeight: FontWeight.bold,
                fontSize: 14,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
