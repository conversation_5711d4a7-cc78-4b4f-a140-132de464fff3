import 'package:flutter/material.dart';

import '../styles.dart';

class PaymentMethodListItem extends StatelessWidget {
  const PaymentMethodListItem({
    Key? key,
    required this.name,
    required this.icon,
    this.selected = false,
    this.expanded = false,
    this.bottom,
    this.onPressed,
    this.subtitle,
  }) : super(key: key);

  final bool selected;
  final bool expanded;
  final String name;
  final Widget? icon;
  final Widget? subtitle;
  final Widget? bottom;
  final void Function()? onPressed;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onPressed,
      child: Container(
        width: double.infinity,
        decoration: BoxDecoration(
          color: Colors.white,
          boxShadow: [kBoxShadow],
          borderRadius: BorderRadius.circular(10),
        ),
        padding: const EdgeInsets.symmetric(vertical: 20, horizontal: 0),
        child: Opacity(
          opacity: onPressed == null ? 0.4 : 1,
          child: Column(
            children: [
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 10),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Container(
                      width: 20,
                      height: 20,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        color: selected ? Styles.primaryColor : Colors.white,
                        border: Border.all(
                          color: selected ? Styles.primaryColor : Colors.grey,
                          width: 1,
                        ),
                      ),
                      alignment: Alignment.center,
                      child: selected ? const Icon(Icons.check, size: 14, color: Colors.white) : null,
                    ),
                    const SizedBox(width: 15),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Text(
                            name,
                            style: Theme.of(context).textTheme.labelLarge,
                          ),
                          if (subtitle != null) const SizedBox(height: 10),
                          if (subtitle != null) subtitle!,
                        ],
                      ),
                    ),
                    const Spacer(),
                    if (icon != null) icon!,
                  ],
                ),
              ),
              if (expanded) const Divider(indent: 10, endIndent: 10, height: 40),
              if (bottom != null && expanded) bottom!,
            ],
          ),
        ),
      ),
    );
  }
}
