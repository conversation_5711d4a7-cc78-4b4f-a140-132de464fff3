import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';

import '../models/home_banner_item_model.dart';
import '../styles.dart';

class HomeCardItem extends StatefulWidget {
  const HomeCardItem({
    required this.banner,
    required this.onPressed,
    this.noRadius = false, // إضافة خاصية للتحكم في الحواف المستديرة
    Key? key,
  }) : super(key: key);

  final HomeBannerItemModel banner;
  final void Function(HomeBannerItemModel) onPressed;
  final bool noRadius; // خاصية جديدة للتحكم في الحواف المستديرة

  @override
  State<HomeCardItem> createState() => _HomeCardItemState();
}

class _HomeCardItemState extends State<HomeCardItem>
    with SingleTickerProviderStateMixin {
  late final AnimationController _controller;
  late final Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 200),
    );
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.98,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // تعريف قيمة البوردر ريديوس بناءً على خاصية noRadius
    final borderRadius = widget.noRadius ? 0.0 : 20.0;

    return GestureDetector(
      onTapDown: (_) => _controller.forward(),
      onTapUp: (_) => _controller.reverse(),
      onTapCancel: () => _controller.reverse(),
      child: ScaleTransition(
        scale: _scaleAnimation,
        child: InkWell(
          onTap: () => widget.onPressed(widget.banner),
          child: AspectRatio(
            aspectRatio: 16 / 9,
            child: Container(
              width: double.infinity,
              decoration: BoxDecoration(
                color: Styles.secondaryColor,
                borderRadius: BorderRadius.circular(
                    borderRadius), // استخدام القيمة المتغيرة للبوردر ريديوس
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.1),
                    offset: const Offset(0, 3),
                    blurRadius: 10,
                    spreadRadius: 0,
                  ),
                ],
              ),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(
                    borderRadius), // استخدام نفس القيمة هنا أيضا
                child: Stack(
                  children: [
                    SizedBox(
                      width: double.infinity,
                      height: double.infinity,
                      child: CachedNetworkImage(
                        imageUrl: widget.banner.image,
                        fit: BoxFit.cover,
                      ),
                    ),
                    // التدرج الشفاف للنص
                    if (widget.banner.name != null ||
                        widget.banner.desc != null)
                      Positioned(
                        bottom: 0,
                        left: 0,
                        right: 0,
                        child: Container(
                          padding: const EdgeInsets.symmetric(
                              vertical: 10, horizontal: 15),
                          decoration: BoxDecoration(
                            gradient: LinearGradient(
                              begin: Alignment.bottomCenter,
                              end: Alignment.topCenter,
                              colors: [
                                Colors.black.withOpacity(0.7),
                                Colors.transparent,
                              ],
                            ),
                          ),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              if (widget.banner.name != null)
                                Text(
                                  widget.banner.name!,
                                  style: const TextStyle(
                                    color: Colors.white,
                                    fontWeight: FontWeight.bold,
                                    fontSize: 16,
                                  ),
                                ),
                              if (widget.banner.desc != null)
                                Text(
                                  widget.banner.desc!,
                                  style: TextStyle(
                                    color: Colors.white.withOpacity(0.9),
                                    fontSize: 14,
                                  ),
                                  maxLines: 2,
                                  overflow: TextOverflow.ellipsis,
                                ),
                            ],
                          ),
                        ),
                      ),
                    // إضافة طبقة لإظهار حالة عدم التفعيل
                    if (!widget.banner.isActive)
                      Positioned.fill(
                        child: Container(
                          color: Colors.black.withOpacity(0.5),
                          child: Center(
                            child: Text(
                              'غير متاح',
                              style: const TextStyle(
                                color: Colors.white,
                                fontWeight: FontWeight.bold,
                                fontSize: 16,
                              ),
                            ),
                          ),
                        ),
                      ),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}
