import 'package:flutter/material.dart';
import 'package:one_drop/styles.dart';

class WalletSavedCardItem extends StatelessWidget {
  const WalletSavedCardItem({
    Key? key,
    required this.bankName,
    required this.numberText,
    required this.image,
    this.onPressed,
    this.onDeletePressed,
  }) : super(key: key);

  final String bankName;
  final String? numberText;
  final Widget? image;
  final void Function()? onPressed;
  final void Function()? onDeletePressed;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onPressed,
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 5),
        child: Row(
          children: [
            if (image != null) image! else const Icon(Icons.payment, color: Styles.primaryColor),
            const SizedBox(width: 20),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    bankName,
                    style: Theme.of(context).textTheme.labelLarge,
                  ),
                  if (numberText != null) const SizedBox(height: 5),
                  if (numberText != null) Text(numberText!, style: const TextStyle(color: Colors.grey)),
                ],
              ),
            ),
            if (onPressed != null) const Icon(Icons.keyboard_arrow_left),
            if (onDeletePressed != null)
              IconButton(
                onPressed: onDeletePressed,
                icon: const Icon(Icons.delete_outlined),
                color: Colors.red,
              ),
          ],
        ),
      ),
    );
  }
}
