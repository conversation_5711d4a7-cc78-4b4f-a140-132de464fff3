import 'package:cached_network_image/cached_network_image.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:one_drop/api/user_api.dart';
import 'package:one_drop/models/target_item_model.dart';
import 'package:one_drop/providers/create_order_provider.dart';
import 'package:one_drop/styles.dart';
import 'package:provider/provider.dart';

import '../models/order_category_item_model.dart';
import '../screens/create_order_select_location_map_screen.dart';
import 'od_alert_dialog.dart';

class HomeCreateWashOrderList extends StatefulWidget {
  const HomeCreateWashOrderList({
    required this.categories,
    Key? key,
  }) : super(key: key);

  final List<OrderCategoryItemModel> categories;

  @override
  State<HomeCreateWashOrderList> createState() =>
      _HomeCreateWashOrderListState();
}

class _HomeCreateWashOrderListState extends State<HomeCreateWashOrderList> {
  List<TargetItemModel>? _targets;
  bool _loading = true;

  @override
  void initState() {
    super.initState();
    _loadTargets();
  }

  void _loadTargets() async {
    _targets = await UserAPI().getTargets();
    if (mounted) {
      setState(() {
        _loading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final cardWidth = screenWidth * 0.38; // About 38% of screen width
    final cardHeight = screenHeight * 0.25; // About 25% of screen height

    if (_loading) {
      return const Center(child: CircularProgressIndicator());
    }

    final hasCompletedTarget = _targets?.any(
            (target) => target.totalCompletedOrders >= target.washCount) ??
        false;

    final listItems = widget.categories.map<Widget>((e) {
      return HomeCreateWashOrderListItem(
        orderCategory: e,
        cardWidth: cardWidth,
        cardHeight: cardHeight,
        hasFreeWash: hasCompletedTarget,
      );
    }).toList();
    return Column(
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 20.0),
          child: Row(
            children: [
              Text(
                tr('order_new_wash'),
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
              ),
            ],
          ),
        ),
        const SizedBox(height: 10),
        SizedBox(
          height: cardHeight,
          child: ListView.separated(
            itemBuilder: (cxt, index) => listItems[index],
            separatorBuilder: (cxt, index) =>
                SizedBox(width: screenWidth * 0.03),
            itemCount: listItems.length,
            padding: EdgeInsets.symmetric(
              horizontal: screenWidth * 0.04,
              vertical: screenHeight * 0.01,
            ),
            scrollDirection: Axis.horizontal,
          ),
        ),
      ],
    );
  }
}

class HomeCreateWashOrderListItem extends StatelessWidget {
  const HomeCreateWashOrderListItem({
    Key? key,
    required this.orderCategory,
    required this.cardWidth,
    required this.cardHeight,
    required this.hasFreeWash,
  }) : super(key: key);

  final bool hasFreeWash;

  final OrderCategoryItemModel orderCategory;
  final double cardWidth;
  final double cardHeight;

  static void onPressed({
    required BuildContext context,
    required OrderCategoryItemModel orderCategory,
    required bool hasFreeWash,
  }) {
    if (orderCategory.isActive) {
      final createOrderProvider = context.read<CreateOrderProvider>();
      createOrderProvider.clearAll();
      createOrderProvider.hasFreeWash = hasFreeWash;
      createOrderProvider.selectedOrderCategory = orderCategory;
      Navigator.pushNamed(
          context, CreateOrderSelectLocationMapScreen.routeName);
    } else {
      showDialog<bool>(
        context: context,
        builder: (cxt) => ODAlertDialog(
          child: Text(tr('service_currently_unavailable')),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.pop(cxt);
              },
              child: Text(tr('close')),
              style: ButtonStyle(
                foregroundColor: MaterialStateProperty.all(Colors.black),
              ),
            )
          ],
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;

    return InkWell(
      onTap: () {
        onPressed(
            context: context,
            orderCategory: orderCategory,
            hasFreeWash: hasFreeWash);
      },
      child: Container(
        height: cardHeight,
        width: cardWidth,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.08),
              offset: const Offset(0, 2),
              blurRadius: 8,
              spreadRadius: 0,
            ),
          ],
        ),
        padding: EdgeInsets.all(screenWidth * 0.02),
        child: Column(
          children: [
            Expanded(
              flex: 3,
              child: Container(
                decoration: BoxDecoration(
                  color: Styles.primaryColor.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                alignment: Alignment.center,
                child: SizedBox(
                  width: cardWidth * 0.45,
                  height: cardWidth * 0.45,
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(10),
                    child: kReleaseMode
                        ? CachedNetworkImage(
                            imageUrl: orderCategory.image,
                            fit: BoxFit.contain,
                          )
                        : SvgPicture.asset(
                            'assets/images/6.svg',
                            fit: BoxFit.contain,
                          ),
                  ),
                ),
              ),
            ),
            SizedBox(height: screenHeight * 0.008), // تقليل المسافة أسفل الصورة
            Expanded(
              flex: 2,
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment:
                    CrossAxisAlignment.center, // لمحاذاة النص في الوسط
                children: [
                  Text(
                    orderCategory.name,
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold, // جعل اسم الخدمة عريض
                          color: Styles.primaryColor,
                        ),
                    textAlign: TextAlign.center, // محاذاة النص للوسط
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  SizedBox(height: screenHeight * 0.005), // تقليل المسافة
                  Padding(
                    padding:
                        EdgeInsets.symmetric(horizontal: screenWidth * 0.02),
                    child: Text(
                      orderCategory.desc,
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            height: 1.2, // تقليل المسافة بين السطور
                            color: Colors.grey[600],
                          ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                      textAlign: TextAlign.center, // محاذاة النص للوسط
                    ),
                  ),
                ],
              ),
            ),
            SizedBox(height: screenHeight * 0.005), // تقليل المسافة أسفل الكارد
          ],
        ),
      ),
    );
  }
}
