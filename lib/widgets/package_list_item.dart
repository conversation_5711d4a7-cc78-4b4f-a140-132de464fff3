import 'package:bot_toast/bot_toast.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:one_drop/helpers.dart';
import 'package:one_drop/providers/user_provider.dart';
import 'package:one_drop/utils/notification_utils.dart';
import 'package:provider/provider.dart';

import '../api/user_api.dart';
import '../constants.dart';
import '../models/package_item_model.dart';
import '../screens/main_tab_controller.dart';
import '../screens/payment_method_screen.dart';
import '../styles.dart';

class PackageListItem extends StatelessWidget {
  const PackageListItem({
    Key? key,
    required this.package,
  }) : super(key: key);

  final PackageItemModel package;

  void _subscribePressed(BuildContext context) {
    Navigator.push(
      context,
      CupertinoPageRoute(
        builder: (cxt) => PaymentMethodScreen(
          onPaymentSuccess: ({
            required PaymentMethod paymentMethod,
            required String transactionID,
          }) =>
              _onPaymentSuccess(
            context: context,
            paymentMethod: paymentMethod,
            transactionID: transactionID,
          ),
          paymentAmount: package.price,
          paymentDescription: package.name,
          isFromPackages: true, // تحديد أن المستخدم قادم من صفحة الاشتراكات
          paymentMethods: const [
            K.paymentMethodApplePay,
            K.paymentMethodWallet,
            K.paymentMethodCard,
          ],
        ),
        settings: const RouteSettings(name: PaymentMethodScreen.routeName),
      ),
    );
  }

  void _onPaymentSuccess({
    required String transactionID,
    required PaymentMethod paymentMethod,
    required BuildContext context,
  }) async {
    try {
      BotToast.showLoading();
      final user = context.read<UserProvider>().userModel;
      if (user == null || user.id == null) {
        BotToast.closeAllLoading();
        NotificationUtils.showError(tr('something_went_wrong'));
        return;
      }

      await UserAPI().pay(
        userID: user.id!,
        paymentAmount: package.price.toInt(),
        purchasedItemID: package.id,
        paymentMethod: kMapPaymentMethodEnumToString[paymentMethod]!,
        purchasedItemType: K.purchasedItemTypePackage,
        transactionID: transactionID,
        receivedID: null,
      );
      BotToast.closeAllLoading();
      Navigator.pushNamedAndRemoveUntil(
        context,
        MainTabController.routeName,
        (route) => false,
      );
    } catch (_) {
      BotToast.closeAllLoading();
      NotificationUtils.showError(tr('something_went_wrong'));
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      margin: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(5),
        boxShadow: [kBoxShadow],
        color: Colors.white,
      ),
      padding: const EdgeInsets.symmetric(horizontal: 5, vertical: 5),
      child: Column(
        children: [
          Container(
            width: double.infinity,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(5),
              color: Styles.primaryColor.withOpacity(0.1),
            ),
            padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 20),
            child: Text(
              package.name,
              textAlign: TextAlign.center,
              style: Theme.of(context).textTheme.labelLarge?.copyWith(
                    color: Styles.secondaryColor,
                  ),
            ),
          ),
          const SizedBox(height: 10),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                package.price.toStringAsFixed(2),
                style: Theme.of(context).textTheme.labelLarge?.copyWith(
                      color: Styles.primaryColor,
                    ),
              ),
              Text(
                ' ${tr('sr')} ',
                style: const TextStyle(color: Styles.primaryColor),
              ),
              Text(
                ' / ${package.durationInDays >= 365 ? tr('yearly') : tr('monthly')}',
                style: Theme.of(context).textTheme.labelLarge,
              ),
            ],
          ),
          const SizedBox(height: 20),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                  '${tr('number_of_washes')}: ${package.washes} ${tr('washes')}'),
            ],
          ),
          const SizedBox(height: 10),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text('${tr('wash_type')}: ${package.category?.name ?? ''}'),
            ],
          ),
          const SizedBox(height: 10),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text('${tr('car_size')}: ${package.car?.name ?? ''}'),
            ],
          ),
          const SizedBox(height: 20),
          SizedBox(
            width: 200,
            child: ElevatedButton(
              onPressed: () => _subscribePressed(context),
              child: Text(tr('subscribe_now')),
              style: ButtonStyle(
                backgroundColor:
                    MaterialStateProperty.all(Styles.secondaryColor),
                shape: MaterialStateProperty.all(
                  RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(10)),
                ),
              ),
            ),
          ),
          const SizedBox(height: 20),
        ],
      ),
    );
  }
}
