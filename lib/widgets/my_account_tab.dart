import 'package:bot_toast/bot_toast.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:one_drop/api/user_api.dart';
import 'package:one_drop/providers/user_provider.dart';
import 'package:one_drop/screens/terms_and_conditions_screen.dart';
import 'package:provider/provider.dart';

import '../constants.dart';
import '../screens/contact_us_screen.dart';
import '../screens/edit_account_screen.dart';
import '../screens/language_screen.dart';
import '../screens/onboarding_screen.dart';
import '../screens/privacy_policy_screen.dart';
import '../screens/saved_addresses_screen.dart';
import '../screens/wallet_screen.dart';
import '../styles.dart';

class MyAccountTab extends StatelessWidget {
  const MyAccountTab({Key? key}) : super(key: key);

  static Future<void> logoutPressed(BuildContext context) async {
    BotToast.showLoading();
    await context.read<UserProvider>().setUserModel(null);
    BotToast.closeAllLoading();
    Navigator.pushNamedAndRemoveUntil(
      context,
      OnboardingScreen.routeName,
      (route) => false,
    );
  }

  @override
  Widget build(BuildContext context) {
    final currentLang = EasyLocalization.of(context)?.locale.languageCode;
    // final screenHeight = MediaQuery.of(context).size.height;
    final userModel = context.watch<UserProvider>().userModel;
    final listItems = <Widget>[
      const SizedBox(height: 20),
      Row(
        children: [
          const SizedBox(width: 20),
          Text(
            tr('my_account'),
            style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                  color: Styles.textPrimaryColor,
                  fontWeight: FontWeight.bold,
                ),
          ),
        ],
      ),
      Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          ClipOval(
            child: kReleaseMode && userModel?.image != null
                ? CachedNetworkImage(
                    imageUrl: userModel?.image ?? '',
                    errorWidget: (_, __, ___) => placeholderUserImage,
                    placeholder: (_, __) => placeholderUserImage,
                    fit: BoxFit.cover,
                    width: 80,
                    height: 80,
                  )
                : SizedBox(
                    width: 80,
                    height: 80,
                    child: placeholderUserImage,
                  ),
          ),
        ],
      ),
      const SizedBox(height: 10),
      Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text(
            userModel?.name ?? '',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  color: Styles.textPrimaryColor,
                  fontWeight: FontWeight.w600,
                ),
          ),
        ],
      ),
      const SizedBox(height: 10),
      Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text(
            userModel?.email ?? '',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Styles.textSecondaryColor,
                ),
          ),
        ],
      ),
      const SizedBox(height: 30),
      Container(
        // constraints: BoxConstraints(
        //   minHeight: screenHeight * 0.6,
        // ),
        width: double.infinity,
        decoration: BoxDecoration(
          color: Colors.white,
          boxShadow: [kBoxShadow],
          borderRadius: const BorderRadius.only(
            topLeft: Radius.circular(10),
            topRight: Radius.circular(10),
          ),
        ),
        child: Column(
          children: [
            const SizedBox(height: 30),
            MyAccountButton(
              icon: SvgPicture.asset(
                'assets/images/17.svg',
                color: Styles.primaryColor,
              ),
              onPressed: () {
                Navigator.pushNamed(context, EditAccountScreen.routeName);
              },
              text: tr('edit_account'),
            ),
            MyAccountButton(
              icon: SvgPicture.asset(
                'assets/images/16.svg',
                color: Styles.primaryColor,
              ),
              onPressed: () {
                Navigator.pushNamed(context, SavedAddressesScreen.routeName);
              },
              text: tr('saved_addresses'),
            ),
            MyAccountButton(
              icon: SvgPicture.asset(
                'assets/images/15.svg',
                color: Styles.primaryColor,
              ),
              onPressed: () {
                Navigator.pushNamed(context, WalletScreen.routeName);
              },
              text: tr('wallet'),
            ),
            const Divider(indent: 20, endIndent: 20),
            MyAccountButton(
              icon: SvgPicture.asset(
                'assets/images/18.svg',
                color: Styles.primaryColor,
              ),
              onPressed: () {
                Navigator.pushNamed(context, LanguageScreen.routeName);
              },
              text: tr('language'),
              trailing: Row(
                children: [
                  Text(
                    kLocaleLangName[currentLang] ?? '',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: Styles.textSecondaryColor,
                        ),
                  ),
                  const SizedBox(width: 5),
                  const Icon(Icons.chevron_right),
                ],
              ),
            ),
            MyAccountButton(
              icon: SvgPicture.asset(
                'assets/images/19.svg',
                color: Styles.primaryColor,
              ),
              onPressed: null,
              text: tr('notifications'),
              trailing: Switch.adaptive(
                value: userModel?.isNotify == 1,
                onChanged: (value) async {
                  debugPrint(
                      'Notification toggle pressed. Current value: ${userModel?.isNotify}, New value: ${value ? 1 : 0}');

                  // إظهار مؤشر التحميل
                  BotToast.showLoading();

                  try {
                    // استدعاء API لتحديث إعدادات الإشعارات
                    final success = await UserAPI().updateNotificationSettings(
                        enabled: value, context: context);

                    debugPrint(
                        'Update notification settings API response: $success');

                    if (success) {
                      // إذا نجحت عملية التحديث، قم بتحديث نموذج المستخدم محلياً
                      final updatedUser =
                          userModel?.copyWith(isNotify: value ? 1 : 0);
                      await context
                          .read<UserProvider>()
                          .setUserModel(updatedUser);

                      // عرض رسالة نجاح
                      BotToast.showText(
                        text: value
                            ? tr('notifications_enabled')
                            : tr('notifications_disabled'),
                        contentColor: Styles.successColor,
                        duration: const Duration(seconds: 2),
                      );
                    } else {
                      // إذا فشلت العملية، أعد القيمة إلى حالتها السابقة
                      BotToast.showText(
                        text: tr('failed_to_update_notification_settings'),
                        contentColor: Colors.red,
                        duration: const Duration(seconds: 2),
                      );
                    }
                  } catch (e) {
                    debugPrint('Error updating notification settings: $e');
                    // عرض رسالة خطأ
                    BotToast.showText(
                      text: tr('error_updating_notification_settings'),
                      contentColor: Colors.red,
                      duration: const Duration(seconds: 2),
                    );
                  } finally {
                    // إغلاق مؤشر التحميل في جميع الحالات
                    BotToast.closeAllLoading();
                  }
                },
                activeColor: Styles.primaryColor,
              ),
            ),
            const Divider(indent: 20, endIndent: 20),
            MyAccountButton(
              icon: SvgPicture.asset(
                'assets/images/12.svg',
                color: Styles.primaryColor,
              ),
              onPressed: () {
                Navigator.pushNamed(
                    context, TermsAndConditionsScreen.routeName);
              },
              text: tr('terms_and_conditions'),
            ),
            MyAccountButton(
              icon: SvgPicture.asset(
                'assets/images/13.svg',
                color: Styles.primaryColor,
              ),
              onPressed: () {
                Navigator.pushNamed(context, PrivacyPolicyScreen.routeName);
              },
              text: tr('privacy_policy'),
            ),
            MyAccountButton(
              icon: const Icon(
                Icons.email_outlined,
                color: Styles.primaryColor,
              ),
              onPressed: () {
                Navigator.pushNamed(context, ContactUsScreen.routeName);
              },
              text: tr('contact_us'),
            ),
            MyAccountButton(
              // icon: const Icon(
              //   Icons.logout,
              //   color: Colors.red,
              // ),
              icon: SvgPicture.asset(
                'assets/images/14.svg',
                color: Colors.red,
              ),
              iconBackgroundColor: Colors.red.withOpacity(0.1),
              onPressed: () => logoutPressed(context),
              text: tr('logout'),
            ),
            const SizedBox(height: 30),
          ],
        ),
      ),
    ];
    return SafeArea(
      child: Container(
        color: Colors.grey[200],
        child: ListView.builder(
          itemBuilder: (cxt, index) => listItems[index],
          itemCount: listItems.length,
        ),
      ),
    );
  }
}

class MyAccountButton extends StatelessWidget {
  const MyAccountButton({
    Key? key,
    required this.text,
    required this.icon,
    required this.onPressed,
    this.trailing,
    this.iconBackgroundColor,
  }) : super(key: key);

  final String text;
  final Widget icon;
  final Widget? trailing;
  final Color? iconBackgroundColor;
  final void Function()? onPressed;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 5),
      child: InkWell(
        onTap: onPressed,
        child: Row(
          children: [
            Container(
              width: 45,
              height: 45,
              alignment: Alignment.center,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(10),
                color:
                    iconBackgroundColor ?? Styles.primaryColor.withOpacity(0.1),
              ),
              child: icon,
            ),
            const SizedBox(width: 10),
            Text(
              text,
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    color: Styles.textPrimaryColor,
                  ),
            ),
            const Spacer(),
            trailing ?? const Icon(Icons.chevron_right),
          ],
        ),
      ),
    );
  }
}
