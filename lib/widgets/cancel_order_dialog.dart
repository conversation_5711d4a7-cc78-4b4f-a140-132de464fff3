import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';

import 'od_alert_dialog.dart';

class CancelOrderDialog extends StatelessWidget {
  const CancelOrderDialog({
    Key? key,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return ODAlertDialog(
      child: Text(
        tr('cancel_order'),
        style: Theme.of(context).textTheme.labelLarge,
      ),
      actions: [
        TextButton(
          onPressed: () {
            Navigator.pop(context, true);
          },
          child: Text(tr('yes_cancel')),
          style: ButtonStyle(
            foregroundColor: MaterialStateProperty.all(Colors.black),
          ),
        ),
        TextButton(
          onPressed: () {
            Navigator.pop(context);
          },
          child: Text(tr('no')),
          style: ButtonStyle(
            foregroundColor: MaterialStateProperty.all(Colors.black),
          ),
        ),
      ],
    );
  }
}
