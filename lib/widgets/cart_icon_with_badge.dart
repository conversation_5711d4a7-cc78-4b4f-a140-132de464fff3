import 'package:flutter/material.dart';
import 'package:one_drop/models/cart.dart';
import 'package:one_drop/screens/cart_screen.dart';
import 'package:one_drop/services/cart_service.dart';
import 'package:one_drop/services/service_locator.dart';
import 'package:easy_localization/easy_localization.dart';

class CartIconWithBadge extends StatefulWidget {
  const CartIconWithBadge({Key? key}) : super(key: key);

  @override
  State<CartIconWithBadge> createState() => _CartIconWithBadgeState();
}

class _CartIconWithBadgeState extends State<CartIconWithBadge> {
  final CartService _cartService = sl.get<CartService>();
  CartResponse? _cart;
  bool _isLoading = false;
  bool _hasError = false;

  @override
  void initState() {
    super.initState();
    _loadCart();
  }

  Future<void> _loadCart() async {
    if (_isLoading) return; // منع التحميل المتزامن

    setState(() {
      _isLoading = true;
      _hasError = false;
    });

    try {
      final cart = await _cartService.getCart();
      if (mounted) {
        setState(() {
          _cart = cart;
          _isLoading = false;
          _hasError = false;
        });
      }
    } catch (e) {
      print('Error loading cart: $e');
      if (mounted) {
        setState(() {
          _isLoading = false;
          _hasError = true;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final mediaQuery = MediaQuery.of(context);
    final iconSize = mediaQuery.size.width * 0.07; // Proportional icon size
    final badgeSize = iconSize * 0.6; // Badge size relative to icon

    return Stack(
      clipBehavior: Clip.none,
      children: [
        // أيقونة السلة
        IconButton(
          tooltip: 'view_cart'.tr(),
          icon: _isLoading
              ? SizedBox(
                  width: iconSize * 0.7,
                  height: iconSize * 0.7,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor: AlwaysStoppedAnimation<Color>(
                      Theme.of(context).primaryColor,
                    ),
                  ),
                )
              : const Icon(Icons.shopping_cart_outlined),
          iconSize: iconSize,
          padding: EdgeInsets.all(iconSize * 0.25),
          onPressed: () {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => const CartScreen(),
              ),
            ).then((_) => _loadCart());
          },
        ),

        // شارة عدد العناصر في السلة
        if (!_isLoading && _cart != null && _cart!.items.isNotEmpty)
          Positioned(
            right: 0,
            top: 0,
            child: Container(
              padding: EdgeInsets.all(badgeSize * 0.15),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.error,
                borderRadius: BorderRadius.circular(badgeSize / 2),
                border: Border.all(
                  color: Theme.of(context).colorScheme.surface,
                  width: 2,
                ),
                boxShadow: [
                  BoxShadow(
                    // استخدام withAlpha بدلاً من withOpacity
                    color: Colors.black.withAlpha(51), // 0.2 * 255 = 51
                    blurRadius: 4,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              constraints: BoxConstraints(
                minWidth: badgeSize,
                minHeight: badgeSize,
              ),
              child: Text(
                '${_cart!.items.length}',
                style: TextStyle(
                  color: Theme.of(context).colorScheme.onError,
                  fontSize: badgeSize * 0.5,
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
                semanticsLabel: 'cart_items'.tr(args: [_cart!.items.length.toString()]),
              ),
            ),
          ),

        // زر إعادة المحاولة في حالة الخطأ
        if (_hasError)
          Positioned(
            right: 0,
            bottom: 0,
            child: GestureDetector(
              onTap: _loadCart,
              child: Container(
                padding: const EdgeInsets.all(4),
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.error.withAlpha(200),
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  Icons.refresh,
                  size: iconSize * 0.5,
                  color: Theme.of(context).colorScheme.onError,
                ),
              ),
            ),
          ),
      ],
    );
  }
}
