import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';

import '../styles.dart';
import 'payment_method_bottom_sheet.dart';
import 'wallet_saved_card_item.dart';

enum AddBalanceBottomSheetActions {
  selectPayemntMethod,
  addBalance,
  goBack,
}

class AddBalanceBottomSheet extends StatelessWidget {
  const AddBalanceBottomSheet({
    Key? key,
    required this.parentContext,
    required this.title,
    required this.onSubmitButtonPressed,
    required this.submitButtonText,
  }) : super(key: key);

  final String title;
  final String submitButtonText;
  final BuildContext parentContext;
  final void Function() onSubmitButtonPressed;

  void _onSelectPayemntMethodPressed(BuildContext context) {
    Navigator.pop(context);
    showModalBottomSheet<AddBalanceBottomSheetActions>(
      context: parentContext,
      builder: (cxt) => PaymentMethodBottomSheet(
        parentContext: parentContext,
        title: title,
        onSubmitButtonPressed: onSubmitButtonPressed,
        submitButtonText: submitButtonText,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 20.0, vertical: 20),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                width: 70,
                height: 8,
                decoration: BoxDecoration(
                  color: Colors.grey[300],
                  borderRadius: BorderRadius.circular(20),
                ),
              ),
            ],
          ),
          const SizedBox(height: 20),
          Row(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              Text(
                title,
                style: Theme.of(context).textTheme.bodyLarge,
              ),
              const Spacer(),
              Text(
                '100',
                style: Theme.of(context).textTheme.titleLarge,
              ),
              const SizedBox(width: 5),
              Text(
                tr('saudi_riyal'),
                style: Theme.of(context).textTheme.labelMedium,
              ),
            ],
          ),
          const SizedBox(height: 20),
          Container(
            width: double.infinity,
            padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 10),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(10),
              boxShadow: [kBoxShadow],
            ),
            child: Column(
              children: [
                Row(
                  children: [
                    Text(
                      tr('send_through'),
                      style: const TextStyle(color: Colors.grey),
                    ),
                  ],
                ),
                const SizedBox(height: 20),
                WalletSavedCardItem(
                  image: const Icon(Icons.credit_card),
                  bankName: 'اسم البنك',
                  numberText: '1234*****',
                  onPressed: () => _onSelectPayemntMethodPressed(context),
                ),
                const SizedBox(height: 20),
              ],
            ),
          ),
          const Spacer(),
          SizedBox(
            width: double.infinity,
            child: ElevatedButton(
              onPressed: onSubmitButtonPressed,
              child: Text(submitButtonText),
              style: ButtonStyle(
                backgroundColor: MaterialStateProperty.all(Styles.secondaryColor),
              ),
            ),
          ),
          const SizedBox(height: 20),
        ],
      ),
    );
  }
}
