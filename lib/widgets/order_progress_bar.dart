import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:one_drop/models/order_model.dart';
import 'package:one_drop/styles.dart';

import '../constants.dart';

const widgetSize = 20.0;

class OrderProgressBar extends StatelessWidget {
  const OrderProgressBar({
    Key? key,
    required this.order,
  }) : super(key: key);

  final OrderModel order;

  @override
  Widget build(BuildContext context) {
    final step = kOrderStatusProgress[order.status] ?? 0;
    final carImage = SvgPicture.asset(
      'assets/images/26.svg',
      width: widgetSize,
      height: widgetSize,
    );
    const placeholderSizedBoxForCarImage = SizedBox(width: widgetSize, height: widgetSize);
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 40.0),
      child: Stack(
        clipBehavior: Clip.none,
        children: [
          Row(
            children: [
              Expanded(
                child: Container(
                  width: double.infinity,
                  height: 2,
                  color: step > 0 ? Styles.primaryColor : Colors.grey[300],
                ),
              ),
              Expanded(
                child: Container(
                  width: double.infinity,
                  height: 2,
                  color: step > 1 ? Styles.primaryColor : Colors.grey[300],
                ),
              ),
              Expanded(
                child: Container(
                  width: double.infinity,
                  height: 2,
                  color: step > 2 ? Styles.primaryColor : Colors.grey[300],
                ),
              ),
            ],
          ),
          Positioned(
            left: 0,
            right: 0,
            bottom: -30,
            top: -30,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                OrderProgressBarItem(
                  name: 'pending',
                  steps: const [0],
                  currentStep: step,
                ),
                OrderProgressBarItem(
                  name: 'in_progress',
                  currentStep: step,
                  steps: const [1],
                ),
                OrderProgressBarItem(
                  name: 'on_way',
                  currentStep: step,
                  steps: const [2],
                ),
                OrderProgressBarItem(
                  name: step == 4 ? K.orderStatusCancelled : 'completed',
                  currentStep: step,
                  steps: const [3, 4],
                ),
              ],
            ),
          ),
          Positioned(
            left: 0,
            right: 0,
            bottom: -30,
            top: -30,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                if (step == 0) carImage else placeholderSizedBoxForCarImage,
                if (step == 1) carImage else placeholderSizedBoxForCarImage,
                if (step == 2) carImage else placeholderSizedBoxForCarImage,
                if (step >= 3) carImage else placeholderSizedBoxForCarImage,
              ],
            ),
          ),
        ],
      ),
    );
  }
}

class OrderProgressBarItem extends StatelessWidget {
  const OrderProgressBarItem({
    Key? key,
    required this.name,
    required this.currentStep,
    required this.steps,
    this.textColor,
  }) : super(key: key);

  final int currentStep;
  final String name;
  final Color? textColor;
  final List<int> steps;

  @override
  Widget build(BuildContext context) {
    return Stack(
      clipBehavior: Clip.none,
      children: [
        if (!steps.contains(currentStep))
          Container(
            width: widgetSize,
            height: widgetSize,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: steps.every((element) => element <= currentStep) ? Styles.primaryColor : Colors.grey[300],
            ),
          )
        else
          const SizedBox(
            width: widgetSize,
            height: widgetSize,
          ),
        Positioned(
          left: -20,
          right: -20,
          bottom: -20,
          child: Text(
            tr(name),
            textAlign: TextAlign.center,
            style: Theme.of(context).textTheme.labelSmall?.copyWith(
                  color: steps.contains(currentStep) ? (kOrderStatusColor[name] ?? Colors.grey[400]) : Colors.grey[400],
                ),
          ),
        )
      ],
    );
  }
}
