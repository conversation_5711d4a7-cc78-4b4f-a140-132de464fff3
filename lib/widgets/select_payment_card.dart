import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:one_drop/styles.dart';

import '../models/payment_card_model.dart';
import '../screens/add_payment_card_screen.dart';

class SelectPaymentCard extends StatelessWidget {
  const SelectPaymentCard({
    Key? key,
    required this.savedCards,
    required this.onCardSelected,
    required this.selectedCard,
    required this.onNewCardAdded,
  }) : super(key: key);

  final PaymentCardModel? selectedCard;
  final void Function(PaymentCardModel) onCardSelected;
  final void Function(PaymentCardModel) onNewCardAdded;
  final List<PaymentCardModel> savedCards;

  _onAddCardPressed(BuildContext context) async {
    final PaymentCardModel? addedCard = await Navigator.pushNamed<dynamic>(context, AddPaymentCardScreen.routeName);
    if (addedCard == null) return;
    onNewCardAdded(addedCard);
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 20),
          child: Row(
            children: [
              Text(
                tr('select_card'),
                style: const TextStyle(color: Colors.grey),
              ),
            ],
          ),
        ),
        const SizedBox(height: 10),
        for (final card in savedCards)
          SavedPaymentCardItem(
            selected: selectedCard?.cardNumber == card.cardNumber,
            card: card,
            onPressed: onCardSelected,
          ),
        const SizedBox(height: 10),
        SizedBox(
          height: 40,
          child: TextButton.icon(
            onPressed: () => _onAddCardPressed(context),
            icon: const Icon(Icons.add_circle_outline),
            style: ButtonStyle(
              padding: MaterialStateProperty.all(const EdgeInsets.all(0)),
            ),
            label: Text(
              tr('add_new_card'),
              style: const TextStyle(color: Styles.secondaryColor),
            ),
          ),
        ),
      ],
    );
  }
}

class SavedPaymentCardItem extends StatelessWidget {
  const SavedPaymentCardItem({
    Key? key,
    required this.selected,
    required this.card,
    required this.onPressed,
  }) : super(key: key);

  final bool selected;
  final PaymentCardModel card;
  final void Function(PaymentCardModel) onPressed;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () => onPressed(card),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 15),
        color: Colors.grey[200],
        child: Row(
          children: [
            Container(
              width: 20,
              height: 20,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                border: Border.all(color: Colors.grey[300]!, width: 1),
                color: selected ? Styles.primaryColor : Colors.white,
              ),
              alignment: Alignment.center,
              child: Container(
                width: 8,
                height: 8,
                decoration: const BoxDecoration(
                  shape: BoxShape.circle,
                  color: Colors.white,
                ),
              ),
            ),
            if (card.imagePath != null) const SizedBox(width: 10),
            if (card.imagePath != null)
              Image.asset(
                card.imagePath!,
                width: 30,
                height: 20,
              ),
            const SizedBox(width: 10),
            // const Text('اسم البنك'),
            // const SizedBox(width: 10),
            Text(
              card.concealedCardNumber,
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
          ],
        ),
      ),
    );
  }
}
