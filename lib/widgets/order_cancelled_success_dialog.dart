import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

import '../screens/main_tab_controller.dart';
import '../styles.dart';
import 'od_alert_dialog.dart';

class OrderCancelledSuccessDialog extends StatefulWidget {
  const OrderCancelledSuccessDialog({
    Key? key,
    required this.orderNumber,
  }) : super(key: key);

  final String orderNumber;

  @override
  State<OrderCancelledSuccessDialog> createState() => _OrderCancelledSuccessDialogState();
}

class _OrderCancelledSuccessDialogState extends State<OrderCancelledSuccessDialog> {
  @override
  void initState() {
    super.initState();

    Future.delayed(const Duration(seconds: 2), () {
      Navigator.pushAndRemoveUntil(
        context,
        CupertinoPageRoute(
          builder: (cxt) => const MainTabController(initialTab: 1),
          settings: const RouteSettings(name: MainTabController.routeName),
        ),
        (route) => false,
      );
    });
  }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: () async {
        return false;
      },
      child: ODAlertDialog(
        child: Column(
          children: [
            Container(
              width: 50,
              height: 50,
              decoration: BoxDecoration(
                color: Styles.primaryColor.withOpacity(0.1),
                shape: BoxShape.circle,
              ),
              child: const Icon(Icons.check, color: Styles.primaryColor),
            ),
            const SizedBox(height: 20),
            Text(
              tr('order_canceled_successfully'),
              style: Theme.of(context).textTheme.labelLarge,
            ),
            const SizedBox(height: 20),
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text('${tr('order_number')}:  '),
                Text(
                  '#${widget.orderNumber}',
                  style: const TextStyle(color: Colors.grey),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
