import 'package:flutter/material.dart';
import 'package:one_drop/models/brand.dart';
import 'package:one_drop/models/product.dart' as store_product;
import 'package:one_drop/screens/cart_screen.dart';
import 'package:one_drop/screens/product_details_screen.dart';
import 'package:one_drop/services/cart_service.dart';
import 'package:one_drop/services/store_service.dart';
import 'package:one_drop/styles.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:one_drop/widgets/common_app_bar.dart';

class StoreTab extends StatefulWidget {
  const StoreTab({Key? key}) : super(key: key);

  @override
  State<StoreTab> createState() => _StoreTabState();
}

class _StoreTabState extends State<StoreTab> {
  final _storeService = StoreService();
  final _cartService = CartService();
  List<store_product.Product> _allProducts = []; // Store all products
  List<store_product.Product> _filteredProducts = []; // Store filtered products
  bool _isLoading = true;
  String? _error;
  final _searchController = TextEditingController();
  String _sortBy = 'name';
  Brand? _selectedBrand;
  Set<Brand> _availableBrands = {};
  Map<int, bool> _addingToCart = {};

  @override
  void initState() {
    super.initState();
    _loadProducts();
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  Future<void> _loadProducts({String? search}) async {
    try {
      setState(() {
        _isLoading = true;
        _error = null;
      });

      final products = await _storeService.getProducts(search: search);
      setState(() {
        _allProducts = products.cast<store_product.Product>();
        _availableBrands = products.map((p) => p.brand).toSet();
        _filterProducts();
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _error = e.toString();
        _isLoading = false;
      });
    }
  }

  void _filterProducts() {
    List<store_product.Product> filtered;
    if (_selectedBrand == null) {
      filtered = List<store_product.Product>.from(_allProducts);
    } else {
      filtered = _allProducts
          .where((product) => product.brand.id == _selectedBrand!.id)
          .toList();
    }

    _sortProducts(products: filtered);
  }

  void _sortProducts({List<store_product.Product>? products}) {
    final productsToSort =
        List<store_product.Product>.from(products ?? _allProducts);

    switch (_sortBy) {
      case 'price_low':
        productsToSort.sort((a, b) => a.price.compareTo(b.price));
        break;
      case 'price_high':
        productsToSort.sort((a, b) => b.price.compareTo(a.price));
        break;
      default:
        productsToSort.sort((a, b) => a.name.compareTo(b.name));
    }

    setState(() {
      _filteredProducts = productsToSort;
    });
  }

  void _toggleBrandSelection(Brand brand) {
    setState(() {
      if (_selectedBrand?.id == brand.id) {
        _selectedBrand = null;
      } else {
        _selectedBrand = brand;
      }
      _filterProducts();
    });
  }

  Future<void> _addToCart(store_product.Product product) async {
    if (_addingToCart[product.id] == true) return; // Prevent double taps

    setState(() {
      _addingToCart[product.id] = true;
    });

    try {
      await _cartService.addItem(product.id, quantity: 1);
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('${product.name} ' + 'added_to_cart'.tr()),
          duration: const Duration(seconds: 2),
          action: SnackBarAction(
            label: 'VIEW CART',
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const CartScreen(),
                ),
              );
            },
          ),
        ),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('${product.name} ' + 'failed_to_add_to_cart'.tr()),
          backgroundColor: Colors.red,
        ),
      );
    } finally {
      setState(() {
        _addingToCart[product.id] = false;
      });
    }
  }

  void _cancelAddToCart(store_product.Product product) {
    setState(() {
      _addingToCart[product.id] = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CommonAppBar(
        title: 'store_title'.tr(),
        showBackButton: false,
        showCartIcon: true,
      ),
      body: Column(
        children: [
          Padding(
            padding: const EdgeInsets.all(Styles.spacing16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                TextField(
                  controller: _searchController,
                  decoration: InputDecoration(
                    hintText: 'search_products'.tr(),
                    hintStyle: TextStyle(color: Styles.textSecondaryColor),
                    prefixIcon:
                        Icon(Icons.search, color: Styles.textSecondaryColor),
                    suffixIcon: _searchController.text.isNotEmpty
                        ? IconButton(
                            icon: Icon(Icons.clear,
                                color: Styles.textSecondaryColor),
                            onPressed: () {
                              _searchController.clear();
                              _loadProducts();
                            },
                          )
                        : null,
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(Styles.spacing12),
                      borderSide: BorderSide(color: Styles.lightGrey),
                    ),
                    enabledBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(Styles.spacing12),
                      borderSide: BorderSide(color: Styles.lightGrey),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(Styles.spacing12),
                      borderSide: BorderSide(color: Styles.primaryColor),
                    ),
                    filled: true,
                    fillColor: Styles.surfaceColor,
                  ),
                  onChanged: (value) {
                    _loadProducts(search: value);
                  },
                ),
                const SizedBox(height: Styles.spacing24),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'store_brands'.tr(),
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                            color: Styles.textPrimaryColor,
                            fontWeight: FontWeight.bold,
                          ),
                    ),
                    Container(
                      padding: const EdgeInsets.symmetric(
                          horizontal: Styles.spacing8),
                      decoration: BoxDecoration(
                        border: Border.all(color: Styles.lightGrey),
                        borderRadius: BorderRadius.circular(Styles.spacing8),
                      ),
                      child: DropdownButton<String>(
                        value: _sortBy,
                        underline: const SizedBox(),
                        icon:
                            Icon(Icons.sort, color: Styles.textSecondaryColor),
                        items: [
                          DropdownMenuItem(
                            value: 'name',
                            child: Text('store_sort_by_name'.tr()),
                          ),
                          DropdownMenuItem(
                            value: 'price_low',
                            child: Text('store_sort_price_low_to_high'.tr()),
                          ),
                          DropdownMenuItem(
                            value: 'price_high',
                            child: Text('store_sort_price_high_to_low'.tr()),
                          ),
                        ],
                        onChanged: (String? value) {
                          if (value != null) {
                            setState(() {
                              _sortBy = value;
                              _sortProducts();
                            });
                          }
                        },
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: Styles.spacing16),
                if (_availableBrands.isNotEmpty)
                  SizedBox(
                    height: 40,
                    child: ListView.builder(
                      scrollDirection: Axis.horizontal,
                      itemCount: _availableBrands.length,
                      itemBuilder: (context, index) {
                        final brand = _availableBrands.elementAt(index);
                        final isSelected = _selectedBrand?.id == brand.id;
                        return Padding(
                          padding:
                              const EdgeInsets.only(right: Styles.spacing8),
                          child: FilterChip(
                            selected: isSelected,
                            label: Text(
                              brand.name,
                              style: TextStyle(
                                color: isSelected
                                    ? Styles.primaryColor
                                    : Styles.textPrimaryColor,
                                fontWeight: isSelected
                                    ? FontWeight.bold
                                    : FontWeight.normal,
                              ),
                            ),
                            onSelected: (_) => _toggleBrandSelection(brand),
                            selectedColor: Styles.primaryColor.withOpacity(0.1),
                            backgroundColor: Styles.surfaceColor,
                            shape: RoundedRectangleBorder(
                              borderRadius:
                                  BorderRadius.circular(Styles.spacing16),
                              side: BorderSide(
                                color: isSelected
                                    ? Styles.primaryColor
                                    : Styles.lightGrey,
                              ),
                            ),
                          ),
                        );
                      },
                    ),
                  ),
                if (_availableBrands.isEmpty && !_isLoading)
                  Center(
                    child: Text(
                      'no_brands_available'.tr(),
                      style: TextStyle(color: Styles.textSecondaryColor),
                    ),
                  ),
              ],
            ),
          ),
          Expanded(
            child: _error != null
                ? Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        const Icon(
                          Icons.error_outline,
                          color: Colors.red,
                          size: 48,
                        ),
                        const SizedBox(height: 16),
                        Text(
                          _error!,
                          style:
                              Theme.of(context).textTheme.bodyMedium?.copyWith(
                                    color: Colors.red,
                                  ),
                        ),
                      ],
                    ),
                  )
                : _isLoading
                    ? const Center(
                        child: CircularProgressIndicator(),
                      )
                    : _filteredProducts.isEmpty
                        ? Center(
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Icon(
                                  Icons.shopping_bag_outlined,
                                  size: 48,
                                  color: Colors.grey[400],
                                ),
                                const SizedBox(height: 16),
                                Text(
                                  'no_products_found'.tr(),
                                  style: Theme.of(context)
                                      .textTheme
                                      .titleMedium
                                      ?.copyWith(
                                        color: Colors.grey[600],
                                      ),
                                ),
                              ],
                            ),
                          )
                        : GridView.builder(
                            padding: const EdgeInsets.all(Styles.spacing16),
                            gridDelegate:
                                const SliverGridDelegateWithFixedCrossAxisCount(
                              crossAxisCount: 2,
                              childAspectRatio: 0.6,
                              crossAxisSpacing: 16,
                              mainAxisSpacing: 16,
                            ),
                            itemCount: _filteredProducts.length,
                            itemBuilder: (context, index) {
                              final product = _filteredProducts[index];
                              return GestureDetector(
                                onTap: () {
                                  Navigator.push(
                                    context,
                                    MaterialPageRoute(
                                      builder: (context) =>
                                          ProductDetailsScreen(
                                        productId: product.id,
                                      ),
                                    ),
                                  );
                                },
                                child: Card(
                                  clipBehavior: Clip.antiAlias,
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(12),
                                  ),
                                  elevation: 2,
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Hero(
                                        tag: 'product-${product.id}',
                                        child: AspectRatio(
                                          aspectRatio: 1,
                                          child: Image.network(
                                            product.image,
                                            fit: BoxFit.cover,
                                            errorBuilder:
                                                (context, error, stackTrace) {
                                              return Container(
                                                color: Colors.grey[200],
                                                child: const Icon(
                                                  Icons.image_not_supported,
                                                  color: Colors.grey,
                                                ),
                                              );
                                            },
                                          ),
                                        ),
                                      ),
                                      Expanded(
                                        child: Container(
                                          padding: const EdgeInsets.all(8),
                                          child: Column(
                                            crossAxisAlignment:
                                                CrossAxisAlignment.start,
                                            children: [
                                              Flexible(
                                                child: Text(
                                                  product.name,
                                                  style: Theme.of(context)
                                                      .textTheme
                                                      .titleSmall
                                                      ?.copyWith(
                                                        fontWeight:
                                                            FontWeight.bold,
                                                      ),
                                                  maxLines: 1,
                                                  overflow:
                                                      TextOverflow.ellipsis,
                                                ),
                                              ),
                                              const SizedBox(height: 2),
                                              Flexible(
                                                child: Text(
                                                  product.brand.name,
                                                  style: Theme.of(context)
                                                      .textTheme
                                                      .bodySmall
                                                      ?.copyWith(
                                                        color: Colors.grey[600],
                                                      ),
                                                  maxLines: 1,
                                                  overflow:
                                                      TextOverflow.ellipsis,
                                                ),
                                              ),
                                              const SizedBox(height: 2),
                                              Flexible(
                                                child: Text(
                                                  '${product.price.toStringAsFixed(2)} ر.س',
                                                  style: Theme.of(context)
                                                      .textTheme
                                                      .titleMedium
                                                      ?.copyWith(
                                                        color:
                                                            Styles.primaryColor,
                                                        fontWeight:
                                                            FontWeight.bold,
                                                      ),
                                                ),
                                              ),
                                              if (product.quantity == 0)
                                                Padding(
                                                  padding:
                                                      const EdgeInsets.only(
                                                          top: 4),
                                                  child: Container(
                                                    padding: const EdgeInsets
                                                        .symmetric(
                                                      horizontal: 6,
                                                      vertical: 2,
                                                    ),
                                                    decoration: BoxDecoration(
                                                      color: Colors.red[50],
                                                      borderRadius:
                                                          BorderRadius.circular(
                                                              4),
                                                    ),
                                                    child: Text(
                                                      'Out of Stock',
                                                      style: Theme.of(context).textTheme.labelSmall?.copyWith(
                                                        color: Colors.red[700],
                                                      ),
                                                    ),
                                                  ),
                                                )
                                              else
                                                Padding(
                                                  padding:
                                                      const EdgeInsets.only(
                                                          top: 4),
                                                  child: SizedBox(
                                                    width: double.infinity,
                                                    height: 24,
                                                    child:
                                                        _addingToCart[product
                                                                    .id] ==
                                                                true
                                                            ? ElevatedButton(
                                                                style: ElevatedButton
                                                                    .styleFrom(
                                                                  backgroundColor:
                                                                      Colors.red[
                                                                          400],
                                                                  padding: const EdgeInsets
                                                                      .symmetric(
                                                                      horizontal:
                                                                          8),
                                                                ),
                                                                onPressed: () =>
                                                                    _cancelAddToCart(
                                                                        product),
                                                                child: Row(
                                                                  mainAxisAlignment:
                                                                      MainAxisAlignment
                                                                          .center,
                                                                  children: [
                                                                    SizedBox(
                                                                      width: 16,
                                                                      height:
                                                                          16,
                                                                      child:
                                                                          CircularProgressIndicator(
                                                                        strokeWidth:
                                                                            2,
                                                                        valueColor:
                                                                            AlwaysStoppedAnimation<Color>(Colors.white),
                                                                      ),
                                                                    ),
                                                                    const SizedBox(
                                                                        width:
                                                                            4),
                                                                    Text(
                                                                        'cancel'.tr(),
                                                                        style: Theme.of(context).textTheme.labelSmall),
                                                                  ],
                                                                ),
                                                              )
                                                            : ElevatedButton(
                                                                style: ElevatedButton
                                                                    .styleFrom(
                                                                  padding: const EdgeInsets
                                                                      .symmetric(
                                                                      horizontal:
                                                                          8),
                                                                ),
                                                                onPressed: () =>
                                                                    _addToCart(
                                                                        product),
                                                                child: Text(
                                                                    'add_to_cart'.tr(),
                                                                    style: Theme.of(context).textTheme.labelSmall),
                                                              ),
                                                  ),
                                                ),
                                            ],
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              );
                            },
                          ),
          ),
        ],
      ),
    );
  }
}
