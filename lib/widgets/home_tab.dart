import 'package:bot_toast/bot_toast.dart';
import 'package:flutter_carousel_widget/flutter_carousel_widget.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:one_drop/api/user_api.dart';
import 'package:one_drop/models/home_tab_model.dart';
import 'package:one_drop/providers/user_provider.dart';
import 'package:provider/provider.dart';
import 'package:url_launcher/url_launcher.dart';

import '../models/home_banner_item_model.dart';
import '../models/package_item_model.dart';
import '../screens/monthly_packages_screen.dart';
import '../screens/notifications_screen.dart';
import '../styles.dart';
import 'home_card_item.dart';
import 'home_create_wash_order_list.dart';
import 'od_alert_dialog.dart';

class HomeTab extends StatefulWidget {
  const HomeTab({Key? key}) : super(key: key);

  @override
  State<HomeTab> createState() => _HomeTabState();
}

class _HomeTabState extends State<HomeTab> {
  var _loading = true;
  HomeTabModel? _homeModel;

  @override
  void initState() {
    super.initState();

    _initState();
  }

  void _initState() async {
    _homeModel = await UserAPI().getHomeContent();
    _loading = false;
    if (!mounted) return;
    setState(() {});
  }

  void _onBannerPressed(HomeBannerItemModel banner) {
    if (banner.isActive) {
      if (banner.link != null) {
        launchUrl(Uri.parse(banner.link!));
      } else {
        try {
          final category = _homeModel?.categories
              .firstWhere((element) => element.id == banner.categoryID);
          if (category == null) return;
          HomeCreateWashOrderListItem.onPressed(
            context: context,
            orderCategory: category,
            hasFreeWash: false,
          );
        } catch (_) {}
      }
    } else {
      showDialog<bool>(
        context: context,
        builder: (cxt) => ODAlertDialog(
          actions: [
            TextButton(
              onPressed: () {
                Navigator.pop(cxt);
              },
              style: ButtonStyle(
                foregroundColor: WidgetStateProperty.all(Colors.black),
              ),
              child: Text(tr('close')),
            )
          ],
          child: Text(tr('service_currently_unavailable')),
        ),
      );
    }
  }

  void _onPackagesPressed() async {
    BotToast.showLoading();
    final packagesStatus = await UserAPI().getPackagesStatus();
    BotToast.closeAllLoading();
    if (packagesStatus.enabled) {
      //
      Navigator.push(
        context,
        CupertinoPageRoute(
          builder: (cxt) => const MonthlyPackagesScreen(package: null),
          settings: const RouteSettings(name: MonthlyPackagesScreen.routeName),
        ),
      );
    } else {
      showDialog<bool>(
        context: context,
        builder: (cxt) => ODAlertDialog(
          actions: [
            TextButton(
              onPressed: () {
                Navigator.pop(cxt);
              },
              style: ButtonStyle(
                foregroundColor: WidgetStateProperty.all(Colors.black),
              ),
              child: Text(tr('close')),
            )
          ],
          child: Text(packagesStatus.message),
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final currentLang = EasyLocalization.of(context)?.locale.languageCode;
    final currentUser = context.watch<UserProvider>().userModel;
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final sliderHeight =
        screenHeight * 0.28; // Approximately 28% of screen height

    List<Widget> listItems;
    if (_loading) {
      listItems = [
        const Padding(
          padding: EdgeInsets.only(top: 50.0),
          child: Center(child: CircularProgressIndicator()),
        )
      ];
    } else if (_homeModel == null) {
      listItems = [
        Padding(
          padding: const EdgeInsets.only(top: 50.0),
          child: Center(child: Text(tr('error'))),
        )
      ];
    } else {
      listItems = [
        SizedBox(
          height: sliderHeight,
          child: FlutterCarousel.builder(
            itemCount: _homeModel?.banners.length ?? 0,
            itemBuilder: (cxt, index, i) {
              return Padding(
                padding: EdgeInsets.symmetric(horizontal: screenWidth * 0.01),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(10),
                  child: SizedBox(
                    width: MediaQuery.of(context).size.width * 0.8,
                    height: MediaQuery.of(context).size.width * 0.8,
                    child: HomeCardItem(
                      banner: _homeModel!.banners[index],
                      onPressed: _onBannerPressed,
                      noRadius: true,
                    ),
                  ),
                ),
              );
            },
            options: FlutterCarouselOptions(
                height: sliderHeight,
                enlargeCenterPage: true,
                enableInfiniteScroll: true,
                autoPlay: true,
                autoPlayInterval: const Duration(seconds: 4),
                autoPlayAnimationDuration: const Duration(milliseconds: 800),
                autoPlayCurve: Curves.fastOutSlowIn,
                viewportFraction:
                    0.8, // تصغير العنصر الحالي ليظهر أطراف الشرائح المجاورة
                aspectRatio: 16 / 9,
                disableCenter: false,
                padEnds: true,
                enlargeFactor: 0.15, // تقليل معامل التكبير
                showIndicator: true,
                indicatorMargin: 8,
                slideIndicator: CircularSlideIndicator(
                  slideIndicatorOptions: SlideIndicatorOptions(
                    indicatorRadius: 4,
                    itemSpacing: 10,
                    currentIndicatorColor: Styles.primaryColor,
                    indicatorBackgroundColor: Colors.grey.withOpacity(0.3),
                  ),
                )),
          ),
        ),
        SizedBox(height: screenHeight * 0.025),
        HomeCreateWashOrderList(
          categories: currentLang == 'ar'
              ? (_homeModel?.categories ?? []).reversed.toList()
              : _homeModel?.categories ?? [],
        ),
        const SizedBox(height: 20),
        if ((_homeModel?.packages ?? <PackageItemModel>[]).isNotEmpty)
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Expanded(
                child: Padding(
                  padding:
                      const EdgeInsets.only(left: 20, right: 20, bottom: 20),
                  child: InkWell(
                    onTap: _onPackagesPressed,
                    child: Container(
                      constraints: const BoxConstraints(maxWidth: 400),
                      decoration: BoxDecoration(
                        color: Styles.lightGrey,
                        borderRadius: BorderRadius.circular(10),
                      ),
                      padding: const EdgeInsets.symmetric(
                          horizontal: 20, vertical: 20),
                      child: Row(
                        children: [
                          SizedBox(
                            width: 40,
                            height: 40,
                            child: SvgPicture.asset('assets/images/7.svg'),
                            // child: SvgPicture.asset('assets/images/7.svg'),
                          ),
                          const SizedBox(width: 20),
                          Text(
                            tr('monthly_packages'),
                            style: Theme.of(context)
                                .textTheme
                                .titleMedium
                                ?.copyWith(
                                  fontWeight: FontWeight.bold,
                                ),
                          ),
                          const Spacer(),
                          Container(
                            padding: const EdgeInsets.all(7),
                            decoration: BoxDecoration(
                              color: Colors.white,
                              borderRadius: BorderRadius.circular(5),
                              boxShadow: [
                                BoxShadow(
                                    color: Colors.grey[300]!, blurRadius: 5),
                              ],
                            ),
                            child: const Icon(Icons.arrow_forward),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
        const SizedBox(height: 20),
      ];
    }
    return SafeArea(
      child: CustomScrollView(
        slivers: [
          SliverAppBar(
            pinned: true,
            floating: true,
            elevation: 5,
            shadowColor: Colors.grey[100],
            backgroundColor: Colors.white,
            title: Padding(
              padding: kAppBarItemsPadding,
              child: Text(
                '${tr('welcome')} ${currentUser?.name ?? ''}',
                style: Theme.of(context).textTheme.titleMedium,
              ),
            ),
            centerTitle: false,
            automaticallyImplyLeading: false,
            actions: [
              if (currentUser?.id != null)
                Padding(
                  padding: kAppBarItemsPadding,
                  child: IconButton(
                    onPressed: () {
                      Navigator.pushNamed(
                          context, NotificationsScreen.routeName);
                    },
                    icon: const Icon(Icons.notifications_none),
                  ),
                ),
            ],
          ),
          SliverList(
            delegate: SliverChildBuilderDelegate(
              (cxt, index) {
                return listItems[index];
              },
              childCount: listItems.length,
            ),
          ),
        ],
      ),
    );
  }
}
