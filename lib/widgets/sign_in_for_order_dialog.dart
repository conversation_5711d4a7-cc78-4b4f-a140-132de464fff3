import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';

import 'od_alert_dialog.dart';

class SignInForOrderDialog extends StatelessWidget {
  const SignInForOrderDialog({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return ODAlertDialog(
      child: SingleChildScrollView(
        child: Column(
          children: [
            Text(
              tr('sign_in_now'),
              style: Theme.of(context).textTheme.labelLarge,
            ),
            const SizedBox(height: 10),
            Text(
              tr('please_sign_in_to_complete_order'),
            ),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () {
            Navigator.pop(context, true);
          },
          child: Text(tr('sign_in')),
          style: ButtonStyle(
            foregroundColor: MaterialStateProperty.all(Colors.black),
          ),
        ),
        TextButton(
          onPressed: () {
            Navigator.pop(context);
          },
          child: Text(tr('not_now')),
          style: Button<PERSON>tyle(
            foregroundColor: MaterialStateProperty.all(Colors.grey),
          ),
        ),
      ],
    );
  }
}
