import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';

import '../styles.dart';
import 'add_balance_bottom_sheet.dart';

class PaymentMethodBottomSheet extends StatelessWidget {
  const PaymentMethodBottomSheet({
    Key? key,
    required this.title,
    required this.submitButtonText,
    required this.parentContext,
    required this.onSubmitButtonPressed,
  }) : super(key: key);

  final String title;
  final String submitButtonText;
  final void Function() onSubmitButtonPressed;
  final BuildContext parentContext;

  void _onBackPressed(BuildContext context) {
    Navigator.pop(context);
    showModalBottomSheet(
      context: parentContext,
      builder: (cxt) => AddBalanceBottomSheet(
        title: title,
        submitButtonText: submitButtonText,
        parentContext: parentContext,
        onSubmitButtonPressed: onSubmitButtonPressed,
      ),
    );
  }

  void _onMethodSelected(BuildContext context) {
    Navigator.pop(context);
    showModalBottomSheet(
      context: parentContext,
      builder: (cxt) => AddBalanceBottomSheet(
        title: title,
        submitButtonText: submitButtonText,
        parentContext: parentContext,
        onSubmitButtonPressed: onSubmitButtonPressed,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 20.0, vertical: 20),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                width: 70,
                height: 8,
                decoration: BoxDecoration(
                  color: Colors.grey[300],
                  borderRadius: BorderRadius.circular(20),
                ),
              ),
            ],
          ),
          const SizedBox(height: 20),
          Stack(
            children: [
              Padding(
                padding: const EdgeInsets.symmetric(vertical: 10),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      tr('select_payment_method'),
                      style: Theme.of(context).textTheme.labelLarge,
                    ),
                  ],
                ),
              ),
              Positioned.fill(
                  child: Row(
                children: [
                  IconButton(
                    padding: const EdgeInsets.all(0),
                    onPressed: () => _onBackPressed(context),
                    icon: const Icon(Icons.arrow_back),
                  ),
                ],
              ))
            ],
          ),
          const SizedBox(height: 20),
          Container(
            width: double.infinity,
            padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 10),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(10),
              boxShadow: [kBoxShadow],
            ),
            child: Column(
              children: [
                Row(
                  children: [
                    Text(
                      tr('select_payment_method'),
                      style: const TextStyle(color: Colors.grey),
                    ),
                  ],
                ),
                const SizedBox(height: 20),
                PaymentMethodBottomSheetPaymentMethodItem(
                  icon: const Icon(Icons.credit_card, color: Styles.primaryColor),
                  name: 'اسم البنك',
                  selected: true,
                  subTitle: '1234****',
                  onPressed: () => _onMethodSelected(context),
                ),
                const SizedBox(height: 10),
                PaymentMethodBottomSheetPaymentMethodItem(
                  icon: const Icon(Icons.credit_card, color: Styles.primaryColor),
                  name: 'اسم البنك',
                  selected: false,
                  subTitle: '1234****',
                  onPressed: () => _onMethodSelected(context),
                ),
                const Divider(),
                TextButton(
                  onPressed: () {},
                  child: Row(
                    children: [
                      const Icon(Icons.add_circle_outline),
                      const SizedBox(width: 10),
                      Text(
                        tr('add_new_card'),
                        style: const TextStyle(color: Styles.secondaryColor),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

class PaymentMethodBottomSheetPaymentMethodItem extends StatelessWidget {
  const PaymentMethodBottomSheetPaymentMethodItem({
    required this.icon,
    required this.name,
    required this.subTitle,
    required this.selected,
    this.onPressed,
    Key? key,
  }) : super(key: key);

  final Widget icon;
  final String name;
  final String subTitle;
  final bool selected;
  final void Function()? onPressed;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onPressed,
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 5.0),
        child: Row(
          children: [
            icon,
            // Container(
            //   width: 50,
            //   height: 50,
            //   decoration: BoxDecoration(
            //     color: Styles.primaryColor.withOpacity(0.1),
            //     shape: BoxShape.circle,
            //   ),
            //   alignment: Alignment.center,
            //   child: ,
            // ),
            const SizedBox(width: 20),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    name,
                    style: Theme.of(context).textTheme.labelLarge,
                  ),
                  Text(
                    subTitle,
                    style: const TextStyle(color: Colors.grey),
                  ),
                ],
              ),
            ),
            Container(
              width: 20,
              height: 20,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: selected ? Styles.primaryColor : Colors.white,
                border: selected ? null : Border.all(color: Colors.grey, width: 1),
              ),
              alignment: Alignment.center,
              child: selected
                  ? Container(
                      width: 8,
                      height: 8,
                      decoration: const BoxDecoration(
                        shape: BoxShape.circle,
                        color: Colors.white,
                      ),
                    )
                  : null,
            ),
          ],
        ),
      ),
    );
  }
}
