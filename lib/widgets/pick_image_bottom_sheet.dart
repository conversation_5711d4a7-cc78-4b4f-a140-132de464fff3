import 'dart:io';

import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';

class PickImageBottomSheet extends StatefulWidget {
  const PickImageBottomSheet({
    Key? key,
  }) : super(key: key);

  @override
  State<PickImageBottomSheet> createState() => _PickImageBottomSheetState();
}

class _PickImageBottomSheetState extends State<PickImageBottomSheet> {
  final _picker = ImagePicker();

  void _cameraSelected() async {
    final pickedImage = await _picker.pickImage(
      source: ImageSource.camera,
      imageQuality: 50,
      maxHeight: 500,
      maxWidth: 500,
    );
    if (pickedImage == null) return;
    if (!mounted) return;
    Navigator.pop(context, File(pickedImage.path));
  }

  void _librarySelected() async {
    final pickedImage = await _picker.pickImage(
      source: ImageSource.gallery,
      imageQuality: 50,
      maxHeight: 500,
      maxWidth: 500,
    );
    if (pickedImage == null) return;
    if (!mounted) return;
    Navigator.pop(context, File(pickedImage.path));
  }

  @override
  Widget build(BuildContext context) {
    // final translations = Provider.of<UserChangeNotifier>(context, listen: true).translations;
    return SizedBox(
      height: 200,
      child: Column(
        children: [
          const SizedBox(height: 30),
          SizedBox(
            width: double.infinity,
            child: TextButton.icon(
              style: ButtonStyle(
                backgroundColor: MaterialStateProperty.all(Theme.of(context).scaffoldBackgroundColor),
                foregroundColor: MaterialStateProperty.all(Theme.of(context).primaryColor),
              ),
              onPressed: _cameraSelected,
              icon: const Icon(Icons.camera_alt),
              label: Text(tr('camera')),
            ),
          ),
          const SizedBox(height: 20),
          SizedBox(
            width: double.infinity,
            child: TextButton.icon(
              style: ButtonStyle(
                backgroundColor: MaterialStateProperty.all(Theme.of(context).scaffoldBackgroundColor),
                foregroundColor: MaterialStateProperty.all(Theme.of(context).primaryColor),
              ),
              onPressed: _librarySelected,
              icon: const Icon(Icons.photo_library),
              label: Text(tr('gallery')),
            ),
          ),
        ],
      ),
    );
  }
}
