import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:one_drop/api/user_api.dart';
import 'package:one_drop/models/order_model.dart';
import 'package:one_drop/screens/order_details_screen.dart';

import '../constants.dart';
import '../styles.dart';

class MyOrdersTab extends StatefulWidget {
  const MyOrdersTab({Key? key}) : super(key: key);

  @override
  State<MyOrdersTab> createState() => _MyOrdersTabState();
}

class _MyOrdersTabState extends State<MyOrdersTab> {
  var _loading = true;
  List<OrderModel>? _orders;

  @override
  void initState() {
    super.initState();

    _getOrders();
  }

  void _getOrders() async {
    _orders = await UserAPI().getUserOrders();
    _loading = false;
    if (!mounted) return;
    setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    const xPadding = EdgeInsets.symmetric(horizontal: 20, vertical: 10);
    List<Widget> listItems;
    if (_loading) {
      listItems = [
        const Padding(
          padding: EdgeInsets.only(top: 30.0),
          child: Center(child: CircularProgressIndicator()),
        ),
      ];
    } else if (_orders == null) {
      listItems = [
        Padding(
          padding: const EdgeInsets.only(top: 30.0),
          child: Center(child: Text(tr('error'))),
        ),
      ];
    } else if (_orders!.isEmpty) {
      listItems = [
        Padding(
          padding: const EdgeInsets.only(top: 30.0),
          child: Center(child: Text(tr('no_orders_found'))),
        ),
      ];
    } else {
      listItems = <Widget>[
        for (final order in _orders!)
          Padding(
            padding: xPadding,
            child: OrderListItem(order: order),
          ),
        const SizedBox(height: 20),
      ];
    }
    return SafeArea(
      child: CustomScrollView(
        slivers: [
          SliverAppBar(
            pinned: true,
            floating: true,
            elevation: 5,
            shadowColor: Colors.grey[100],
            backgroundColor: Colors.white,
            title: Text(
              tr('my_orders'),
              style: Theme.of(context).textTheme.titleLarge,
            ),
            centerTitle: false,
            automaticallyImplyLeading: false,
          ),
          SliverList(
            delegate: SliverChildBuilderDelegate(
              (cxt, index) {
                return listItems[index];
              },
              childCount: listItems.length,
            ),
          ),
        ],
      ),
    );
  }
}

class OrderListItem extends StatelessWidget {
  const OrderListItem({Key? key, required this.order}) : super(key: key);

  final OrderModel order;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () {
        Navigator.push(
          context,
          CupertinoPageRoute(
            builder: (cxt) => OrderDetailsScreen(order: order),
            settings: const RouteSettings(name: OrderDetailsScreen.routeName),
          ),
        );
      },
      child: Container(
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(10),
          boxShadow: [kBoxShadow],
        ),
        padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 10),
        width: double.infinity,
        child: Column(
          children: [
            Row(
              children: [
                Text(
                  '${tr('order_number')} #${order.orderNumber}',
                  style: Theme.of(context).textTheme.labelSmall?.copyWith(
                        color: Colors.grey,
                      ),
                ),
                const Spacer(),
                Text(
                  '${order.finalCost} ${tr('sr')}',
                  style: const TextStyle(color: Styles.primaryColor),
                ),
              ],
            ),
            const SizedBox(height: 10),
            Row(
              children: [
                Text(
                  order.category?.name ?? '',
                  style: Theme.of(context).textTheme.titleMedium,
                ),
              ],
            ),
            const SizedBox(height: 10),
            Row(
              children: [
                const Icon(Icons.location_on, color: Styles.primaryColor),
                const SizedBox(width: 5),
                Expanded(
                  child: Text(
                    order.address,
                    style: Theme.of(context).textTheme.labelMedium,
                  ),
                ),
              ],
            ),
            const Divider(height: 35),
            Row(
              children: [
                Text(
                  '${tr('order_status')}: ',
                  style: Theme.of(context).textTheme.labelSmall,
                ),
                Text(
                  tr(order.status ?? ''),
                  style: Theme.of(context).textTheme.labelSmall?.copyWith(
                        color: kOrderStatusColor[order.status ?? ''],
                      ),
                ),
              ],
            ),
            const SizedBox(height: 10),
          ],
        ),
      ),
    );
  }
}
