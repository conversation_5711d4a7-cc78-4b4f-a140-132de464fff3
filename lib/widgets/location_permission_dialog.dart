import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';

import 'od_alert_dialog.dart';

class LocationPermissionDialog extends StatelessWidget {
  const LocationPermissionDialog({
    Key? key,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return ODAlertDialog(
      child: Text(
        tr('location_permission_prompt'),
        style: Theme.of(context).textTheme.labelLarge,
      ),
      actions: [
        TextButton(
          onPressed: () {
            Navigator.pop(context, true);
          },
          child: Text(tr('ok')),
          style: ButtonStyle(
            foregroundColor: MaterialStateProperty.all(Colors.black),
          ),
        ),
        TextButton(
          onPressed: () {
            Navigator.pop(context);
          },
          child: Text(tr('cancel')),
          style: ButtonStyle(
            foregroundColor: MaterialStateProperty.all(Colors.black),
          ),
        ),
      ],
    );
  }
}
