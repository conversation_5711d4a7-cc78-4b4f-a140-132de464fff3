import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';

import '../models/searchable_item_model.dart';

class SearchableItemsListBottomSheet extends StatefulWidget {
  const SearchableItemsListBottomSheet({
    Key? key,
    required this.hintText,
    required this.filterList,
    this.loading = false,
  }) : super(key: key);

  final bool loading;
  final String? hintText;
  final Future<List<SearchableItemModel>?> Function(String) filterList;

  @override
  State<SearchableItemsListBottomSheet> createState() => _SearchableItemsListBottomSheetState();
}

class _SearchableItemsListBottomSheetState extends State<SearchableItemsListBottomSheet> {
  final _scrollController = ScrollController();
  final _loading = <bool>[];
  var _searchQ = '';
  List<SearchableItemModel>? _results;

  @override
  void initState() {
    super.initState();

    _scrollController.addListener(() {
      if (FocusScope.of(context).hasFocus) {
        FocusScope.of(context).unfocus();
      }
    });
    Future.delayed(Duration.zero, () {
      _onSearchFieldChanged('');
    });
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  void _onEditingCompleted() async {
    _loading.add(true);
    if (_loading.length == 1) {
      setState(() {});
    }
    _results = await widget.filterList(_searchQ.trim());
    _loading.removeLast();
    if (!mounted) return;
    if (_loading.isEmpty) {
      setState(() {});
    }
    if (_results?.isEmpty ?? true) {
      FocusScope.of(context).unfocus();
      return;
    }
    _onItemSelected(_results!.first);
  }

  void _onItemSelected(SearchableItemModel item) {
    Navigator.pop(context, item);
  }

  void _onSearchFieldChanged(String value) async {
    _searchQ = value;
    _loading.add(true);
    if (_loading.length == 1) {
      setState(() {});
    }
    _results = await widget.filterList(_searchQ.trim());
    _loading.removeLast();
    if (!mounted) return;
    if (_loading.isEmpty) {
      setState(() {});
    }
  }

  @override
  Widget build(BuildContext context) {
    Widget child;
    if (widget.loading || _loading.isNotEmpty) {
      child = const Align(
        alignment: Alignment.topCenter,
        child: Padding(
          padding: EdgeInsets.only(top: 30.0),
          child: CircularProgressIndicator(),
        ),
      );
    } else if (_results == null) {
      return Center(child: Text(tr('error')));
    } else {
      final listItems = _results!.map<Widget>((item) {
        return TextButton(
          onPressed: () => _onItemSelected(item),
          style: ButtonStyle(
            backgroundColor: MaterialStateProperty.all(Colors.transparent),
            foregroundColor: MaterialStateProperty.all(Theme.of(context).primaryColor),
          ),
          child: Text(item.getName(context)),
        );
      }).toList();
      child = ListView.separated(
        controller: _scrollController,
        padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 20),
        itemBuilder: (cxt, index) => listItems[index],
        separatorBuilder: (cxt, index) => const SizedBox(height: 20),
        itemCount: listItems.length,
      );
    }
    final inputBorder = OutlineInputBorder(
      borderRadius: BorderRadius.circular(10),
      borderSide: const BorderSide(color: Colors.grey),
    );
    return SafeArea(
      child: Column(
        children: [
          const SizedBox(height: 40),
          Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              IconButton(
                onPressed: () => Navigator.pop(context),
                icon: const Icon(Icons.close),
                color: Theme.of(context).textTheme.bodyLarge?.color,
              ),
            ],
          ),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 20),
            child: TextFormField(
              autofocus: true,
              onChanged: (value) {
                _onSearchFieldChanged(value);
                if (value.trim().isEmpty) {
                  Future.delayed(const Duration(seconds: 1), () {
                    _onSearchFieldChanged('');
                  });
                }
              },
              onEditingComplete: _onEditingCompleted,
              decoration: InputDecoration(
                hintText: widget.hintText,
                hintStyle: const TextStyle(color: Colors.grey),
                border: inputBorder,
                enabledBorder: inputBorder,
                focusedBorder: inputBorder.copyWith(
                  borderSide: BorderSide(color: Theme.of(context).primaryColor),
                ),
              ),
            ),
          ),
          Expanded(child: child),
        ],
      ),
    );
  }
}
