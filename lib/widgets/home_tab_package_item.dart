import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:one_drop/styles.dart';

import '../models/package_item_model.dart';
import '../screens/monthly_packages_screen.dart';

class HomeTabPackageItem extends StatelessWidget {
  const HomeTabPackageItem({
    required this.package,
    Key? key,
  }) : super(key: key);

  final PackageItemModel package;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () {
        Navigator.push(
          context,
          CupertinoPageRoute(
            builder: (cxt) => const MonthlyPackagesScreen(package: null), // package
            settings: const RouteSettings(name: MonthlyPackagesScreen.routeName),
          ),
        );
      },
      child: Container(
        decoration: BoxDecoration(
          color: Styles.lightGrey,
          borderRadius: BorderRadius.circular(10),
        ),
        padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 20),
        child: Row(
          children: [
            SizedBox(
              width: 40,
              height: 40,
              child: kReleaseMode
                  ? CachedNetworkImage(
                      imageUrl: package.image ?? '',
                    )
                  : SvgPicture.asset('assets/images/5.svg'),
              // child: SvgPicture.asset('assets/images/7.svg'),
            ),
            const SizedBox(width: 10),
            Text(package.name),
            const Spacer(),
            Container(
              padding: const EdgeInsets.all(7),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(5),
                boxShadow: [
                  BoxShadow(color: Colors.grey[300]!, blurRadius: 5),
                ],
              ),
              child: const Icon(Icons.arrow_forward),
            ),
          ],
        ),
      ),
    );
  }
}
