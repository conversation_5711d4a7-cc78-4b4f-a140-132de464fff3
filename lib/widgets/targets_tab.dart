import 'dart:async';

import 'package:bot_toast/bot_toast.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:fl_chart/fl_chart.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:one_drop/api/user_api.dart';
import 'package:one_drop/helpers.dart';
import 'package:provider/provider.dart';

import '../constants.dart';
import '../models/target_item_model.dart';
import '../providers/create_order_provider.dart';
import '../screens/create_order_select_location_map_screen.dart';
import '../services/orders_service.dart';
import '../services/service_locator.dart';
import '../styles.dart';

class TargetsTab extends StatefulWidget {
  const TargetsTab({Key? key}) : super(key: key);

  @override
  State<TargetsTab> createState() => _TargetsTabState();
}

class _TargetsTabState extends State<TargetsTab> {
  var _loading = true;
  List<TargetItemModel>? _targets;
  StreamSubscription<int?>? _orderCreatedSubscription;

  @override
  void initState() {
    super.initState();

    _initState();
    _orderCreatedSubscription = sl.get<OrderService>().onOrderCreated.listen((event) {
      if (event == null) return;
      _initState();
    });
  }

  @override
  void dispose() {
    _orderCreatedSubscription?.cancel();
    super.dispose();
  }

  void _initState() async {
    if (!_loading) {
      setState(() {
        _loading = true;
      });
    }
    _targets = await UserAPI().getTargets();
    _loading = false;
    if (!mounted) return;
    setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    const xPadding = EdgeInsets.symmetric(horizontal: 20, vertical: 10);
    List<Widget> listItems;
    if (_loading) {
      listItems = const [
        Padding(
          padding: EdgeInsets.only(top: 20),
          child: Center(child: CircularProgressIndicator()),
        )
      ];
    } else if (_targets == null) {
      listItems = [Center(child: Text(
        tr('error'),
        style: Theme.of(context).textTheme.bodyLarge?.copyWith(
          color: Styles.errorColor,
        ),
      ))];
    } else if (_targets!.isEmpty) {
      listItems = [Center(child: Text(
        tr('no_targets_found'),
        style: Theme.of(context).textTheme.bodyLarge?.copyWith(
          color: Styles.textSecondaryColor,
        ),
      ))];
    } else {
      listItems = listItems = <Widget>[
        for (final target in _targets!)
          Padding(
            padding: xPadding,
            child: TargetListItem(
              target: target,
            ),
          ),
        // const Padding(
        //   padding: xPadding,
        //   child: TargetListItem(svgImage: 'assets/images/6.svg'),
        // ),
        const SizedBox(height: 20),
      ];
    }
    return SafeArea(
      child: CustomScrollView(
        slivers: [
          SliverAppBar(
            pinned: true,
            floating: true,
            elevation: 5,
            shadowColor: Colors.grey[100],
            backgroundColor: Colors.white,
            title: Text(
              tr('the_target'),
              style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                color: Styles.textPrimaryColor,
                fontWeight: FontWeight.bold,
              ),
            ),
            centerTitle: false,
            automaticallyImplyLeading: false,
          ),
          SliverList(
            delegate: SliverChildBuilderDelegate(
              (cxt, index) {
                return listItems[index];
              },
              childCount: listItems.length,
            ),
          ),
        ],
      ),
    );
  }
}

class TargetListItem extends StatelessWidget {
  const TargetListItem({
    Key? key,
    // required this.svgImage,
    required this.target,
  }) : super(key: key);

  final TargetItemModel target;
  // final String svgImage;

  void _orderPressed(BuildContext context) async {
    final createOrderProvider = context.read<CreateOrderProvider>();
    createOrderProvider.clearAll();
    createOrderProvider.hasFreeWash = target.enableFreeOrder;
    BotToast.showLoading();
    final homeModel = await UserAPI().getHomeContent();
    BotToast.closeAllLoading();
    if (homeModel == null || homeModel.categories.isEmpty) {
      return Helpers.showErrorNotification();
    }
    createOrderProvider.selectedOrderCategory = homeModel.categories.first;
    Navigator.pushNamed(context, CreateOrderSelectLocationMapScreen.routeName);
  }

  @override
  Widget build(BuildContext context) {
    return AspectRatio(
      aspectRatio: 3 / 3.5,
      child: Container(
        width: double.infinity,
        decoration: BoxDecoration(
          color: Colors.white,
          boxShadow: [kBoxShadow],
          borderRadius: BorderRadius.circular(10),
        ),
        padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 10),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Spacer(),
            Stack(
              children: [
                TargetPieChart(fillValue: target.totalCompletedOrders / target.washCount),
                Positioned.fill(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      SizedBox(
                        width: 30,
                        height: 30,
                        child: kReleaseMode
                            ? CachedNetworkImage(
                                imageUrl: target.image ?? '',
                                fit: BoxFit.cover,
                                errorWidget: (_, __, ___) => kImagePlaceholder,
                                placeholder: (_, __) => kImagePlaceholder,
                              )
                            : SvgPicture.asset('assets/images/6.svg'),
                        // child: SvgPicture.asset(svgImage),
                      ),
                      const SizedBox(height: 10),
                      Text(
                        '${target.totalCompletedOrders} / ${target.washCount}',
                        style: Theme.of(context).textTheme.titleLarge?.copyWith(
                          color: Styles.textPrimaryColor,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      const SizedBox(height: 5),
                      Text(
                        tr('completed'),
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: Styles.textSecondaryColor,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 10),
            Text(
              tr('free_wash'),
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                color: Styles.textPrimaryColor,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 10),
            if (target.enableFreeOrder)
              Text(
                tr('you_now_have_free_wash'),
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Styles.textSecondaryColor,
                ),
                textAlign: TextAlign.center,
              )
            else
              Text(
                tr('complete_5_washes_of_same_type'),
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Styles.textSecondaryColor,
                ),
                textAlign: TextAlign.center,
              ),
            const Spacer(),
            SizedBox(
              width: 200,
              child: ElevatedButton(
                onPressed: () => _orderPressed(context),
                child: Text(
                  target.enableFreeOrder ? tr('get_free_wash_now') : tr('order_new_wash'),
                  style: Theme.of(context).textTheme.labelLarge?.copyWith(
                    color: Colors.white,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                style: ButtonStyle(
                  backgroundColor: MaterialStateProperty.all(Styles.secondaryColor),
                  shape: MaterialStateProperty.all(
                    RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
                  ),
                ),
              ),
            ),
            const SizedBox(height: 10),
          ],
        ),
      ),
    );
  }
}

class TargetPieChart extends StatelessWidget {
  const TargetPieChart({
    required this.fillValue,
    Key? key,
  }) : super(key: key);

  final double fillValue;

  @override
  Widget build(BuildContext context) {
    const pieSize = 7.0;
    return SizedBox(
      height: 140,
      child: PieChart(
        PieChartData(
          startDegreeOffset: 270,
          centerSpaceRadius: 60,
          sectionsSpace: 0,
          sections: [
            PieChartSectionData(
              radius: pieSize,
              color: Styles.primaryColor,
              value: fillValue,
              showTitle: false,
            ),
            PieChartSectionData(
              radius: pieSize,
              color: Colors.grey[200],
              value: 1 - fillValue,
              showTitle: false,
            ),
          ],
        ),
        swapAnimationDuration: const Duration(milliseconds: 150),
        swapAnimationCurve: Curves.linear,
      ),
    );
  }
}
