import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';

import '../screens/add_payment_card_screen.dart';
import '../styles.dart';

class CardPaymentSavedCardsList extends StatelessWidget {
  const CardPaymentSavedCardsList({
    Key? key,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 10.0),
          child: Row(
            children: [
              Text(
                tr('select_card'),
                style: const TextStyle(color: Colors.grey),
              ),
            ],
          ),
        ),
        const SizedBox(height: 10),
        const PaymentMethodSavedCardItem(selected: true),
        const SizedBox(height: 5),
        const PaymentMethodSavedCardItem(selected: false),
        const SizedBox(height: 10),
        TextButton.icon(
          onPressed: () {
            Navigator.pushNamed(context, AddPaymentCardScreen.routeName);
          },
          icon: const Icon(Icons.add_circle_outline),
          label: Text(
            tr('add_new_card'),
            style: const TextStyle(color: Styles.secondaryColor),
          ),
        ),
      ],
    );
  }
}

class PaymentMethodSavedCardItem extends StatelessWidget {
  const PaymentMethodSavedCardItem({
    Key? key,
    this.selected = false,
  }) : super(key: key);

  final bool selected;

  @override
  Widget build(BuildContext context) {
    return Container(
      color: Colors.grey[100],
      padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 10),
      child: Row(
        children: [
          Container(
            width: 20,
            height: 20,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: selected ? Styles.primaryColor : Colors.white,
            ),
            alignment: Alignment.center,
            child: selected
                ? Container(
                    width: 8,
                    height: 8,
                    decoration: const BoxDecoration(
                      shape: BoxShape.circle,
                      color: Colors.white,
                    ),
                  )
                : null,
          ),
          const SizedBox(width: 10),
          const Icon(Icons.image),
          const SizedBox(width: 10),
          const Text('Bank Name'),
          const SizedBox(width: 10),
          const Text('**** 1234'),
        ],
      ),
    );
  }
}
