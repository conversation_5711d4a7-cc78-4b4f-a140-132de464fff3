import 'package:flutter/material.dart';

import '../styles.dart';

class SavedAddressListItem extends StatelessWidget {
  const SavedAddressListItem({
    Key? key,
    required this.onSelected,
    required this.name,
    required this.address,
    this.onSavePressed,
    this.isSaved = true,
  }) : super(key: key);

  final bool isSaved;
  final String name;
  final String address;
  final void Function() onSelected;
  final void Function()? onSavePressed;

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        InkWell(
          onTap: onSelected,
          child: const Icon(Icons.location_on),
        ),
        const SizedBox(width: 10),
        Expanded(
          child: InkWell(
            onTap: onSelected,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  name,
                  style: Theme.of(context).textTheme.labelLarge,
                ),
                const SizedBox(height: 5),
                Text(address),
              ],
            ),
          ),
        ),
        IconButton(
          icon: isSaved ? const Icon(Icons.bookmark, color: Styles.primaryColor) : const Icon(Icons.bookmark_outline),
          color: isSaved ? Styles.primaryColor : Colors.black,
          onPressed: onSavePressed,
        ),
      ],
    );
  }
}
