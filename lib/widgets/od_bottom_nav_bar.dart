import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:provider/provider.dart';
import 'package:easy_localization/easy_localization.dart';

import '../providers/user_provider.dart';
import '../screens/sign_up_phone_number_screen.dart';
import '../styles.dart';

class ODBottomNavBar extends StatefulWidget {
  const ODBottomNavBar({Key? key}) : super(key: key);

  @override
  State<ODBottomNavBar> createState() => _ODBottomNavBarState();
}

class _ODBottomNavBarState extends State<ODBottomNavBar> {
  int _currentIndex = 0;

  final List<BottomNavItem> _navItems = [
    BottomNavItem(
      svgPath: 'assets/images/8.svg',
      // label: 'navigation_home',
      icon: Icons.home_outlined,
    ),
    BottomNavItem(
      svgPath: 'assets/images/9.svg',
      // label: 'navigation_orders',
      icon: Icons.list_alt_outlined,
    ),
    BottomNavItem(
      svgPath: null,
      label: 'navigation_shop',
      icon: Icons.shopping_cart_outlined,
    ),
    BottomNavItem(
      svgPath: 'assets/images/10.svg',
      // label: 'navigation_targets',
      icon: Icons.track_changes_outlined,
    ),
    BottomNavItem(
      svgPath: 'assets/images/11.svg',
      // label: 'navigation_account',
      icon: Icons.person_outline,
    ),
  ];

  void _onTabTapped(int index) {
    final userProvider = context.read<UserProvider>();
    final isAuthenticated = userProvider.userModel?.token != null;

    if (!isAuthenticated && index != 0) {
      Navigator.pushNamed(context, SignUpPhoneNumberScreen.routeName);
    } else {
      final tabController = DefaultTabController.of(context);
      tabController.animateTo(index);
    }
  }

  @override
  Widget build(BuildContext context) {
    final bottomPadding = MediaQuery.of(context).viewPadding.bottom;

    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(20),
          topRight: Radius.circular(20),
        ),
        boxShadow: [kBoxShadow],
      ),
      height: 70 + bottomPadding,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceAround,
        children: _navItems.asMap().entries.map((entry) {
          final index = entry.key;
          final item = entry.value;

          return _BottomNavBarItemWidget(
            item: item,
            isSelected: _currentIndex == index,
            onTap: () {
              setState(() => _currentIndex = index);
              _onTabTapped(index);
            },
          );
        }).toList(),
      ),
    );
  }
}

class BottomNavItem {
  final String? svgPath;
  final String? label;
  final IconData icon;

  const BottomNavItem({
    required this.svgPath,
    this.label,
    required this.icon,
  });
}

class _BottomNavBarItemWidget extends StatelessWidget {
  final BottomNavItem item;
  final bool isSelected;
  final VoidCallback onTap;

  const _BottomNavBarItemWidget({
    required this.item,
    required this.isSelected,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return Expanded(
      child: GestureDetector(
        onTap: onTap,
        child: AnimatedContainer(
          duration: const Duration(milliseconds: 250),
          curve: Curves.easeInOut,
          padding: const EdgeInsets.symmetric(vertical: 8),
          decoration: BoxDecoration(
            color: isSelected
                ? Styles.primaryColor.withOpacity(0.1)
                : Colors.transparent,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              _buildIcon(),
              if (item.label != null) const SizedBox(height: 4),
              if (item.label != null) _buildLabel(context),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildIcon() {
    final color = isSelected ? Styles.primaryColor : Colors.grey;
    return item.svgPath != null
        ? SvgPicture.asset(
            item.svgPath!,
            color: color,
            width: 40,
            height: 40,
          )
        : Icon(
            item.icon,
            color: color,
            size: 24,
          );
  }

  Widget _buildLabel(BuildContext context) {
    return Text(
      item.label!.tr(),
      style: Theme.of(context).textTheme.bodySmall?.copyWith(
            color: isSelected ? Styles.primaryColor : Colors.grey,
            fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
          ),
    );
  }
}
