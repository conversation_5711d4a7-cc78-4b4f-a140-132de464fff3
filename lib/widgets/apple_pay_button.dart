import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class ApplePayButton extends StatefulWidget {
  final Function(String) onPaymentSuccess;
  final Function(String) onPaymentError;
  final String invoiceId;
  final double amount;

  const ApplePayButton({
    Key? key,
    required this.onPaymentSuccess,
    required this.onPaymentError,
    required this.invoiceId,
    required this.amount,
  }) : super(key: key);

  @override
  State<ApplePayButton> createState() => _ApplePayButtonState();
}

class _ApplePayButtonState extends State<ApplePayButton> {
  static const platform = MethodChannel('one_drop/apple_pay');

  @override
  void initState() {
    super.initState();
    _setupApplePay();
  }

  Future<void> _setupApplePay() async {
    try {
      await platform.invokeMethod('setupApplePay', {
        'invoiceId': widget.invoiceId,
        'amount': widget.amount,
      });
    } catch (e) {
      widget.onPaymentError('Failed to setup Apple Pay');
    }
  }

  Future<void> _startPayment() async {
    try {
      final result = await platform.invokeMethod('startPayment');
      if (result == true) {
        widget.onPaymentSuccess(widget.invoiceId);
      } else {
        widget.onPaymentError('Payment failed');
      }
    } catch (e) {
      widget.onPaymentError('Payment failed');
    }
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: _startPayment,
      child: Container(
        height: 48,
        decoration: BoxDecoration(
          color: Colors.black,
          borderRadius: BorderRadius.circular(8),
        ),
        child: Center(
          child: Image.asset(
            'assets/images/apple_pay_button.png',
            height: 32,
          ),
        ),
      ),
    );
  }
}
