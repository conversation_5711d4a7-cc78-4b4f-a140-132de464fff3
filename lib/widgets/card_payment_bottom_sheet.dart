import 'package:bot_toast/bot_toast.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:one_drop/api/my_fatoorah_api.dart';
import 'package:one_drop/helpers.dart';
import 'package:one_drop/providers/user_provider.dart';
import 'package:provider/provider.dart';

import '../models/my_fatoorah_init_payment_response_model.dart';

class CardPaymentBottomSheet extends StatefulWidget {
  const CardPaymentBottomSheet({
    Key? key,
    required this.paymentAmount,
    this.currencyCode = 'SAR',
  }) : super(key: key);

  final double paymentAmount;
  final String currencyCode;

  @override
  State<CardPaymentBottomSheet> createState() => _CardPaymentBottomSheetState();
}

class _CardPaymentBottomSheetState extends State<CardPaymentBottomSheet> {
  final _formKey = GlobalKey<FormState>();
  final _cardNumberFieldController = TextEditingController();
  final _cvvFieldController = TextEditingController();
  String? _selectedExpiryMonth;
  String? _selectedExpiryYear;
  MyFatoorahInitPaymentResponseModel? _initPaymentResponse;

  @override
  void initState() {
    super.initState();

    _initPayment();
  }

  @override
  void dispose() {
    _cardNumberFieldController.dispose();
    _cvvFieldController.dispose();
    super.dispose();
  }

  void _initPayment() async {
    BotToast.showLoading();
    _initPaymentResponse = await MyFatoorahAPI().initPayment(
      paymentAmount: widget.paymentAmount,
      currencyCode: widget.currencyCode,
    );
    BotToast.closeAllLoading();
    if (_initPaymentResponse == null ||
        !(_initPaymentResponse?.isSuccess ?? false)) {
      Helpers.showErrorNotification();
      Navigator.pop(context);
    }
  }

  void _onPayPressed() async {
    FocusScope.of(context).unfocus();
    final validForm = _formKey.currentState?.validate() ?? false;
    if (!validForm) return;
    BotToast.showLoading();
    final user = context.read<UserProvider>().userModel;
    final myFatoorahAPI = MyFatoorahAPI();
    final currentLocale = EasyLocalization.of(context)?.locale.languageCode;
    try {
      final executePaymentResponse = await myFatoorahAPI.executePayment(
        customerEmail: user?.email,
        customerName: user?.name,
        paymentAmount: widget.paymentAmount,
        languageCode: currentLocale,
        currencyCode: widget.currencyCode,
        paymentMethodID:
            _initPaymentResponse!.getMasterVisaPaymentMethod!.paymentMethodID,
      );

      if (executePaymentResponse == null) {
        Helpers.showErrorNotification(tr('payment_failed'));
        return;
      }

      if (executePaymentResponse.isSuccess) {
        final transactionID = await myFatoorahAPI.completeDirectPayment(
          paymentLink: executePaymentResponse.data.paymentUrl,
          cardNumber: _cardNumberFieldController.text.trim(),
          cardExpiryDateMonth: _selectedExpiryMonth!,
          cardExpiryDateYear: _selectedExpiryYear!,
          cardSecurityCode: _cvvFieldController.text.trim(),
        );
        if (transactionID == null) {
          Helpers.showErrorNotification(tr('payment_failed'));
        } else {
          Navigator.pop(context, transactionID);
        }
      } else {
        Helpers.showErrorNotification(tr('payment_failed'));
      }
    } catch (e) {
      Helpers.showErrorNotification(tr('payment_failed'));
    } finally {
      BotToast.closeAllLoading();
    }
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => FocusScope.of(context).unfocus(),
      child: SafeArea(
        child: Column(
          children: [
            Padding(
              padding: const EdgeInsets.only(left: 20, right: 20, top: 60),
              child: Row(
                children: [
                  Text(tr('pay_using_card')),
                  const Spacer(),
                  IconButton(
                    onPressed: () => Navigator.pop(context),
                    icon: const Icon(Icons.close),
                  ),
                ],
              ),
            ),
            Expanded(
              child: SingleChildScrollView(
                padding: const EdgeInsets.only(left: 20, right: 20, bottom: 20),
                child: Form(
                  key: _formKey,
                  child: Column(
                    children: [
                      TextFormField(
                        controller: _cardNumberFieldController,
                        autofocus: true,
                        decoration: InputDecoration(
                          labelText: tr('card_number'),
                        ),
                        keyboardType: TextInputType.number,
                        validator: (value) {
                          if (value?.trim().isEmpty ?? true) {
                            return tr('please_enter_card_number');
                          } else if ((value?.trim().length ?? 0) < 14) {
                            return tr('please_enter_valid_card_number');
                          }
                          return null;
                        },
                      ),
                      const SizedBox(height: 20),
                      Row(
                        children: [
                          Text(tr('expiry_date')),
                        ],
                      ),
                      Row(
                        children: [
                          Expanded(
                            child: DropdownButtonFormField<String>(
                              validator: (value) {
                                if (value?.isEmpty ?? true) {
                                  return tr('please_select_expiry_month');
                                }
                                return null;
                              },
                              decoration: InputDecoration(
                                labelText: tr('month'),
                              ),
                              value: _selectedExpiryMonth,
                              onChanged: (value) {
                                setState(() {
                                  _selectedExpiryMonth = value;
                                });
                              },
                              items: List.generate(12, (index) {
                                index = index + 1;
                                return DropdownMenuItem<String>(
                                  child: Row(
                                    mainAxisAlignment: MainAxisAlignment.end,
                                    children: <Widget>[
                                      index > 9
                                          ? Text('$index')
                                          : Text('0$index'),
                                    ],
                                  ),
                                  value: index > 9 ? '$index' : '0$index',
                                );
                              }),
                            ),
                          ),
                          const SizedBox(width: 20),
                          Expanded(
                            child: DropdownButtonFormField<String>(
                              validator: (value) {
                                if (value?.isEmpty ?? true) {
                                  return tr('please_select_expiry_year');
                                }
                                return null;
                              },
                              decoration: InputDecoration(
                                labelText: tr('year'),
                              ),
                              value: _selectedExpiryYear,
                              onChanged: (value) {
                                setState(() {
                                  _selectedExpiryYear = value;
                                });
                              },
                              items: List.generate(20, (index) {
                                final year = DateTime.now().year + index;
                                final yearStr = year.toString().substring(2);
                                return DropdownMenuItem<String>(
                                  child: Row(
                                    mainAxisAlignment: MainAxisAlignment.end,
                                    children: <Widget>[
                                      Text(yearStr),
                                    ],
                                  ),
                                  value: yearStr,
                                );
                              }),
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 20),
                      TextFormField(
                        controller: _cvvFieldController,
                        decoration: const InputDecoration(
                          labelText: 'CVV',
                        ),
                        maxLength: 3,
                        keyboardType: TextInputType.number,
                        validator: (value) {
                          if ((value?.trim().length ?? 0) < 3) {
                            return tr('please_enter_cvv');
                          }
                          return null;
                        },
                      ),
                      const SizedBox(height: 20),
                      SizedBox(
                        width: 300,
                        child: ElevatedButton(
                          onPressed: _onPayPressed,
                          child: Text(
                              '${tr('pay')} ${widget.paymentAmount.toInt()} ${tr(widget.currencyCode)}'),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
