import 'package:flutter/material.dart';

class ODAlertDialog extends StatelessWidget {
  const ODAlertDialog({
    Key? key,
    required this.child,
    this.actions = const [],
  }) : super(key: key);

  final Widget child;
  final List<Widget> actions;

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(20),
      ),
      child: SingleChildScrollView(
        child: Column(
          children: [
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 20),
              child: child,
            ),
            Container(
              decoration: BoxDecoration(
                border: actions.isNotEmpty ? Border(top: BorderSide(color: Colors.grey[350]!, width: 0.5)) : null,
              ),
              child: Row(
                children: List<Widget>.generate(
                  actions.length,
                  (index) {
                    final widget = actions[index];
                    if (index == actions.length - 1) {
                      return Expanded(child: widget);
                    }
                    return Expanded(
                      child: Container(
                        decoration: BoxDecoration(
                          border: Border(
                            left: BorderSide(color: Colors.grey[350]!, width: 0.5),
                          ),
                        ),
                        child: widget,
                      ),
                    );
                  },
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
