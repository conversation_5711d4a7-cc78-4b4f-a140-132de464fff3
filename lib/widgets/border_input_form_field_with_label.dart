import 'package:flutter/material.dart';

import '../styles.dart';

class BorderInputFormFieldWithLabel extends StatelessWidget {
  const BorderInputFormFieldWithLabel({
    required this.label,
    this.hint,
    this.prefix,
    this.controller,
    this.validator,
    this.keyboardType,
    this.maxLength,
    Key? key,
  }) : super(key: key);

  final int? maxLength;
  final String label;
  final String? hint;
  final Widget? prefix;
  final TextInputType? keyboardType;
  final TextEditingController? controller;
  final String? Function(String?)? validator;

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Row(
          children: [
            Text(label),
          ],
        ),
        const SizedBox(height: 10),
        TextFormField(
          controller: controller,
          validator: validator,
          keyboardType: keyboardType,
          maxLength: maxLength,
          decoration: InputDecoration(
            counterText: '',
            hintText: hint,
            hintStyle: TextStyle(
              color: Colors.grey[350]!,
              fontSize: 14,
            ),
            prefixIcon: prefix,
            prefixIconConstraints: const BoxConstraints(maxWidth: 80),
            border: kInputOutlineBorder,
            enabledBorder: kInputOutlineBorder,
            focusedBorder: kInputOutlineFocusedBorder,
          ),
        ),
      ],
    );
  }
}
