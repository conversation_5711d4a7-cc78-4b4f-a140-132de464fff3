import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:one_drop/services/prefs_service.dart';
import 'package:one_drop/services/service_locator.dart';
import 'package:one_drop/widgets/delete_payment_card_dialog.dart';

import '../models/payment_card_model.dart';
import '../screens/add_payment_card_screen.dart';
import '../styles.dart';
import 'wallet_saved_card_item.dart';

class UserSavedCards extends StatefulWidget {
  const UserSavedCards({Key? key}) : super(key: key);

  @override
  State<UserSavedCards> createState() => _UserSavedCardsState();
}

class _UserSavedCardsState extends State<UserSavedCards> {
  var _editMode = false;
  List<PaymentCardModel>? _savedCards;

  @override
  void initState() {
    super.initState();

    _initState();
  }

  void _initState() async {
    _savedCards = await sl.get<PrefsService>().getSavedPaymentCards();
    setState(() {});
  }

  void _onAddCardPressed() async {
    final PaymentCardModel? addedCard = await Navigator.push<dynamic>(
      context,
      CupertinoPageRoute(
        builder: (cxt) => const AddPaymentCardScreen(showRememberCardButton: false),
        settings: const RouteSettings(name: AddPaymentCardScreen.routeName),
      ),
    );
    if (addedCard == null) return;
    _savedCards?.add(addedCard);
    setState(() {});
  }

  void _onDeleteCardPressed(PaymentCardModel card) async {
    final deleteCard = await showDialog<bool>(
      context: context,
      builder: (cxt) => const DeletePaymentCardDialog(),
    );
    final doDeleteCard = deleteCard ?? false;
    if (doDeleteCard) {
      _savedCards?.removeWhere((element) => element.cardNumber == card.cardNumber);
      setState(() {});
      final success = await sl.get<PrefsService>().deleteCard(card);
      if (!success) {
        _savedCards?.add(card);
        setState(() {});
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [kBoxShadow],
        borderRadius: BorderRadius.circular(10),
      ),
      padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 10),
      margin: const EdgeInsets.symmetric(horizontal: 20),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                tr('saved_cards'),
                style: Theme.of(context).textTheme.labelLarge?.copyWith(color: Colors.grey),
              ),
              if (_savedCards?.isNotEmpty ?? false)
                TextButton(
                  onPressed: () {
                    setState(() {
                      _editMode = !_editMode;
                    });
                  },
                  child: Text(_editMode ? tr('done') : tr('edit')),
                ),
            ],
          ),
          for (final card in (_savedCards ?? <PaymentCardModel>[]))
            WalletSavedCardItem(
              key: ValueKey(card.cardNumber),
              image: card.imagePath == null
                  ? null
                  : Image.asset(
                      card.imagePath ?? '',
                      width: 20,
                      height: 20,
                    ),
              bankName: card.concealedCardNumber,
              numberText: null,
              onDeletePressed: _editMode ? () => _onDeleteCardPressed(card) : null,
            ),
          const Divider(),
          TextButton(
            onPressed: _onAddCardPressed,
            child: Row(
              children: [
                const Icon(Icons.add_circle_outline),
                const SizedBox(width: 10),
                Text(
                  tr('add_new_card'),
                  style: const TextStyle(color: Styles.secondaryColor),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
