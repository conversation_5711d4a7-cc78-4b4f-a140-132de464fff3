import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:one_drop/styles.dart';

class OrderCouponField extends StatefulWidget {
  const OrderCouponField({
    Key? key,
    required this.onApplyPressed,
  }) : super(key: key);

  final void Function(String) onApplyPressed;

  @override
  State<OrderCouponField> createState() => _OrderCouponFieldState();
}

class _OrderCouponFieldState extends State<OrderCouponField> {
  var _value = '';

  void _onApplyPressed() {
    FocusScope.of(context).unfocus();
    widget.onApplyPressed(_value);
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey[300]!, width: 1),
        borderRadius: BorderRadius.circular(10),
      ),
      child: Row(
        children: [
          Expanded(
            child: TextField(
              onChanged: (value) {
                _value = value;
                setState(() {});
              },
              onEditingComplete: () {
                if (_value.trim().length > 3) {
                  _onApplyPressed();
                }
              },
              decoration: InputDecoration(
                contentPadding: const EdgeInsets.symmetric(horizontal: 20),
                hintText: tr('enter_coupon_code_here'),
                hintStyle: const TextStyle(color: Colors.grey, fontSize: 14),
                border: InputBorder.none,
                errorBorder: InputBorder.none,
                enabledBorder: InputBorder.none,
                focusedBorder: InputBorder.none,
                disabledBorder: InputBorder.none,
              ),
            ),
          ),
          Container(
            height: 30,
            width: 1,
            color: Colors.grey[300],
          ),
          TextButton(
            onPressed: _value.trim().length < 3 ? null : _onApplyPressed,
            child: Text(
              tr('apply'),
              style: const TextStyle(color: Styles.primaryColor),
            ),
          ),
        ],
      ),
    );
  }
}
