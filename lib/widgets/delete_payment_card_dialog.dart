import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';

import 'od_alert_dialog.dart';

class DeletePaymentCardDialog extends StatelessWidget {
  const DeletePaymentCardDialog({
    Key? key,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return ODAlertDialog(
      child: Text(
        tr('delete_card'),
        style: Theme.of(context).textTheme.labelLarge,
      ),
      actions: [
        TextButton(
          onPressed: () {
            Navigator.pop(context, true);
          },
          child: Text(tr('yes')),
          style: ButtonStyle(
            foregroundColor: MaterialStateProperty.all(Colors.black),
          ),
        ),
        TextButton(
          onPressed: () {
            Navigator.pop(context);
          },
          child: Text(tr('no')),
          style: ButtonStyle(
            foregroundColor: MaterialStateProperty.all(Colors.black),
          ),
        ),
      ],
    );
  }
}
