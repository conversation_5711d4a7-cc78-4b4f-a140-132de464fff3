import 'package:flutter/material.dart';

import 'screens/payment_method_screen.dart';

enum CarSize { small, medium, large }

const kCarSizeNameMap = <CarSize, String>{
  CarSize.large: 'large',
  CarSize.medium: 'medium',
  CarSize.small: 'small',
};

const kOrderStatusColor = <String, Color>{
  K.orderStatusPending: Colors.orange,
  K.orderStatusCancelled: Colors.red,
  K.orderStatusDone: Colors.green,
  K.orderStatusOnWay: Colors.blue,
  K.orderStatusPreparing: Colors.indigo,
};

const kOrderStatusProgress = <String, int>{
  K.orderStatusPending: 0,
  K.orderStatusPreparing: 1,
  K.orderStatusOnWay: 2,
  K.orderStatusDone: 3,
  K.orderStatusCancelled: 4,
};

final placeholderUserImage = Image.asset(
  'assets/images/logo.png',
  width: double.infinity,
  height: double.infinity,
  fit: BoxFit.cover,
);

const kImagePlaceholder = Icon(Icons.image, color: Colors.grey);

const kLocaleLangName = <String, String>{'ar': 'العربية', 'en': 'English'};

enum PaymentMethod { wallet, package, card, cash, applePay }

const kMapPaymentMethodEnumToString = <PaymentMethod, String>{
  PaymentMethod.wallet: K.paymentTypeWallet,
  PaymentMethod.package: K.paymentTypePackage,
  PaymentMethod.card: K.paymentTypeVisa,
  PaymentMethod.cash: K.paymentTypeCash,
  PaymentMethod.applePay: K.paymentTypeApplePay,
};

mixin K {
  static const notificationDuration = Duration(seconds: 3);
  static const serverURL = 'https://qatrawahda.sa';
  static const apiBaseURL = '$serverURL/api';
  static const userModelPrefsKey = 'user_model';
  static const otpCodeSentResponseMessage = 'auth.code_sent';
  static const otpCodeResentResponseMessage = 'auth.code_re_send';
  static const googleAPIKey = 'AIzaSyBypxT1CuIUy4sWyaqXCoa_vQMArvKTJGQ';
  static const userAddressMaxLength = 50;
  static const orderStatusCancelled = 'cancelled';
  static const orderStatusOnWay = 'delegate_accept';
  static const orderStatusPreparing = 'preparing';
  static const orderStatusDone = 'done';
  static const orderStatusPending = 'pending';
  static const androidPackageName = 'com.app.onedrop';
  static const iOSBundleID = 'com.onedrop.qatrahWahdah';
  static const iOSPaymentTestSecretKey = 'sk_test_ynhg4Cs25dwEaHIGmWLefAo9';
  static const androidPaymentTestSecretKey = 'sk_test_Ap0FP8xhoJfYS5jCk2sQm7r1';
  static const paymentApprovedResponseMessage = 'Approved';
  static const transferTypeWallet = 'wallet';
  static const appNameAr = 'قطرة واحدة';
  static const savedPaymentCardsKey = 'savedPaymentCards';
  static const paymentMethodApplePay = PaymentMethodModel(
    method: PaymentMethod.applePay,
    name: 'pay-using-apple-pay',
    svgPath: null,
  );
  static const paymentMethodWallet = PaymentMethodModel(
    method: PaymentMethod.wallet,
    name: 'pay-using-wallet',
    svgPath: 'assets/images/15.svg',
  );
  static const paymentMethodCard = PaymentMethodModel(
    method: PaymentMethod.card,
    name: 'pay-using-card',
    svgPath: 'assets/images/31.svg',
  );
  static const paymentMethodCash = PaymentMethodModel(
    method: PaymentMethod.cash,
    name: 'pay-cash',
    svgPath: 'assets/images/30.svg',
  );
  static const paymentMethodPackage = PaymentMethodModel(
    method: PaymentMethod.package,
    name: 'pay-using-package',
    svgPath: 'assets/images/7.svg',
  );
  static const myFatoorahLiveAPI = 'https://api-sa.myfatoorah.com/v2';
  static const myFatoorahTestAPI = 'https://apitest.myfatoorah.com/v2';
  static const myFatoorahAPI = K.myFatoorahLiveAPI;
  // static const myFatoorahAPI = K.myFatoorahTestAPI;
  static const paymentLiveMode = K.myFatoorahAPI == K.myFatoorahLiveAPI;
  // Production
  static const myFatoorahLiveToken =
      'tDmGI7Myq0a4tK5YTqVnm2MNnGxahneOVG4TzJy75hfPmwWPeSZcKOppgVeql3n96fQBXmj9fmx-d8Qc0SenfR81c8NIj1OgGMl-nYSXs-Uguxok_dzt9jj83npzSRJi3KTKyKsEHXbqvW0OHpy7n0mzaOykpTu4pXciNjWaQXj2ySLNqaXhsYIokbR3ODRFHKRT4GBk-fBllgwkTs8BwelgxaYpH4OBba6xhLaJM5RX5kUYapJjJMenKr4QpnimxAoD_1LlYeBooPx_5e2y1syEF4oLqvdctzHg6bF0I0o9PJu_5Md7Hw1gczgyAeYhJFRp6SKMBCtMIb1pRAAJqp7b_UMCvPSPHwuUm-i7sAib0z3e2kt7VSwLstRZNp0RI_NsrVU25rgsTxCZOS5qoqT3x7RRFFsyw9dmDHN-SX2BP-1lpJ1VQIgIP4_EIz93lNt-BpIWrbb0EjDiJh0i2TBObhl36EEnyXX6yL3hl5QYDO8YHhVorL_B44yp1Cwza2iUFELMEb_KEH7xXC6zf9tR2ieTil0AUxhAeb3FlB96Nr7Ki80_EwrfKY89Yo89ORq3Dyq1b2peCTRtWxQaB_6ShFrXsbl3za-A17V6u90SDa4dEh35YtriplsMbWqZP1CxvXLTGr43FmSF_xKPOJyrlp-wTdFS063tsdBwtOGdf6TMcoQaaapAvduvmm98eE3p5v7sVr_lkIVl2skDbFntCFydVXaQPjtpTZGCGNY6a-ED';
  // Test
  static const myFatoorahTestToken =
      'rLtt6JWvbUHDDhsZnfpAhpYk4dxYDQkbcPTyGaKp2TYqQgG7FGZ5Th_WD53Oq8Ebz6A53njUoo1w3pjU1D4vs_ZMqFiz_j0urb_BH9Oq9VZoKFoJEDAbRZepGcQanImyYrry7Kt6MnMdgfG5jn4HngWoRdKduNNyP4kzcp3mRv7x00ahkm9LAK7ZRieg7k1PDAnBIOG3EyVSJ5kK4WLMvYr7sCwHbHcu4A5WwelxYK0GMJy37bNAarSJDFQsJ2ZvJjvMDmfWwDVFEVe_5tOomfVNt6bOg9mexbGjMrnHBnKnZR1vQbBtQieDlQepzTZMuQrSuKn-t5XZM7V6fCW7oP-uXGX-sMOajeX65JOf6XVpk29DP6ro8WTAflCDANC193yof8-f5_EYY-3hXhJj7RBXmizDpneEQDSaSz5sFk0sV5qPcARJ9zGG73vuGFyenjPPmtDtXtpx35A-BVcOSBYVIWe9kndG3nclfefjKEuZ3m4jL9Gg1h2JBvmXSMYiZtp9MR5I6pvbvylU_PP5xJFSjVTIz7IQSjcVGO41npnwIxRXNRxFOdIUHn0tjQ-7LwvEcTXyPsHXcMD8WtgBh-wxR8aKX7WPSsT1O8d8reb2aR7K3rkV3K82K_0OgawImEpwSvp9MNKynEAJQS6ZHe_J_l77652xwPNxMRTMASk1ZsJL';
  static const myFatoorahToken = K.myFatoorahLiveToken;
  // static const myFatoorahToken = K.myFatoorahTestToken;
  // static const myFatooraVisaMasterDirectPayemntMethodName = 'Visa/Master Direct';
  static const myFatooraVisaMasterPayemntMethodName = 'VISA/MASTER';
  static const paymentTypePackage = 'package';
  static const paymentTypeTarget = 'target';
  static const paymentTypeApplePay = 'applepay';
  static const paymentTypeWallet = 'wallet';
  static const paymentTypeCash = 'cash';
  static const paymentTypeVisa = 'visa';
  static const paymentTypeMada = 'mada';
  static const paymentTypeMaster = 'mastercard';
  static const purchasedItemTypeOrder = 'Order';
  static const purchasedItemTypePackage = 'Package';
  static const purchasedItemTypeCharge = 'Charge';
  static const purchasedItemTypeSendGift = 'send_gift';
  static const purchasedItemTypeOrderThroughWallet = 'order_through_wallet';
  static const purchasedItemTypeOrderThroughPackage = 'order_through_package';
  static const purchaseStatusSuccess = 'successful';
  static const purchaseStatusFailed = 'failed';
  static const transactionAddBalanaceReason = 'اضافة رصيد';
  static const paymentCallbackURL = 'http://qatrawahda.sa';
  static const paymentStatusURLParamName = 'paymentSuccess';
}

const kMapPaymentTypeToForableType = <String, String>{
  K.paymentTypeWallet: K.purchasedItemTypeOrderThroughWallet,
  K.paymentTypePackage: K.purchasedItemTypeOrderThroughPackage,
};
