import 'package:dio/dio.dart';

import '../constants.dart';
import '../models/my_fatoorah_execute_payment_response_model.dart';
import 'package:one_drop/models/my_fatoorah_init_payment_response_model.dart';

class MyFatoorahAPI {
  final Dio _dio;

  MyFatoorahAPI()
      : _dio = Dio(BaseOptions(
          baseUrl: K.myFatoorahAPI,
          headers: <String, String>{
            'Accept': 'application/json',
            'Authorization': 'Bearer ${K.myFatoorahToken}',
          },
        ));

  Future<bool> completeDirectPayment({
    required String paymentLink,
    required String cardNumber,
    required String cardExpiryDateMonth,
    required String cardExpiryDateYear,
    required String cardSecurityCode,
  }) async {
    try {
      final res = await _dio.post<Map>(paymentLink, data: {
        'paymentType': 'card',
        'saveToken': false,
        'IsRecurring': false,
        'Bypass3DS': false,
        'card': {
          'Number': cardNumber,
          'expiryMonth': cardExpiryDateMonth,
          'expiryYear': cardExpiryDateYear,
          'securityCode': cardSecurityCode,
        },
      });
      return res.data?['Data']['PaymentId'] != null;
    } catch (e) {
      return false;
    }
  }

  Future<MyFatoorahExecutePaymentResponse?> executePayment({
    required int paymentMethodID,
    required double paymentAmount,
    required String? customerName,
    required String? customerEmail,
    String currencyCode = 'SAR',
    String? languageCode,
  }) async {
    try {
      final data = {
        'PaymentMethodId': paymentMethodID,
        'CustomerName': customerName,
        'DisplayCurrencyIso': currencyCode,
        'CustomerEmail': customerEmail,
        'InvoiceValue': paymentAmount.toInt(),
        'Language': languageCode,
        'CallBackUrl':
            '${K.paymentCallbackURL}?${K.paymentStatusURLParamName}=true',
        'ErrorUrl':
            '${K.paymentCallbackURL}?${K.paymentStatusURLParamName}=false',
      };
      final res = await _dio.post<Map>(
        '/ExecutePayment',
        data: data,
      );
      if (res.data == null) return null;
      return MyFatoorahExecutePaymentResponse.fromMap(
          <String, dynamic>{...res.data!});
    } catch (e) {
      return null;
    }
  }

  Future<MyFatoorahInitPaymentResponseModel?> initPayment({
    required double paymentAmount,
    String currencyCode = 'SAR',
  }) async {
    try {
      final res = await _dio.post<Map>(
        '/InitiatePayment',
        data: {
          'InvoiceAmount': paymentAmount.toInt(),
          'CurrencyIso': currencyCode
        },
      );
      if (res.data == null) return null;
      return MyFatoorahInitPaymentResponseModel.fromMap(
          <String, dynamic>{...res.data!});
    } catch (e) {
      return null;
    }
  }
}
