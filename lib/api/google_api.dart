import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:one_drop/services/network_service.dart';
import 'package:one_drop/services/service_locator.dart';

import '../constants.dart';
import '../models/google_address_model.dart';

class GoogleAPI {
  Future<List<GoogleAddressModel>?> geocode({
    String? address,
    double? lat,
    double? lng,
  }) async {
    try {
      final navigator = sl.get<GlobalKey<NavigatorState>>();
      final context = navigator.currentContext;
      
      // If we can't get context, try to use default language
      final currentLocale = context != null 
          ? EasyLocalization.of(context)?.currentLocale?.languageCode ?? 'ar'
          : 'ar';  // Default to Arabic since this is a Saudi Arabia app

      // Validate that either address or coordinates are provided
      if (address == null && (lat == null || lng == null)) {
        print('Neither address nor valid coordinates provided');
        return [];
      }

      final queryParams = {
        'key': K.googleAPIKey,
        if (lat != null && lng != null) 'latlng': '$lat,$lng',
        if (address != null) 'address': address,
        'language': currentLocale,
        'components': address != null ? 'country:sa' : null,
      }..removeWhere((key, value) => value == null);

      final res = await sl.get<NetworkService>().requestWithCache(path:
        'https://maps.googleapis.com/maps/api/geocode/json',  method:  'GET',
        queryParameters: queryParams
      );
      
      if (res == null || res['status'] != 'OK' || res['results'] == null) {
        print('Invalid response from Google API: ${res?['status']}');
        return [];
      }

      final results = (res['results'] as List).map<GoogleAddressModel>((e) {
        String name;
        final addressComponents = e['address_components'] as List?;
        if (addressComponents != null && 
            addressComponents.length >= 2 && 
            addressComponents[0]?['long_name'] != null && 
            addressComponents[1]?['long_name'] != null) {
          name = '${addressComponents[0]['long_name']} - ${addressComponents[1]['long_name']}';
        } else {
          final formattedAddress = (e['formatted_address'] as String?) ?? '';
          final parts = formattedAddress.split(' - ');
          name = '${parts.isNotEmpty ? parts[0] : ''} - ${parts.length > 1 ? parts[1] : ''}'.trim();
          if (name == ' - ') name = formattedAddress; // Fallback to full address if parts are empty
        }
        return GoogleAddressModel.fromJson(json: e, name: name);
      }).toList();

      return results;
    } catch (e) {
      print('--- getAddressFromText error');
      print(e.toString());
      return [];  // Return empty list instead of null for consistency
    }
  }
}
