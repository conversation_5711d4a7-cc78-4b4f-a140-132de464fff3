import 'dart:io';
import 'dart:math' as math;
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:bot_toast/bot_toast.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/material.dart';
import 'package:logging/logging.dart';
import 'package:one_drop/models/about_us_model.dart';
import 'package:one_drop/exceptions/api_exceptions.dart';
import 'package:one_drop/models/available_time_slot_model.dart';
import 'package:one_drop/providers/user_provider.dart';
import 'package:one_drop/utils/notification_utils.dart';
import 'package:one_drop/utils/platform_utils.dart';
import 'package:provider/provider.dart';
import 'package:path/path.dart' as path;

import 'package:dio/dio.dart';
import 'package:one_drop/models/city_model.dart';
import 'package:one_drop/models/coupon_model.dart';
import 'package:one_drop/models/home_tab_model.dart';
import 'package:one_drop/models/user_address_model.dart';
import 'package:one_drop/models/user_model.dart';
import 'package:one_drop/services/network_service.dart';

import '../constants.dart';
import '../models/car_type_model.dart';
import '../models/notification_model.dart';
import '../models/order_category_item_model.dart';
import '../models/order_model.dart';
import '../models/package_item_model.dart';
import '../models/packages_status_model.dart';
import '../models/payment_model.dart';
import '../models/price_model.dart';
import '../models/privacy_policy_model.dart';
import '../models/target_item_model.dart';
import '../models/terms_and_conditions_model.dart';
import '../models/wallet_model.dart';
import '../services/service_locator.dart';

class UserAPI {
  static final _logger = Logger('UserAPI');
  final _networkService = sl.get<NetworkService>();
  final Connectivity _connectivity = Connectivity();
  final _dio = Dio()
    ..options.baseUrl = "http://qatrawahda.sa/api"
    ..interceptors.add(LogInterceptor(
      requestBody: true,
      responseBody: true,
      requestHeader: true,
      responseHeader: true,
      error: true,
      logPrint: (obj) => debugPrint('UserAPI: ${obj.toString()}'),
    ));

  Future<bool> _checkInternetConnection() async {
    final connectivityResult = await _connectivity.checkConnectivity();
    final hasInternet = connectivityResult != ConnectivityResult.none;

    if (!hasInternet) {
      BotToast.showText(
        text: 'No internet connection',
        contentColor: Colors.red,
        duration: const Duration(seconds: 3),
      );
    }

    return hasInternet;
  }

  void _handleError(Object error, StackTrace stackTrace) {
    debugPrint('Error: $error');
    debugPrint('StackTrace: $stackTrace');

    if (error is DioException) {
      final message = error.response?.data?['message'] ??
          error.message ??
          tr('network_error');
      NotificationUtils.showError(message);
    } else {
      NotificationUtils.showError(error.toString());
    }
  }

  Future<List<PriceModel>?> getPrices({
    required int categoryID,
    int? carID,
  }) async {
    if (!await _checkInternetConnection()) return null;
    try {
      final res = await _networkService.request(
        method: 'GET',
        path: '/prices',
        queryParameters: {
          'category_id': categoryID,
          if (carID != null) 'car_id': carID,
        },
      );

      return ((res?['data'] as List?) ?? []).map<PriceModel>((e) {
        return PriceModel.fromMap(e);
      }).toList();
    } catch (e, stackTrace) {
      _handleError(e, stackTrace);
      return null;
    }
  }

  Future<bool> sendContactUsMessage(
      {required String name, required String message}) async {
    if (!await _checkInternetConnection()) return false;
    try {
      final res = await _networkService.request(
        method: 'POST',
        path: '/contact-us',
        data: {
          'name': name,
          'message': message,
        },
      );
      return res?['status'] == true;
    } catch (e, stackTrace) {
      _handleError(e, stackTrace);
      return false;
    }
  }

  Future<Map<String, String>?> getSocialLinks() async {
    if (!await _checkInternetConnection()) return null;
    try {
      final res =
          await _networkService.request(method: 'GET', path: '/social-links');
      return Map<String, String>.from(res?['data'] ?? {});
    } catch (e, stackTrace) {
      _handleError(e, stackTrace);
      return null;
    }
  }

  Future<bool> deleteAccount() async {
    if (!await _checkInternetConnection()) return false;
    try {
      final context = sl.get<GlobalKey<NavigatorState>>().currentContext;
      final userModel = context?.read<UserProvider>().userModel;
      final res = await _networkService.request(
          method: 'GET', path: '/delete-account/${userModel?.id}');
      return res?['status'];
    } catch (e, stackTrace) {
      _handleError(e, stackTrace);
      return false;
    }
  }

  Future<PrivacyPolicyModel?> getPrivacyPolicy() async {
    if (!await _checkInternetConnection()) return null;
    try {
      final res = await _networkService.request(method: 'GET', path: '/policy');
      final privacyPolicy = PrivacyPolicyModel.fromJson(res?['data']['policy']);
      return privacyPolicy;
    } catch (e, stackTrace) {
      _handleError(e, stackTrace);
      return null;
    }
  }

  Future<List<PackageItemModel>> getUserPackages() async {
    if (!await _checkInternetConnection()) return [];
    try {
      final res =
          await _networkService.request(method: 'GET', path: '/user-packages');
      final packages = ((res?['data']['packages'] as List?) ?? [])
          .map<PackageItemModel>((e) {
        return PackageItemModel.fromMap(e);
      }).toList();
      return packages;
    } catch (e, stackTrace) {
      _handleError(e, stackTrace);
      return [];
    }
  }

  Future<PackagesStatusModel> getPackagesStatus() async {
    if (!await _checkInternetConnection()) {
      return PackagesStatusModel(
        enabled: false,
        message: tr('no_internet_connection'),
      );
    }
    try {
      final res =
          await _networkService.request(method: 'GET', path: '/check-packages');
      final packagesStatus = PackagesStatusModel.fromJson(res ?? {});
      return packagesStatus;
    } catch (e, stackTrace) {
      _handleError(e, stackTrace);
      return PackagesStatusModel(
        enabled: false,
        message: tr('service_currently_unavailable'),
      );
    }
  }

  Future<UserModel?> getUserByPhone({
    required String phone,
    required String countryCode,
  }) async {
    if (!await _checkInternetConnection()) return null;
    try {
      final res = await _networkService.request(
        method: 'POST',
        path: '/get-user-by-phone',
        data: {'phone': phone, 'country_code': countryCode},
      );
      final user = UserModel.fromMap(res?['data']);
      return user;
    } catch (e, stackTrace) {
      _handleError(e, stackTrace);
      return null;
    }
  }

  Future<OrderModel?> getOrderDetails({required int orderID}) async {
    if (!await _checkInternetConnection()) return null;
    try {
      final res = await _networkService.request(
          method: 'GET', path: '/order-details/$orderID');
      final order = OrderModel.fromMap(res?['data']);

      return order;
    } catch (e, stackTrace) {
      _handleError(e, stackTrace);
      return null;
    }
  }

  Future<PaymentModel?> pay({
    required int userID,
    required int paymentAmount,
    required int? purchasedItemID,
    required String paymentMethod,
    required String purchasedItemType,
    required String transactionID,
    required String? receivedID,
    String status = K.purchaseStatusSuccess,
  }) async {
    try {
      final data = <String, dynamic>{
        'type': paymentMethod,
        'transactionable_id': userID,
        'forable_id': purchasedItemID,
        'forable_type': purchasedItemType,
        'credit': paymentAmount,
        'status': status,
        'transaction_id': transactionID,
        'receiver_id': receivedID,
      };
      final res = await _networkService.request(
          method: 'POST', path: '/payment', data: data);
      final payment = PaymentModel.fromMap(res?['data']);
      return payment;
    } catch (e, stackTrace) {
      _handleError(e, stackTrace);
      return null;
    }
  }

  Future<AboutUsModel?> getAboutUs() async {
    if (!await _checkInternetConnection()) return null;
    try {
      final res = await _networkService.request(method: 'GET', path: '/about');
      final aboutUs = AboutUsModel.fromJson(res?['data']['terms']);
      return aboutUs;
    } catch (e, stackTrace) {
      _handleError(e, stackTrace);
      return null;
    }
  }

  Future<TermsAndConditionsModel?> getTermsAndConditions() async {
    if (!await _checkInternetConnection()) return null;
    try {
      final res = await _networkService.request(method: 'GET', path: '/terms');
      final termsAndConditions =
          TermsAndConditionsModel.fromJson(res?['data']['terms']);
      return termsAndConditions;
    } catch (e, stackTrace) {
      _handleError(e, stackTrace);
      return null;
    }
  }

  Future<List<NotificationModel>?> getNotifications() async {
    if (!await _checkInternetConnection()) return null;
    try {
      final res =
          await _networkService.request(method: 'GET', path: '/notifications');
      final notifications = ((res?['data']['notifications'] as List?) ?? [])
          .map<NotificationModel>((e) {
        return NotificationModel.fromMap(e);
      }).toList();
      return notifications;
    } catch (e, stackTrace) {
      _handleError(e, stackTrace);
      return null;
    }
  }

  Future<bool> deleteAddress({required int id}) async {
    if (!await _checkInternetConnection()) return false;
    try {
      final res = await _networkService.request(
          method: 'DELETE', path: '/addresse/$id');
      return res?['status'];
    } catch (e, stackTrace) {
      _handleError(e, stackTrace);
      return false;
    }
  }

  Future<WalletModel?> getWallet() async {
    if (!await _checkInternetConnection()) return null;
    try {
      final res = await _networkService.request(method: 'GET', path: '/wallet');
      if (res == null || res['data'] == null) {
        return null;
      }
      final wallet = WalletModel.fromJson(res['data']);
      return wallet;
    } catch (e, stackTrace) {
      _handleError(e, stackTrace);
      return null;
    }
  }

  Future<bool> sendGift({
    required String phoneCountyCode,
    required String phone,
    required double amount,
    String transformType = K.transferTypeWallet,
  }) async {
    if (!await _checkInternetConnection()) return false;
    try {
      final res = await _networkService
          .request(method: 'POST', path: '/send-gift', data: {
        'country_code': phoneCountyCode,
        'phone': phone,
        'money': amount.toInt(),
        'tranform_type': transformType,
      });
      return res?['status'];
    } catch (e, stackTrace) {
      _handleError(e, stackTrace);
      return false;
    }
  }

  Future<List<PackageItemModel>?> getPackages() async {
    if (!await _checkInternetConnection()) return null;
    try {
      final res =
          await _networkService.request(method: 'GET', path: '/packages');
      final packages = ((res?['data']['packages'] as List?) ?? [])
          .map<PackageItemModel>((e) {
        return PackageItemModel.fromMap(e);
      }).toList();
      return packages;
    } catch (e, stackTrace) {
      _handleError(e, stackTrace);
      return null;
    }
  }

  Future<List<TargetItemModel>?> getTargets() async {
    if (!await _checkInternetConnection()) return null;
    try {
      final res =
          await _networkService.request(method: 'GET', path: '/targets');
      final targets =
          ((res?['data']['targets'] as List?) ?? []).map<TargetItemModel>((e) {
        return TargetItemModel.fromMap(e);
      }).toList();
      return targets;
    } catch (e, stackTrace) {
      _handleError(e, stackTrace);
      return null;
    }
  }

  Future<OrderModel?> updateOrderStatus({
    required int orderID,
    required String status,
  }) async {
    if (!await _checkInternetConnection()) return null;
    try {
      final res = await _networkService.request(
        method: 'POST',
        path: '/user-update-order-status',
        data: {'order_id': orderID, 'status': status},
      );
      final order = OrderModel.fromMap(res?['data']);
      return order;
    } catch (e, stackTrace) {
      _handleError(e, stackTrace);
      return null;
    }
  }

  Future<List<OrderModel>?> getUserOrders() async {
    if (!await _checkInternetConnection()) return null;
    try {
      final res =
          await _networkService.request(method: 'GET', path: '/user-orders');
      final orders = ((res?['data'] as List?) ?? []).map<OrderModel>((e) {
        return OrderModel.fromMap(e);
      }).toList();
      return orders;
    } catch (e, stackTrace) {
      _handleError(e, stackTrace);
      return null;
    }
  }

  Future<OrderModel?> createOrder({
    required UserAddressModel address,
    required CarTypeModel car,
    required OrderCategoryItemModel category,
    required String cityName,
    CouponModel? coupon,
    String? scheduledAt,
    int? cartId,
  }) async {
    if (!await _checkInternetConnection()) return null;
    try {
      final data = <String, dynamic>{
        'category_id': category.id,
        'car_id': car.id,
        'lat': address.lat.toString(),
        'lng': address.lng.toString(),
        'address': address.address.trim(),
        'address_id': address.id,
        'city': cityName.trim(),
        'scheduled_at': scheduledAt,
        if (cartId != null) 'cart_id': cartId,
      };

      if (coupon?.id != null) {
        data['coupon_id'] = coupon!.id;
      }

      final res = await _networkService.request(
        method: 'POST',
        path: '/send-order',
        data: data,
      );
      print(111111111111111);
      print(res?['data']);
      print(111111111111111);

      if (res?['message'] == 'apis.not_available') {
        return OrderModel(
          address: '',
          carType: null,
          category: null,
          costAfterDiscount: 0,
          costBeforeDiscount: 0,
          coupon: null,
          delegate: null,
          discount: null,
          errorMessage: 'service_not_available_in_this_location',
          finalCost: 0,
          id: DateTime.now().millisecondsSinceEpoch,
          lat: '',
          lng: '',
          orderNumber: '',
          paymentStatus: '',
          paymentType: '',
          status: '',
          user: null,
        );
      }

      if (res?['data'] == null) {
        return null;
      }

      return OrderModel.fromMap(res?['data']);
    } catch (e, stackTrace) {
      _handleError(e, stackTrace);
      return null;
    }
  }

  Future<CouponModel?> checkCoupon({required String coupon}) async {
    if (!await _checkInternetConnection()) return null;
    try {
      final res = await _networkService.request(
        method: 'POST',
        path: '/check-coupon',
        data: {'coupon': coupon},
      );
      if (res?['data'] == null) return null;
      final couponModel = CouponModel.fromMap(res?['data']);
      return couponModel;
    } catch (e, stackTrace) {
      _handleError(e, stackTrace);
      return null;
    }
  }

  // Future<List<CarTypeModel>?> getCarTypes() async {
  //   try {
  //     final res = await _networkService.request(  method:  'GET',path:'/cars');
  //     return ((res?['data'] as List?) ?? []).map<CarTypeModel>((e) {
  //       return CarTypeModel.fromMap(e);
  //     }).toList();
  //   } catch (e, stackTrace) {
  //     _handleError(e, stackTrace);
  //     return null;
  //   }
  // }

  Future<List<UserAddressModel>?> getUserAddresses() async {
    if (!await _checkInternetConnection()) return null;
    try {
      final res =
          await _networkService.request(method: 'GET', path: '/addresses');
      final addresses =
          ((res?['data'] as List?) ?? []).map<UserAddressModel>((e) {
        return UserAddressModel.fromMap(e);
      }).toList();
      return addresses;
    } catch (e, stackTrace) {
      _handleError(e, stackTrace);
      return null;
    }
  }

  Future<UserAddressModel?> addAddress({
    required double lat,
    required double lng,
    required String name,
    required String address,
    required String city,
  }) async {
    if (!await _checkInternetConnection()) return null;
    try {
      final data = <String, dynamic>{
        'lat': lat,
        'lng': lng,
        'name': name,
        'city': city,
        'address': address
            .substring(
              0,
              math.min(K.userAddressMaxLength, address.trim().length),
            )
            .trim(),
      };

      // Get the current user model
      final userModel = sl.get<UserProvider>().userModel;

      // If user is not authenticated, return a local address model
      if (userModel?.token == null) {
        return UserAddressModel(
          id: null,
          name: name,
          address: address,
          lat: lat.toString(),
          lng: lng.toString(),
        );
      }

      final res = await _networkService.request(
        method: 'POST',
        path: '/add-address',
        data: data,
      );

      if (res == null || !res.containsKey('data')) {
        throw ApiException('Invalid response format from server');
      }

      return UserAddressModel.fromMap(res['data']);
    } on UnauthorizedException catch (e, stackTrace) {
      _handleError(e, stackTrace);
      return null;
    } on ApiException catch (e, stackTrace) {
      _handleError(e, stackTrace);
      return null;
    } catch (e, stackTrace) {
      _handleError(e, stackTrace);
      return null;
    }
  }

  Future<bool> updateNotificationSettings(
      {required bool enabled, required BuildContext context}) async {
    debugPrint('Updating notification settings - enabled: $enabled');

    if (!await _checkInternetConnection()) {
      BotToast.showText(
        text: tr('no_internet_connection'),
        contentColor: Colors.red,
      );
      return false;
    }

    try {
      final userModel = context.read<UserProvider>().userModel;
      if (userModel == null) {
        debugPrint('UserModel is null, cannot update notification settings');
        return false;
      }

      // تحديث النموذج في واجهة المستخدم أولاً
      final updatedUser = userModel.copyWith(isNotify: enabled ? 1 : 0);
      await context.read<UserProvider>().setUserModel(updatedUser);

      // محاولة تحديث الإعدادات على الخادم
      try {
        if (enabled) {
          // في حالة التفعيل، نطلب الإذن من المستخدم
          final messaging = FirebaseMessaging.instance;
          final settings = await messaging.requestPermission(
            alert: true,
            badge: true,
            sound: true,
            provisional: true,
          );

          if (settings.authorizationStatus == AuthorizationStatus.authorized ||
              settings.authorizationStatus == AuthorizationStatus.provisional) {
            await messaging.setForegroundNotificationPresentationOptions(
              alert: true,
              badge: true,
              sound: true,
            );

            // لا ننتظر الحصول على التوكن، فقط نرجع نجاح العملية
            return true;
          }
          return false;
        } else {
          // في حالة التعطيل، مجرد تحديث isNotify يكفي
          return true;
        }
      } catch (e) {
        debugPrint('Error in Firebase operations: $e');
        // حتى لو فشلت عمليات Firebase، ما زلنا قد حدثنا نموذج المستخدم
        return true;
      }
    } catch (e, stackTrace) {
      _handleError(e, stackTrace);
      return false;
    }
  }

// Add this new function to the UserAPI class
  Future<AvailableTimeSlotsResponse?> getAvailableTimeSlots({
    required String date,
  }) async {
    print(
      date,
    );

    if (!await _checkInternetConnection()) return null;
    try {
      final res = await _networkService.request(
        method: 'GET',
        path: '/available-times',
        queryParameters: {
          'date': date,
        },
      );

      if (res == null) {
        NotificationUtils.showError(tr('error_fetching_time_slots'));
        return null;
      }

      return AvailableTimeSlotsResponse.fromMap(res);
    } catch (e, stackTrace) {
      _handleError(e, stackTrace);
      return null;
    }
  }

  Future<UserModel?> updateProfile({
    required String name,
    required String email,
    required File? profileImage,
    int? cityID,
    String? neighborhood,
  }) async {
    if (!await _checkInternetConnection()) return null;
    try {
      final data = <String, dynamic>{
        'name': name,
        'email': email,
        'city_id': cityID,
        'state': neighborhood,
      };
      if (profileImage != null) {
        data['image'] = await MultipartFile.fromFile(profileImage.path,
            filename: path.basename(profileImage.path));
      }
      final res = await _networkService.request(
        method: 'POST',
        path: '/update-profile',
        data: FormData.fromMap(data),
      );

      if (!(res?['status'] ?? false)) {
        NotificationUtils.showError(res?['message']);
        return null;
      }
      final userModel = UserModel.fromMap(res?['data']['user']);
      return userModel;
    } catch (e, stackTrace) {
      _handleError(e, stackTrace);
      return null;
    }
  }

  Future<List<CityModel>?> getCities() async {
    if (!await _checkInternetConnection()) return null;
    try {
      final res = await _networkService.request(method: 'GET', path: '/cities');
      final cities = ((res?['data'] as List?) ?? []).map<CityModel>((e) {
        return CityModel.fromMap(e);
      }).toList();
      return cities;
    } catch (e, stackTrace) {
      _handleError(e, stackTrace);
      return null;
    }
  }

  Future<HomeTabModel?> getHomeContent() async {
    if (!await _checkInternetConnection()) return null;
    try {
      final res =
          await _networkService.requestWithCache(method: 'GET', path: '/home');
      final homeTabModel = HomeTabModel.fromJson(res['data']);
      return homeTabModel;
    } catch (e, stackTrace) {
      _handleError(e, stackTrace);
      return null;
    }
  }
}
