import 'dart:developer';
import 'dart:io';

import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:one_drop/services/network_service.dart';
import 'package:one_drop/services/prefs_service.dart';

import '../models/user_model.dart';
import '../services/service_locator.dart';
import 'package:dio/dio.dart';

class AuthAPI {
  Future<String> _getDeviceId() async {
    try {
      final token = await FirebaseMessaging.instance.getToken();
      if (token != null) {
        log('🔔 [AUTH] Firebase token retrieved', name: 'AuthAPI');
        return token;
      }
    } catch (e) {
      log('⚠️ [AUTH] Firebase token error: $e', name: 'AuthAPI');
    }

    final fallbackId =
        '${Platform.isAndroid ? 'android' : 'ios'}_${DateTime.now().millisecondsSinceEpoch}';
    log('⚠️ [AUTH] Using fallback device ID', name: 'AuthAPI');
    return fallbackId;
  }

  Future<UserModel?> activateAccount({
    required String countryCode,
    required String phone,
    required String otpCode,
  }) async {
    try {
      log('📱 [AUTH] Activating account - Phone: $phone', name: 'Auth<PERSON><PERSON>');

      final deviceId = await _getDeviceId();

      final res = await sl.get<NetworkService>().request(
        path: '/activate-account',
        method: 'POST',
        data: {
          'country_code': countryCode,
          'phone': phone,
          'code': otpCode,
          'device_id': deviceId,
          'device_type': Platform.isAndroid ? 'android' : 'ios',
        },
      );

      if (res == null) {
        log('❌ [AUTH] No response received', name: 'AuthAPI');
        return null;
      }

      final responseData = res ?? res;
      final userData = responseData['data']['user'];
      final token = responseData['data']["user"]['token'];
      PrefsService prefsService = sl.get<PrefsService>();
      prefsService.setString("token", token);

      print(userData);
      print(token + "${prefsService.getString("token")}");

      print("*****************");
      if (userData == null || token == null) {
        log('❌ [AUTH] Missing user data or token', name: 'AuthAPI');
        return null;
      }

      // final userMap = Map<String, dynamic>.from(userData);
      // userMap['token'] = token;

      final user = UserModel.fromMap(userData);
      log('✅ [AUTH] Account activated - User ID: ${user.id}', name: 'AuthAPI');
      return user;
    } catch (e, stackTrace) {
      log('❌ [AUTH] Activation failed',
          name: 'AuthAPI', error: e, stackTrace: stackTrace);
      return null;
    }
  }

  Future<String?> sendOTPCode({
    required String phone,
    required String countryCode,
  }) async {
    try {
      log('📱 [AUTH] Sending OTP - Phone: $phone', name: 'AuthAPI');
      final res = await sl.get<NetworkService>().request(
        path: '/send-code',
        method: 'POST',
        data: {'country_code': countryCode, 'phone': phone},
      );

      return res?['message'];
    } catch (e, stackTrace) {
      log('❌ [AUTH] OTP sending failed',
          name: 'AuthAPI', error: e, stackTrace: stackTrace);
      return null;
    }
  }

  Future<UserModel?> refreshToken(String currentToken) async {
    try {
      log('🔄 [AUTH] Refreshing token', name: 'AuthAPI');

      final dio = Dio()
        ..options.baseUrl = "http://qatrawahda.sa/api"
        ..interceptors.add(LogInterceptor(
          requestBody: true,
          responseBody: true,
          error: true,
          logPrint: (obj) => log(obj.toString(), name: 'AuthAPI'),
        ));

      final response = await dio.post<Map<String, dynamic>>(
        '/refresh-token',
        options: Options(
          headers: {
            'Authorization': 'Bearer $currentToken',
            'Accept': 'application/json',
          },
        ),
      );

      final responseData = response.data;
      if (responseData == null) {
        log('⚠️ [AUTH] Empty refresh token response', name: 'AuthAPI');
        return null;
      }

      final userData = responseData['data'];
      if (userData == null) {
        log('⚠️ [AUTH] No user data in refresh response', name: 'AuthAPI');
        return null;
      }

      final userModel = UserModel.fromMap(userData);
      log('✅ [AUTH] Token refreshed', name: 'AuthAPI');
      return userModel;
    } catch (e, stackTrace) {
      log('❌ [AUTH] Token refresh failed',
          name: 'AuthAPI', error: e, stackTrace: stackTrace);
      return null;
    }
  }
}
