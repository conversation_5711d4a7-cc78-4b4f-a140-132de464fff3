import 'dart:convert';
import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:one_drop/models/user_model.dart';
import 'package:one_drop/services/prefs_service.dart';
import 'package:one_drop/services/service_locator.dart';

import '../constants.dart';

class UserProvider with ChangeNotifier {
  dynamic nextRouteParam;
  String? nextRoute;

  bool get isAuth => _userModel?.id != null && _userModel?.token != null;

  UserModel? _userModel;
  UserModel? get userModel => _userModel;

  Future<void> setUserModel(UserModel? value, {bool writeToPrefs = true}) async {
    _userModel = value;
    log('👤 [UserProvider] Setting user model: ${value?.toMap()}', name: 'UserProvider');
    notifyListeners();
    if (!writeToPrefs) return;
    
    final prefsService = sl.get<PrefsService>();
    if (value == null) {
      log('🗑️ [UserProvider] Removing user model from prefs', name: 'UserProvider');
      await prefsService.remove(K.userModelPrefsKey);
    } else {
      try {
        log('💾 [UserProvider] Saving user model to prefs', name: 'UserProvider');
        final json = value.toMap();
        log('📦 [UserProvider] User model JSON: $json', name: 'UserProvider');
        await prefsService.setString(K.userModelPrefsKey, jsonEncode(json));
        log('✅ [UserProvider] Successfully saved user model to prefs', name: 'UserProvider');
      } catch (e, stackTrace) {
        log('❌ [UserProvider] Error saving user model to prefs', 
            name: 'UserProvider',
            error: e,
            stackTrace: stackTrace);
      }
    }
  }

  Future<void> initUserModel() async {
    try {
      final prefsService = sl.get<PrefsService>();
      final userModelString = prefsService.getString(K.userModelPrefsKey);
      log('📱 [UserProvider] Loading user model from prefs: $userModelString', name: 'UserProvider');
      
      if (userModelString == null) {
        log('⚠️ [UserProvider] No user model found in prefs', name: 'UserProvider');
        return;
      }
      
      final json = jsonDecode(userModelString);
      log('📦 [UserProvider] Decoded JSON: $json', name: 'UserProvider');
      
      final model = UserModel.fromMap(json);
      log('✅ [UserProvider] Successfully loaded user model: ${model.toMap()}', name: 'UserProvider');
      
      await setUserModel(model, writeToPrefs: false);
    } catch (e, stackTrace) {
      log('❌ [UserProvider] Error loading user model', 
          name: 'UserProvider',
          error: e,
          stackTrace: stackTrace);
    }
  }
}
