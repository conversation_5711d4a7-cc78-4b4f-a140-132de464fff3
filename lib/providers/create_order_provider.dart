import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:one_drop/models/car_type_model.dart';
import 'package:one_drop/models/cart.dart';
import 'package:one_drop/models/coupon_model.dart';
import 'package:one_drop/models/order_category_item_model.dart';
import 'package:one_drop/models/user_address_model.dart';

class CreateOrderProvider with ChangeNotifier {
  CarTypeModel? _selectedCarType;
  OrderCategoryItemModel? _selectedOrderCategory;
  UserAddressModel? _selectedAddress;
  CouponModel? _coupon;
  DateTime? _scheduledDate;
  DateTime? _scheduledDateTime;
  String _cityOrNeighborhoodName = '';
  CartResponse? _cart;
  bool _hasFreeWash = false;

  // Getters - include selectors without underscore for compatibility
  CarTypeModel? get selectedCarType => _selectedCarType;
  OrderCategoryItemModel? get selectedOrderCategory => _selectedOrderCategory;
  UserAddressModel? get selectedAddress => _selectedAddress;
  CouponModel? get coupon => _coupon;
  DateTime? get scheduledDate => _scheduledDate;
  DateTime? get scheduledDateTime => _scheduledDateTime;
  String get cityOrNeighborhoodName => _cityOrNeighborhoodName;
  CartResponse? get cart => _cart;
  bool get hasFreeWash => _hasFreeWash;

  // الحصول على إجمالي سعر الطلب مع تطبيق الخصومات
  double get orderTotalPrice {
    final discountAmount = _coupon?.discount ?? 0;
    final discountPercentage = _coupon?.percentage ?? 0;
    final carPrice = _selectedCarType?.price ?? 0;

    double total;
    if (discountPercentage > 0) {
      final amount = carPrice * (discountPercentage / 100);
      total = carPrice - amount;
    } else if (discountAmount > 0) {
      total = carPrice - discountAmount;
    } else {
      total = carPrice;
    }

    // التأكد من أن السعر ليس أقل من صفر
    return total > 0 ? total : 0;
  }

  // Format date for display
  String? get formattedDate {
    if (_scheduledDate == null) return null;
    return DateFormat('yyyy-MM-dd').format(_scheduledDate!);
  }

  // Format date and time for API
  String? get formattedDateTime {
    if (_scheduledDateTime == null) return null;
    return DateFormat('yyyy-MM-dd HH:mm').format(_scheduledDateTime!);
  }

  // Get only time part from scheduledDateTime
  String? get formattedTime {
    if (_scheduledDateTime == null) return null;
    return DateFormat('HH:mm').format(_scheduledDateTime!);
  }

  // Setters
  set selectedCarType(CarTypeModel? value) {
    _selectedCarType = value;
    notifyListeners();
  }

  set selectedOrderCategory(OrderCategoryItemModel? value) {
    _selectedOrderCategory = value;
    notifyListeners();
  }

  set selectedAddress(UserAddressModel? value) {
    _selectedAddress = value;
    notifyListeners();
  }

  set cityOrNeighborhoodName(String value) {
    _cityOrNeighborhoodName = value;
    notifyListeners();
  }

  set coupon(CouponModel? value) {
    _coupon = value;
    notifyListeners();
  }

  set hasFreeWash(bool value) {
    _hasFreeWash = value;
    notifyListeners();
  }

  set cart(CartResponse? value) {
    _cart = value;
    notifyListeners();
  }

  // Methods
  void setSelectedCarType(CarTypeModel? type) {
    _selectedCarType = type;
    notifyListeners();
  }

  void setSelectedOrderCategory(OrderCategoryItemModel? orderCategory) {
    _selectedOrderCategory = orderCategory;
    notifyListeners();
  }

  void setSelectedAddress(UserAddressModel? address) {
    _selectedAddress = address;
    notifyListeners();
  }

  void setCityOrNeighborhoodName(String name) {
    _cityOrNeighborhoodName = name;
    notifyListeners();
  }

  void setCoupon(CouponModel? couponModel) {
    _coupon = couponModel;
    notifyListeners();
  }

  void setScheduledDate(DateTime? date) {
    _scheduledDate = date;
    // If we change the date but not the time, we should also update scheduledDateTime
    if (date != null && _scheduledDateTime != null) {
      // Preserve the time part from the existing scheduledDateTime
      final timeOfDay = TimeOfDay.fromDateTime(_scheduledDateTime!);
      // Create a new DateTime with the new date and existing time
      _scheduledDateTime = DateTime(
        date.year,
        date.month,
        date.day,
        timeOfDay.hour,
        timeOfDay.minute,
      );
    } else if (date == null) {
      // If date is reset, also reset the datetime
      _scheduledDateTime = null;
    }
    notifyListeners();
  }

  void setScheduledDateTime(DateTime? dateTime) {
    _scheduledDateTime = dateTime;
    // If we set the full dateTime, also update the date part
    if (dateTime != null) {
      _scheduledDate = DateTime(dateTime.year, dateTime.month, dateTime.day);
    }
    notifyListeners();
  }

  // Set just the time part, keeping the existing date
  void setScheduledTime(TimeOfDay time) {
    if (_scheduledDate == null) {
      // Can't set time without a date
      return;
    }

    _scheduledDateTime = DateTime(
      _scheduledDate!.year,
      _scheduledDate!.month,
      _scheduledDate!.day,
      time.hour,
      time.minute,
    );
    notifyListeners();
  }

  // Check if the order has all required fields
  bool get isOrderValid {
    return _selectedCarType != null &&
        _selectedOrderCategory != null &&
        _selectedAddress != null;
  }

  // Check if scheduled date and time are both selected when scheduling is enabled
  bool get isScheduleValid {
    if (_scheduledDate == null) return true; // Scheduling is disabled
    return _scheduledDateTime !=
        null; // Time must be selected if date is selected
  }

  // Calculate the total price (order + cart if included)
  double calculateTotalPrice({bool includeCart = false}) {
    double total =
        orderTotalPrice; // استخدام القيمة المحسوبة من orderTotalPrice
    if (includeCart && _cart != null) {
      total += (_cart!.total as double? ?? 0.0);
    }
    return total;
  }

  // Reset all order data
  void resetOrder() {
    _selectedCarType = null;
    _selectedOrderCategory = null;
    _selectedAddress = null;
    _coupon = null;
    _scheduledDate = null;
    _scheduledDateTime = null;
    _cityOrNeighborhoodName = '';
    _cart = null;
    _hasFreeWash = false;
    notifyListeners();
  }

  // تنظيف كل البيانات (المستخدمة في home_create_wash_order_list.dart)
  void clearAll() {
    _selectedCarType = null;
    _selectedOrderCategory = null;
    _selectedAddress = null;
    _coupon = null;
    _scheduledDate = null;
    _scheduledDateTime = null;
    _cityOrNeighborhoodName = '';
    _cart = null;
    // لا نقوم بإعادة تعيين _hasFreeWash هنا لأنه يتم تعيينه مباشرة بعد استدعاء هذه الدالة
    notifyListeners();
  }
}
