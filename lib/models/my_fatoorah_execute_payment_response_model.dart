class MyFatoorahExecutePaymentResponse {
  MyFatoorahExecutePaymentResponse({
    required this.isSuccess,
    required this.message,
    required this.validationErrors,
    required this.data,
  });

  final bool isSuccess;
  final String message;
  final String? validationErrors;
  final ExcutePaymentResponseDataModel data;

  MyFatoorahExecutePaymentResponse copyWith({
    bool? isSuccess,
    String? message,
    String? validationErrors,
    ExcutePaymentResponseDataModel? data,
  }) =>
      MyFatoorahExecutePaymentResponse(
        isSuccess: isSuccess ?? this.isSuccess,
        message: message ?? this.message,
        validationErrors: validationErrors ?? this.validationErrors,
        data: data ?? this.data,
      );

  factory MyFatoorahExecutePaymentResponse.fromMap(Map<String, dynamic> json) => MyFatoorahExecutePaymentResponse(
        isSuccess: json["IsSuccess"] ?? false,
        message: json["Message"] ?? '',
        validationErrors: json["ValidationErrors"]?.toString(),
        data: ExcutePaymentResponseDataModel.fromMap(json["Data"]),
      );

  Map<String, dynamic> toMap() => {
        "IsSuccess": isSuccess,
        "Message": message,
        "ValidationErrors": validationErrors,
        "Data": data.toMap(),
      };
}

class ExcutePaymentResponseDataModel {
  ExcutePaymentResponseDataModel({
    required this.invoiceId,
    required this.isDirectPayment,
    required this.paymentUrl,
    required this.customerReference,
    required this.userDefinedField,
    required this.recurringId,
  });

  final int invoiceId;
  final bool isDirectPayment;
  final String paymentUrl;
  final String recurringId;
  final String? customerReference;
  final String? userDefinedField;

  ExcutePaymentResponseDataModel copyWith({
    int? invoiceId,
    bool? isDirectPayment,
    String? paymentUrl,
    String? customerReference,
    String? userDefinedField,
    String? recurringId,
  }) =>
      ExcutePaymentResponseDataModel(
        invoiceId: invoiceId ?? this.invoiceId,
        isDirectPayment: isDirectPayment ?? this.isDirectPayment,
        paymentUrl: paymentUrl ?? this.paymentUrl,
        customerReference: customerReference ?? this.customerReference,
        userDefinedField: userDefinedField ?? this.userDefinedField,
        recurringId: recurringId ?? this.recurringId,
      );

  factory ExcutePaymentResponseDataModel.fromMap(Map<String, dynamic> json) => ExcutePaymentResponseDataModel(
        invoiceId: json["InvoiceId"],
        isDirectPayment: json["IsDirectPayment"],
        paymentUrl: json["PaymentURL"],
        customerReference: json["CustomerReference"]?.toString(),
        userDefinedField: json["UserDefinedField"]?.toString(),
        recurringId: json["RecurringId"],
      );

  Map<String, dynamic> toMap() => {
        "InvoiceId": invoiceId,
        "IsDirectPayment": isDirectPayment,
        "PaymentURL": paymentUrl,
        "CustomerReference": customerReference,
        "UserDefinedField": userDefinedField,
        "RecurringId": recurringId,
      };
}
