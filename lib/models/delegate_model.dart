class DelegateModel {
  DelegateModel({
    required this.identityImage,
    required this.frontCarImage,
    required this.carLicense,
    required this.backCarImage,
    required this.carImage,
  });

  final String? identityImage;
  final String? frontCarImage;
  final String? carLicense;
  final String? backCarImage;
  final String? carImage;

  DelegateModel copyWith({
    String? identityImage,
    String? frontCarImage,
    String? carLicense,
    String? backCarImage,
    String? carImage,
  }) =>
      DelegateModel(
        identityImage: identityImage ?? this.identityImage,
        frontCarImage: frontCarImage ?? this.frontCarImage,
        carLicense: carLicense ?? this.carLicense,
        backCarImage: backCarImage ?? this.backCarImage,
        carImage: carImage ?? this.carImage,
      );

  factory DelegateModel.fromMap(Map<String, dynamic> json) => DelegateModel(
        identityImage: json["identity_image"],
        frontCarImage: json["front_car_image"],
        carLicense: json["car_license"],
        backCarImage: json["back_car_image"],
        carImage: json["car_image"],
      );

  Map<String, dynamic> toMap() => {
        "identity_image": identityImage,
        "front_car_image": frontCarImage,
        "car_license": carLicense,
        "back_car_image": backCarImage,
        "car_image": carImage,
      };
}
