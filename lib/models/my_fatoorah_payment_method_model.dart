class MyFatoorahPaymentMethodModel {
  MyFatoorahPaymentMethodModel({
    required this.paymentMethodID,
    required this.paymentMethodAr,
    required this.paymentMethodEn,
    required this.paymentMethodCode,
    required this.isDirectPayment,
    required this.serviceCharge,
    required this.totalAmount,
    required this.currencyIso,
    required this.imageUrl,
    required this.isEmbeddedSupported,
    required this.paymentCurrencyIso,
  });

  final int paymentMethodID;
  final String paymentMethodAr;
  final String paymentMethodEn;
  final String paymentMethodCode;
  final bool isDirectPayment;
  final double serviceCharge;
  final double totalAmount;
  final String currencyIso;
  final String imageUrl;
  final bool isEmbeddedSupported;
  final String paymentCurrencyIso;

  MyFatoorahPaymentMethodModel copyWith({
    int? paymentMethodID,
    String? paymentMethodAr,
    String? paymentMethodEn,
    String? paymentMethodCode,
    bool? isDirectPayment,
    double? serviceCharge,
    double? totalAmount,
    String? currencyIso,
    String? imageUrl,
    bool? isEmbeddedSupported,
    String? paymentCurrencyIso,
  }) =>
      MyFatoorahPaymentMethodModel(
        paymentMethodID: paymentMethodID ?? this.paymentMethodID,
        paymentMethodAr: paymentMethodAr ?? this.paymentMethodAr,
        paymentMethodEn: paymentMethodEn ?? this.paymentMethodEn,
        paymentMethodCode: paymentMethodCode ?? this.paymentMethodCode,
        isDirectPayment: isDirectPayment ?? this.isDirectPayment,
        serviceCharge: serviceCharge ?? this.serviceCharge,
        totalAmount: totalAmount ?? this.totalAmount,
        currencyIso: currencyIso ?? this.currencyIso,
        imageUrl: imageUrl ?? this.imageUrl,
        isEmbeddedSupported: isEmbeddedSupported ?? this.isEmbeddedSupported,
        paymentCurrencyIso: paymentCurrencyIso ?? this.paymentCurrencyIso,
      );

  factory MyFatoorahPaymentMethodModel.fromMap(Map<String, dynamic> json) => MyFatoorahPaymentMethodModel(
        paymentMethodID: json["PaymentMethodId"],
        paymentMethodAr: json["PaymentMethodAr"],
        paymentMethodEn: json["PaymentMethodEn"],
        paymentMethodCode: json["PaymentMethodCode"],
        isDirectPayment: json["IsDirectPayment"],
        serviceCharge: json["ServiceCharge"].toDouble(),
        totalAmount: json["TotalAmount"].toDouble(),
        currencyIso: json["CurrencyIso"],
        imageUrl: json["ImageUrl"],
        isEmbeddedSupported: json["IsEmbeddedSupported"],
        paymentCurrencyIso: json["PaymentCurrencyIso"],
      );

  Map<String, dynamic> toMap() => {
        "PaymentMethodId": paymentMethodID,
        "PaymentMethodAr": paymentMethodAr,
        "PaymentMethodEn": paymentMethodEn,
        "PaymentMethodCode": paymentMethodCode,
        "IsDirectPayment": isDirectPayment,
        "ServiceCharge": serviceCharge,
        "TotalAmount": totalAmount,
        "CurrencyIso": currencyIso,
        "ImageUrl": imageUrl,
        "IsEmbeddedSupported": isEmbeddedSupported,
        "PaymentCurrencyIso": paymentCurrencyIso,
      };
}
