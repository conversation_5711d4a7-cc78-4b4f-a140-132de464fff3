class TargetItemModel {
  TargetItemModel({
    required this.id,
    required this.name,
    required this.desc,
    required this.image,
    required this.washCount,
    required this.freeWashCount,
    required this.enableFreeOrder,
    required this.totalCompletedOrders,
  });

  final int id;
  final String name;
  final String desc;
  final String? image;
  final int washCount;
  final int freeWashCount;
  final bool enableFreeOrder;
  final int totalCompletedOrders;

  TargetItemModel copyWith({
    int? id,
    String? name,
    String? desc,
    String? image,
    int? washCount,
    int? freeWashCount,
    bool? enableFreeOrder,
    int? totalCompletedOrders,
  }) =>
      TargetItemModel(
        id: id ?? this.id,
        name: name ?? this.name,
        desc: desc ?? this.desc,
        image: image ?? this.image,
        washCount: washCount ?? this.washCount,
        freeWashCount: freeWashCount ?? this.freeWashCount,
        enableFreeOrder: enableFreeOrder ?? this.enableFreeOrder,
        totalCompletedOrders: totalCompletedOrders ?? this.totalCompletedOrders,
      );

  factory TargetItemModel.fromMap(Map<String, dynamic> json) => TargetItemModel(
        id: json["id"],
        name: json["name"] ?? '',
        desc: json["desc"] ?? '',
        image: json["image"],
        washCount: json["wash_count"] ?? 0,
        freeWashCount: json["free_wash_count"] ?? 0,
        enableFreeOrder: json["enable_free_order"] ?? false,
        totalCompletedOrders: json["total_completed_orders"] ?? 0,
      );

  Map<String, dynamic> toMap() => {
        "id": id,
        "name": name,
        "desc": desc,
        "image": image,
        "wash_count": washCount,
        "free_wash_count": freeWashCount,
        "enable_free_order": enableFreeOrder,
        "total_completed_orders": totalCompletedOrders,
      };
}
