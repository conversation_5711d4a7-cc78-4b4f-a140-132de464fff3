class GoogleAddressModel {
  final double lat;
  final double lng;
  final String placeID;
  final String formattedAddress;
  final String name;
  final List<AddressComponentModel> addressComponents;

  GoogleAddressModel({
    required this.lat,
    required this.lng,
    required this.placeID,
    required this.formattedAddress,
    required this.name,
    required this.addressComponents,
  });

  GoogleAddressModel.fromJson({
    required Map json,
    required this.name,
  })  : lat = json['geometry']['location']['lat'],
        lng = json['geometry']['location']['lng'],
        placeID = json['place_id'],
        formattedAddress = json['formatted_address'],
        addressComponents = ((json['address_components'] as List?) ?? []).map<AddressComponentModel>((e) {
          return AddressComponentModel.fromJson(e);
        }).toList();

  String? get cityOrNeighborhoodName {
    try {
      return addressComponents.firstWhere((element) {
        return (element.types.contains('locality') && element.types.contains('political')) ||
            (element.types.contains('political') && element.types.contains('administrative_area_level_2'));
      }).longName;
    } catch (_) {
      return null;
    }
  }
}

class AddressComponentModel {
  final String shortName;
  final String longName;
  final List<String> types;

  AddressComponentModel({
    required this.shortName,
    required this.longName,
    required this.types,
  });

  factory AddressComponentModel.fromJson(Map json) {
    return AddressComponentModel(
      longName: json['long_name'] ?? '',
      shortName: json['short_name'] ?? '',
      types: <String>[...(json['types'] ?? [])],
    );
  }
}
