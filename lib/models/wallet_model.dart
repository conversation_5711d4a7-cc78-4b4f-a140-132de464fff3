import 'transaction_item_model.dart';

class WalletModel {
  final double balance;
  final List<TransactionItemModel> transactions;

  WalletModel({
    required this.balance,
    required this.transactions,
  });

  factory WalletModel.fromJson(Map json) {
    return WalletModel(
      balance: double.tryParse(json['wallet_balance']) ?? 0,
      transactions: ((json['transactions'] as List?) ?? []).map<TransactionItemModel>((e) {
        return TransactionItemModel.fromMap(e);
      }).toList(),
    );
  }
}
