import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';

class AboutUsModel {
  final String ar;
  final String en;

  AboutUsModel({
    required this.ar,
    required this.en,
  });

  factory AboutUsModel.fromJson(Map json) {
    return AboutUsModel(
      ar: json['terms_ar'] ?? '',
      en: json['terms_en'] ?? '',
    );
  }

  String getLocalizedTerms(BuildContext context) {
    final currentLocale = EasyLocalization.of(context)?.locale.languageCode;
    if (currentLocale == 'ar') {
      return ar;
    }
    return en;
  }
}
