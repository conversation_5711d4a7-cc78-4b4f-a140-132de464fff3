import 'package:one_drop/models/product.dart';

/// A lightweight version of Product that only contains the ID
/// Used when the cart API returns minimal product information
class CartProduct {
  final int id;

  CartProduct({
    required this.id,
  });

  factory CartProduct.fromJson(Map<String, dynamic> json) {
    return CartProduct(
      id: int.tryParse(json['id']?.toString() ?? '') ?? 0,
    );
  }

  /// Convert this CartProduct to a full Product object
  Product toProduct() {
    return Product.empty().copyWith(id: id);
  }
}
