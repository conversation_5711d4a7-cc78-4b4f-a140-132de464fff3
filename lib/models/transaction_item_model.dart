class TransactionItemModel {
  TransactionItemModel({
    required this.id,
    required this.ref,
    required this.senderId,
    required this.senderName,
    required this.recieverId,
    required this.recieverName,
    required this.amount,
    required this.paymentMethod,
    required this.status,
    required this.reason,
    required this.createdAt,
    required this.date,
  });

  final int id;
  final int? amount;
  final int? senderId;
  final int? recieverId;
  final String? ref;
  final String? senderName;
  final String? recieverName;
  final String? paymentMethod;
  final String? status;
  final String? reason;
  final String? createdAt;
  final DateTime? date;

  TransactionItemModel copyWith({
    int? id,
    String? ref,
    int? senderId,
    String? senderName,
    int? recieverId,
    String? recieverName,
    int? amount,
    String? paymentMethod,
    String? status,
    String? reason,
    String? createdAt,
    DateTime? date,
  }) =>
      TransactionItemModel(
        id: id ?? this.id,
        ref: ref ?? this.ref,
        senderId: senderId ?? this.senderId,
        senderName: senderName ?? this.senderName,
        recieverId: recieverId ?? this.recieverId,
        recieverName: recieverName ?? this.recieverName,
        amount: amount ?? this.amount,
        paymentMethod: paymentMethod ?? this.paymentMethod,
        status: status ?? this.status,
        reason: reason ?? this.reason,
        createdAt: createdAt ?? this.createdAt,
        date: date ?? this.date,
      );

  factory TransactionItemModel.fromMap(Map<String, dynamic> json) => TransactionItemModel(
        id: json["id"],
        ref: json["ref"],
        senderId: json["sender_id"],
        senderName: json["sender_name"],
        recieverId: json["reciever_id"],
        recieverName: json["reciever_name"],
        amount: json["amount"],
        paymentMethod: json["payment_method"],
        status: json["status"],
        reason: json["reason"],
        createdAt: json['created_at'],
        date: DateTime.tryParse(json['date']?.split('-').reversed.join('-') ?? ''),
      );

  Map<String, dynamic> toMap() => {
        "id": id,
        "ref": ref,
        "sender_id": senderId,
        "sender_name": senderName,
        "reciever_id": recieverId,
        "reciever_name": recieverName,
        "amount": amount,
        "payment_method": paymentMethod,
        "status": status,
        "reason": reason,
        "date": date?.toIso8601String(),
      };
}
