import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';

class PrivacyPolicyModel {
  final String ar;
  final String en;

  PrivacyPolicyModel({
    required this.ar,
    required this.en,
  });

  factory PrivacyPolicyModel.fromJson(Map json) {
    return PrivacyPolicyModel(
      ar: json['policy_ar'] ?? '',
      en: json['policy_en'] ?? '',
    );
  }

  String getLocalizedText(BuildContext context) {
    final currentLocale = EasyLocalization.of(context)?.locale.languageCode;
    if (currentLocale == 'ar') {
      return ar;
    }
    return en;
  }
}
