import 'package:one_drop/models/product.dart';
import 'package:one_drop/models/cart_product.dart';

class CartResponse {
  final int id;
  final int userId;
  final String status;
  final List<CartItem> items;
  double total;
  final DateTime createdAt;
  final DateTime updatedAt;

  CartResponse({
    required this.id,
    required this.userId,
    required this.status,
    required this.items,
    required this.total,
    required this.createdAt,
    required this.updatedAt,
  });

  // Calculate total from product prices
  double get calculatedTotal {
    return items.fold<double>(0, (sum, item) {
      final price = item.fullProduct?.price ?? 0;
      return sum + (price * item.quantity);
    });
  }

  factory CartResponse.fromJson(Map<String, dynamic> json) {
    print('Parsing Cart JSON: $json');

    return CartResponse(
      id: json['id'] ?? 0,
      userId: json['user_id'] ?? 0,
      status: json['status'] ?? '',
      items: (json['items'] as List<dynamic>?)
              ?.map((item) => CartItem.fromJson(item))
              .toList() ??
          [],
      total: double.tryParse(json['total']?.toString() ?? '0') ?? 0.0,
      createdAt: DateTime.parse(json['created_at'] ?? ''),
      updatedAt: DateTime.parse(json['updated_at'] ?? ''),
    );
  }

  Map<String, dynamic> toJson() => {
        'id': id,
        'user_id': userId,
        'status': status,
        'items': items.map((item) => item.toJson()).toList(),
        'total': calculatedTotal, // Use calculated total instead of API total
        'created_at': createdAt.toIso8601String(),
        'updated_at': updatedAt.toIso8601String(),
      };
}

class CartItem {
  final int id;
  final int productId;
  int quantity;
  final CartProduct product;
  final DateTime createdAt;
  final DateTime updatedAt;

  // Store the full product information when it's fetched
  Product? _fullProduct;

  // Getter for the full product information
  Product? get fullProduct => _fullProduct;

  // Setter to update the full product information
  set fullProduct(Product? product) {
    if (product != null && product.id == productId) {
      _fullProduct = product;
    }
  }

  CartItem({
    required this.id,
    required this.productId,
    required this.quantity,
    required this.product,
    required this.createdAt,
    required this.updatedAt,
  });

  factory CartItem.fromJson(Map<String, dynamic> json) {
    print('📦 Parsing cart item JSON: $json');

    final cartProduct = CartProduct.fromJson(
      json['product'] as Map<String, dynamic>? ?? {'id': json['product_id']},
    );

    print('📝 Product ID parsed for cart item: ${cartProduct.id}');

    final item = CartItem(
      id: int.tryParse(json['id']?.toString() ?? '') ?? 0,
      productId:
          int.tryParse(json['product_id']?.toString() ?? '') ?? cartProduct.id,
      quantity: int.tryParse(json['quantity']?.toString() ?? '') ?? 0,
      product: cartProduct,
      createdAt: DateTime.parse(
          json['created_at'] ?? DateTime.now().toIso8601String()),
      updatedAt: DateTime.parse(
          json['updated_at'] ?? DateTime.now().toIso8601String()),
    );

    print('✅ Cart item parsed successfully:');
    print('- Item ID: ${item.id}');
    print('- Quantity: ${item.quantity}');

    return item;
  }

  Map<String, dynamic> toJson() => {
        'id': id,
        'product_id': productId,
        'quantity': quantity,
        'product': product,
        'created_at': createdAt.toIso8601String(),
        'updated_at': updatedAt.toIso8601String(),
      };
}
