class CouponModel {
  CouponModel({
    required this.id,
    required this.code,
    required this.description,
    required this.discount,
    required this.minOrderAmount,
    required this.maxCouponAmount,
    required this.percentage,
    required this.expiresOn,
    required this.usageTimesRemaining,
    required this.isActive,
    required this.createdAt,
    required this.updatedAt,
    required this.currentUserUsageTimesRmaining,
  });

  final bool isActive;
  final int id;
  final int minOrderAmount;
  final int maxCouponAmount;
  final int percentage;
  final int usageTimesRemaining;
  final int currentUserUsageTimesRmaining;
  final double discount;
  final String code;
  final String? description;
  final String? createdAt;
  final String? updatedAt;
  final DateTime? expiresOn;

  CouponModel copyWith({
    bool? isActive,
    int? id,
    int? usageTimesRemaining,
    int? minOrderAmount,
    int? maxCouponAmount,
    int? percentage,
    int? currentUserUsageTimesRmaining,
    double? discount,
    String? code,
    String? description,
    String? createdAt,
    String? updatedAt,
    DateTime? expiresOn,
  }) =>
      CouponModel(
        id: id ?? this.id,
        code: code ?? this.code,
        description: description ?? this.description,
        discount: discount ?? this.discount,
        minOrderAmount: minOrderAmount ?? this.minOrderAmount,
        maxCouponAmount: maxCouponAmount ?? this.maxCouponAmount,
        percentage: percentage ?? this.percentage,
        expiresOn: expiresOn ?? this.expiresOn,
        usageTimesRemaining: usageTimesRemaining ?? this.usageTimesRemaining,
        isActive: isActive ?? this.isActive,
        createdAt: createdAt ?? this.createdAt,
        updatedAt: updatedAt ?? this.updatedAt,
        currentUserUsageTimesRmaining: currentUserUsageTimesRmaining ?? this.currentUserUsageTimesRmaining,
      );

  factory CouponModel.fromMap(Map<String, dynamic> json) => CouponModel(
        id: json["id"],
        code: json["code"],
        description: json["description"],
        discount: json["discount"]?.toDouble() ?? 0,
        minOrderAmount: json["min_order_amount"] ?? 0,
        maxCouponAmount: json["max_coupon_amount"] ?? 999999,
        percentage: json["percentage"] ?? 0,
        expiresOn: DateTime.tryParse(json["expires_on"]),
        usageTimesRemaining: json["times"] ?? 0,
        isActive: json["is_active"] == 1,
        createdAt: json["created_at"],
        updatedAt: json["updated_at"],
        currentUserUsageTimesRmaining: json["user_times"] ?? 0,
      );

  bool get isExpired {
    if (expiresOn == null) return false;
    final now = DateTime.now();
    return now.isAfter(expiresOn!);
  }

  Map<String, dynamic> toMap() => {
        "id": id,
        "code": code,
        "description": description,
        "discount": discount,
        "min_order_amount": minOrderAmount,
        "max_coupon_amount": maxCouponAmount,
        "percentage": percentage,
        "expires_on": expiresOn?.toIso8601String(),
        "times": usageTimesRemaining,
        "is_active": isActive,
        "created_at": createdAt,
        "updated_at": updatedAt,
        "user_times": currentUserUsageTimesRmaining,
      };
}
