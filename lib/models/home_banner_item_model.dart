class HomeBannerItemModel {
  HomeBannerItemModel({
    required this.id,
    required this.name,
    required this.desc,
    required this.image,
    required this.link,
    required this.categoryID,
    required this.isActive,
  });

  final bool isActive;
  final int id;
  final int? categoryID;
  final String? name;
  final String? desc;
  final String? link;
  final String image;

  HomeBannerItemModel copyWith({
    bool? isActive,
    int? id,
    int? categoryID,
    String? name,
    String? desc,
    String? image,
    String? link,
  }) =>
      HomeBannerItemModel(
        id: id ?? this.id,
        name: name ?? this.name,
        desc: desc ?? this.desc,
        image: image ?? this.image,
        link: link ?? this.link,
        categoryID: categoryID ?? this.categoryID,
        isActive: isActive ?? this.isActive,
      );

  factory HomeBannerItemModel.fromMap(Map<String, dynamic> json) => HomeBannerItemModel(
        id: json["id"],
        name: json["name"],
        desc: json["desc"],
        image: json["image"] ?? '',
        link: json["link"],
        categoryID: json["category_id"],
        isActive: json["is_active"] == 1,
      );

  Map<String, dynamic> toMap() => {
        "id": id,
        "name": name,
        "desc": desc,
        "image": image,
        "link": link,
        "category_id": categoryID,
        "is_active": isActive,
      };
}
