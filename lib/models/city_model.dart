import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';

class CityModel {
  CityModel({
    required this.id,
    required this.name,
    required this.image,
    required this.createdAt,
    required this.updatedAt,
  });

  final int id;
  final String? image;
  final String? createdAt;
  final String? updatedAt;
  final CityNameModel name;

  CityModel copyWith({
    int? id,
    CityNameModel? name,
    String? image,
    String? createdAt,
    String? updatedAt,
  }) =>
      CityModel(
        id: id ?? this.id,
        name: name ?? this.name,
        image: image ?? this.image,
        createdAt: createdAt ?? this.createdAt,
        updatedAt: updatedAt ?? this.updatedAt,
      );

  factory CityModel.fromMap(Map<String, dynamic> json) => CityModel(
        id: json["id"],
        name: CityNameModel.fromMap(json["name"]),
        image: json["image"],
        createdAt: json["created_at"],
        updatedAt: json["updated_at"],
      );

  String getName(BuildContext context) {
    final currentLocale = EasyLocalization.of(context)?.locale.languageCode;
    if (currentLocale == 'ar') {
      return name.ar;
    }
    return name.en;
  }

  Map<String, dynamic> toMap() => {
        "id": id,
        "name": name.toMap(),
        "image": image,
        "created_at": createdAt,
        "updated_at": updatedAt,
      };
}

class CityNameModel {
  CityNameModel({
    required this.ar,
    required this.en,
  });

  final String ar;
  final String en;

  CityNameModel copyWith({
    String? ar,
    String? en,
  }) =>
      CityNameModel(
        ar: ar ?? this.ar,
        en: en ?? this.en,
      );

  factory CityNameModel.fromMap(Map<String, dynamic> json) => CityNameModel(
        ar: json["ar"],
        en: json["en"],
      );

  Map<String, dynamic> toMap() => {
        "ar": ar,
        "en": en,
      };
}
