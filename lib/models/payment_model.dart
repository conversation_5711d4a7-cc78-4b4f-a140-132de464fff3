class PaymentModel {
  PaymentModel({
    required this.id,
    required this.orderNum,
    required this.userId,
    required this.categoryId,
    required this.carId,
    required this.addressId,
    required this.paymentType,
    required this.paymentStatus,
    required this.status,
    required this.couponId,
    required this.delegateId,
    required this.costBeforeDiscount,
    required this.costAfterDiscount,
    required this.discount,
    required this.finalCost,
    required this.lat,
    required this.lng,
    required this.address,
    required this.errorMessage,
    required this.isFree,
    required this.createdAt,
    required this.updatedAt,
  });

  final bool isFree;
  final int id;
  final int? userId;
  final int? categoryId;
  final int? carId;
  final int? addressId;
  final int? couponId;
  final int? delegateId;
  final int? costBeforeDiscount;
  final int? costAfterDiscount;
  final int? discount;
  final int? finalCost;
  final String? orderNum;
  final String? paymentType;
  final String? paymentStatus;
  final String? status;
  final String? lat;
  final String? lng;
  final String? address;
  final String? errorMessage;
  final DateTime createdAt;
  final DateTime updatedAt;

  PaymentModel copyWith({
    int? id,
    String? orderNum,
    int? userId,
    int? categoryId,
    int? carId,
    int? addressId,
    String? paymentType,
    String? paymentStatus,
    String? status,
    int? couponId,
    int? delegateId,
    int? costBeforeDiscount,
    int? costAfterDiscount,
    int? discount,
    int? finalCost,
    String? lat,
    String? lng,
    String? address,
    String? errorMessage,
    bool? isFree,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) =>
      PaymentModel(
        id: id ?? this.id,
        orderNum: orderNum ?? this.orderNum,
        userId: userId ?? this.userId,
        categoryId: categoryId ?? this.categoryId,
        carId: carId ?? this.carId,
        addressId: addressId ?? this.addressId,
        paymentType: paymentType ?? this.paymentType,
        paymentStatus: paymentStatus ?? this.paymentStatus,
        status: status ?? this.status,
        couponId: couponId ?? this.couponId,
        delegateId: delegateId ?? this.delegateId,
        costBeforeDiscount: costBeforeDiscount ?? this.costBeforeDiscount,
        costAfterDiscount: costAfterDiscount ?? this.costAfterDiscount,
        discount: discount ?? this.discount,
        finalCost: finalCost ?? this.finalCost,
        lat: lat ?? this.lat,
        lng: lng ?? this.lng,
        address: address ?? this.address,
        errorMessage: errorMessage ?? this.errorMessage,
        isFree: isFree ?? this.isFree,
        createdAt: createdAt ?? this.createdAt,
        updatedAt: updatedAt ?? this.updatedAt,
      );

  factory PaymentModel.fromMap(Map<String, dynamic> json) => PaymentModel(
        id: json["id"],
        orderNum: json["order_num"],
        userId: json["user_id"],
        categoryId: json["category_id"],
        carId: json["car_id"],
        addressId: json["address_id"],
        paymentType: json["payment_type"],
        paymentStatus: json["payment_status"],
        status: json["status"],
        couponId: json["coupon_id"],
        delegateId: json["delegate_id"],
        costBeforeDiscount: json["cost_before_discount"],
        costAfterDiscount: json["cost_after_discount"],
        discount: json["discount"],
        finalCost: json["final_cost"],
        lat: json["lat"],
        lng: json["lng"],
        address: json["address"],
        isFree: json["is_free"] == 1,
        createdAt: DateTime.tryParse(json["created_at"] ?? '') ?? DateTime.now(),
        updatedAt: DateTime.tryParse(json["updated_at"] ?? '') ?? DateTime.now(),
        errorMessage: json['message'],
      );

  Map<String, dynamic> toMap() => {
        "id": id,
        "order_num": orderNum,
        "user_id": userId,
        "category_id": categoryId,
        "car_id": carId,
        "address_id": addressId,
        "payment_type": paymentType,
        "payment_status": paymentStatus,
        "status": status,
        "coupon_id": couponId,
        "delegate_id": delegateId,
        "cost_before_discount": costBeforeDiscount,
        "cost_after_discount": costAfterDiscount,
        "discount": discount,
        "final_cost": finalCost,
        "lat": lat,
        "lng": lng,
        "address": address,
        "is_free": isFree,
        "created_at": createdAt.toIso8601String(),
        "updated_at": updatedAt.toIso8601String(),
      };
}
