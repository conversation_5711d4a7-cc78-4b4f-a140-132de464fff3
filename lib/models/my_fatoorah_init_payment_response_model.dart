import 'package:one_drop/constants.dart';

import 'my_fatoorah_payment_method_model.dart';

class MyFatoorahInitPaymentResponseModel {
  MyFatoorahInitPaymentResponseModel({
    required this.isSuccess,
    required this.message,
    required this.validationErrors,
    required this.paymentMethods,
  });

  final bool isSuccess;
  final String message;
  final String? validationErrors;
  final List<MyFatoorahPaymentMethodModel> paymentMethods;

  MyFatoorahInitPaymentResponseModel copyWith({
    bool? isSuccess,
    String? message,
    String? validationErrors,
    List<MyFatoorahPaymentMethodModel>? paymentMethods,
  }) =>
      MyFatoorahInitPaymentResponseModel(
        isSuccess: isSuccess ?? this.isSuccess,
        message: message ?? this.message,
        validationErrors: validationErrors ?? this.validationErrors,
        paymentMethods: paymentMethods ?? this.paymentMethods,
      );

  factory MyFatoorahInitPaymentResponseModel.fromMap(Map<String, dynamic> json) => MyFatoorahInitPaymentResponseModel(
        isSuccess: json["IsSuccess"] ?? false,
        message: json["Message"] ?? '',
        validationErrors: json["ValidationErrors"]?.toString(),
        paymentMethods: json['Data'] != null && json['Data']['PaymentMethods'] != null
            ? ((json['Data']['PaymentMethods'] as List?) ?? []).map<MyFatoorahPaymentMethodModel>((e) {
                return MyFatoorahPaymentMethodModel.fromMap(e);
              }).toList()
            : <MyFatoorahPaymentMethodModel>[],
      );

  MyFatoorahPaymentMethodModel? get getMasterVisaPaymentMethod {
    try {
      // return paymentMethods.firstWhere((element) {
      //   return element.paymentMethodEn == K.myFatooraVisaMasterDirectPayemntMethodName && element.isDirectPayment;
      // });
      return paymentMethods.firstWhere((element) {
        return element.paymentMethodEn == K.myFatooraVisaMasterPayemntMethodName && !element.isDirectPayment;
      });
    } catch (_) {
      return null;
    }
  }

  Map<String, dynamic> toMap() => {
        "IsSuccess": isSuccess,
        "Message": message,
        "ValidationErrors": validationErrors,
      };
}
