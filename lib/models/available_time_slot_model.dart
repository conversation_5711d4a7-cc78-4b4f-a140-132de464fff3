// models/available_time_slot_model.dart

class TimeSlot {
  final String hour;
  final String formattedHour;
  final int availableCount;

  TimeSlot({
    required this.hour,
    required this.formattedHour,
    required this.availableCount,
  });

  // للعرض للمستخدم، نستخدم الساعة المنسقة مباشرة
  String get displayHour {
    return formattedHour;
  }

  // احتفظنا بهذه الطريقة للتوافق الخلفي
  String get displayHourLegacy {
    final parts = hour.split(':');
    if (parts.length >= 2) {
      return "${parts[0]}:${parts[1]}";
    }
    return hour;
  }

  factory TimeSlot.fromMap(Map<String, dynamic> map) {
    return TimeSlot(
      hour: map['hour'] ?? '',
      formattedHour: map['formatted_hour'] ?? '', // إضافة الساعة المنسقة
      availableCount: map['available_count'] ?? 0,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'hour': hour,
      'formatted_hour': formattedHour,
      'available_count': availableCount,
    };
  }
}

class AvailableTimeSlotsResponse {
  final String date;
  final List<TimeSlot> availableSlots;

  AvailableTimeSlotsResponse({
    required this.date,
    required this.availableSlots,
  });

  factory AvailableTimeSlotsResponse.fromMap(Map<String, dynamic> map) {
    return AvailableTimeSlotsResponse(
      date: map['date'] ?? '',
      availableSlots: List<TimeSlot>.from(
        (map['available_slots'] as List? ?? []).map(
          (slot) => TimeSlot.fromMap(slot),
        ),
      ),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'date': date,
      'available_slots': availableSlots.map((slot) => slot.toMap()).toList(),
    };
  }
}
