import 'car_type_model.dart';
import 'order_category_item_model.dart';

class PriceModel {
  PriceModel({
    required this.id,
    required this.price,
    required this.durationFrom,
    required this.durationTo,
    required this.category,
    required this.car,
  });

  final int id;
  final int price;
  final String durationFrom;
  final String durationTo;
  final OrderCategoryItemModel? category;
  final CarTypeModel? car;

  PriceModel copyWith({
    int? id,
    int? price,
    String? durationFrom,
    String? durationTo,
    OrderCategoryItemModel? category,
    CarTypeModel? car,
  }) =>
      PriceModel(
        id: id ?? this.id,
        price: price ?? this.price,
        durationFrom: durationFrom ?? this.durationFrom,
        durationTo: durationTo ?? this.durationTo,
        category: category ?? this.category,
        car: car ?? this.car,
      );

  factory PriceModel.fromMap(Map<String, dynamic> json) => PriceModel(
        id: json["id"],
        price: json["price"],
        durationFrom: json["duration_from"],
        durationTo: json["duration_to"],
        category: json["category"] == null ? null : OrderCategoryItemModel.fromMap(json["category"]),
        car: json["car"] == null
            ? null
            : CarTypeModel.fromMap(
                json["car"],
                price: (json["price"] ?? 0)?.toDouble(),
                durationFrom: json['duration_from'] ?? '0.00',
                durationTo: json['duration_to'] ?? '0.00',
              ),
      );

  String get durationFromMinutes {
    return durationFrom.split('.')[0];
  }

  String get durationToMinutes {
    return durationTo.split('.')[0];
  }

  Map<String, dynamic> toMap() => {
        "id": id,
        "price": price,
        "duration_from": durationFrom,
        "duration_to": durationTo,
        "category": category?.toMap(),
        "car": car?.toMap(),
      };
}
