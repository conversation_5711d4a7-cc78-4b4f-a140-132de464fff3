import '../constants.dart';

class PackageItemModel {
  PackageItemModel({
    required this.id,
    required this.name,
    required this.desc,
    required this.image,
    required this.durationInDays,
    required this.washes,
    required this.price,
    required this.category,
    required this.car,
    required this.isActive,
    required this.isSubscribed,
    required this.remainingWashes,
  });

  final bool isActive;
  final bool isSubscribed;
  final int id;
  final int durationInDays;
  final int washes;
  final int remainingWashes;
  final double price;
  final String name;
  final String? desc;
  final String? image;
  final PackageCategoryModel? category;
  final PackageCarModel? car;

  PackageItemModel copyWith({
    bool? isActive,
    bool? isSubscribed,
    int? id,
    String? name,
    String? desc,
    String? image,
    int? durationInDays,
    int? washes,
    int? remainingWashes,
    double? price,
    PackageCategoryModel? category,
    PackageCarModel? car,
  }) =>
      PackageItemModel(
        isSubscribed: isSubscribed ?? this.isSubscribed,
        id: id ?? this.id,
        name: name ?? this.name,
        desc: desc ?? this.desc,
        image: image ?? this.image,
        durationInDays: durationInDays ?? this.durationInDays,
        washes: washes ?? this.washes,
        remainingWashes: remainingWashes ?? this.remainingWashes,
        price: price ?? this.price,
        category: category ?? this.category,
        car: car ?? this.car,
        isActive: isActive ?? this.isActive,
      );

  factory PackageItemModel.fromMap(Map<String, dynamic> json) => PackageItemModel(
        id: json["id"],
        name: json["name"] ?? '',
        desc: json["desc"],
        image: json["image"],
        durationInDays: json["duration"] ?? 0,
        washes: json["washes"] ?? 0,
        price: (json["price"] ?? 0).toDouble(),
        isSubscribed: json['is_subscribed'] == 'true',
        category: json["category"] == null ? null : PackageCategoryModel.fromMap(json["category"]),
        car: json["car"] == null ? null : PackageCarModel.fromMap(json["car"]),
        isActive: json["is_active"] == 1,
        remainingWashes: json['remain_washes'] ?? 0,
      );

  Map<String, dynamic> toMap() => {
        "id": id,
        "name": name,
        "desc": desc,
        "image": image,
        "duration": durationInDays,
        "washes": washes,
        "price": price,
        "category": category?.toMap(),
        "car": car?.toMap(),
        "is_active": isActive ? 1 : 0,
        "remain_washes": remainingWashes,
      };
}

class PackageCarModel {
  PackageCarModel({
    required this.id,
    required this.name,
    required this.desc,
    required this.image,
    required this.isActive,
    // required this.price,
    // required this.durationFrom,
    // required this.durationTo,
  });

  final bool isActive;
  final int id;
  final String name;
  final String desc;
  final String image;
  // final int price;
  // final String durationFrom;
  // final String durationTo;

  PackageCarModel copyWith({
    bool? isActive,
    int? id,
    String? name,
    String? desc,
    String? image,
    int? price,
    String? durationFrom,
    String? durationTo,
  }) =>
      PackageCarModel(
        id: id ?? this.id,
        name: name ?? this.name,
        desc: desc ?? this.desc,
        image: image ?? this.image,
        isActive: isActive ?? this.isActive,
        // price: price ?? this.price,
        // durationFrom: durationFrom ?? this.durationFrom,
        // durationTo: durationTo ?? this.durationTo,
      );

  factory PackageCarModel.fromMap(Map<String, dynamic> json) => PackageCarModel(
        id: json["id"],
        name: json["name"],
        desc: json["desc"],
        image: '${K.serverURL}/${json["image"]}',
        isActive: json["is_active"] == 1,
        // price: json["price"],
        // durationFrom: json["duration_from"],
        // durationTo: json["duration_to"],
      );

  Map<String, dynamic> toMap() => {
        "id": id,
        "name": name,
        "desc": desc,
        "image": image,
        "is_active": isActive,
        // "price": price,
        // "duration_from": durationFrom,
        // "duration_to": durationTo,
      };
}

class PackageCategoryModel {
  PackageCategoryModel({
    required this.id,
    required this.name,
    required this.desc,
    required this.image,
  });

  final int id;
  final String name;
  final String desc;
  final String image;

  PackageCategoryModel copyWith({
    int? id,
    String? name,
    String? desc,
    String? image,
  }) =>
      PackageCategoryModel(
        id: id ?? this.id,
        name: name ?? this.name,
        desc: desc ?? this.desc,
        image: image ?? this.image,
      );

  factory PackageCategoryModel.fromMap(Map<String, dynamic> json) => PackageCategoryModel(
        id: json["id"],
        name: json["name"],
        desc: json["desc"],
        image: json["image"],
      );

  Map<String, dynamic> toMap() => {
        "id": id,
        "name": name,
        "desc": desc,
        "image": image,
      };
}
