import 'package:one_drop/models/user_model.dart';

class UserAddressModel {
  UserAddressModel({
    required this.id,
    required this.name,
    required this.lat,
    required this.lng,
    required this.address,
    this.user,
  });

  final int? id;
  final String lat;
  final String lng;
  final String name;
  final String address;
  final UserModel? user;

  UserAddressModel copyWith({
    int? id,
    String? lat,
    String? lng,
    String? name,
    String? address,
    UserModel? user,
  }) =>
      UserAddressModel(
        id: id ?? this.id,
        name: name ?? this.name,
        lat: lat ?? this.lat,
        lng: lng ?? this.lng,
        address: address ?? this.address,
        user: user ?? this.user,
      );

  factory UserAddressModel.fromMap(Map<String, dynamic> json) => UserAddressModel(
        id: json["id"],
        name: json["name"],
        lat: json["lat"].toString(),
        lng: json["lng"].toString(),
        address: json["address"],
        user: json['user'] == null ? null : UserModel.fromMap(json['user']),
      );

  Map<String, dynamic> toMap() => {
        "id": id,
        "name": name,
        "lat": lat,
        "lng": lng,
        "address": address,
      };
}
