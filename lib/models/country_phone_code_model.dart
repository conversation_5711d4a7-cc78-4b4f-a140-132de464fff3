import 'package:flutter/material.dart';

import 'searchable_item_model.dart';

class CountryPhoneCodeModel implements SearchableItemModel {
  final String name;
  final String code;

  const CountryPhoneCodeModel({
    required this.name,
    required this.code,
  });

  @override
  String get getID => code;

  @override
  String getName(BuildContext? context) {
    return '$code $name';
  }
}

const countryPhoneCodes = <CountryPhoneCodeModel>[
  CountryPhoneCodeModel(code: '+966', name: 'المملكة العربية السعودية'),
];
