import 'package:one_drop/models/brand.dart';

class Product {
  final int id;
  final String name;
  final String slug;
  final String description;
  final String image;
  final double price;
  final int quantity;
  final bool isActive;
  final Brand brand;
  final DateTime createdAt;
  final DateTime updatedAt;

  Product({
    required this.id,
    required this.name,
    required this.slug,
    required this.description,
    required this.image,
    required this.price,
    required this.quantity,
    required this.isActive,
    required this.brand,
    required this.createdAt,
    required this.updatedAt,
  });

  factory Product.fromJson(Map<String, dynamic>? json) {
    if (json == null) {
      print('❌ Product JSON is null');
      return Product.empty();
    }
    
    // Extract the data field from the response
    final data = json['data'] as Map<String, dynamic>? ?? json;
    
    print('📦 Parsing product data: $data');
    final product = Product(
      id: int.tryParse(data['id']?.toString() ?? '') ?? 0,
      name: data['name']?.toString() ?? '',
      slug: data['slug']?.toString() ?? '',
      description: data['description']?.toString() ?? '',
      image: data['image']?.toString() ?? '',
      price: double.tryParse(data['price']?.toString() ?? '0') ?? 0,
      quantity: int.tryParse(data['quantity']?.toString() ?? '') ?? 0,
      isActive: data['is_active'] == 1,
      brand: Brand.fromJson(data['brand'] as Map<String, dynamic>?),
      createdAt: DateTime.tryParse(data['created_at'] ?? '') ?? DateTime.now(),
      updatedAt: DateTime.tryParse(data['updated_at'] ?? '') ?? DateTime.now(),
    );
    
    print('✅ Product parsed:');
    print('- ID: ${product.id}');
    print('- Name: ${product.name}');
    print('- Price: ${product.price}');
    print('- Image: ${product.image}');
    
    return product;
  }

  factory Product.empty() {
    return Product(
      id: 0,
      name: '',
      slug: '',
      description: '',
      image: '',
      price: 0,
      quantity: 0,
      isActive: false,
      brand: Brand.empty(),
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'slug': slug,
      'description': description,
      'image': image,
      'price': price,
      'quantity': quantity,
      'is_active': isActive ? 1 : 0,
      'brand': brand.toJson(),
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  Product copyWith({
    int? id,
    String? name,
    String? slug,
    String? description,
    String? image,
    double? price,
    int? quantity,
    bool? isActive,
    Brand? brand,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Product(
      id: id ?? this.id,
      name: name ?? this.name,
      slug: slug ?? this.slug,
      description: description ?? this.description,
      image: image ?? this.image,
      price: price ?? this.price,
      quantity: quantity ?? this.quantity,
      isActive: isActive ?? this.isActive,
      brand: brand ?? this.brand,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}
