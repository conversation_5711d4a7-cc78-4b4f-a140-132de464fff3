import 'package:one_drop/models/home_banner_item_model.dart';
import 'package:one_drop/models/order_category_item_model.dart';
import 'package:one_drop/models/package_item_model.dart';

class HomeTabModel {
  final List<HomeBannerItemModel> banners;
  final List<OrderCategoryItemModel> categories;
  final List<PackageItemModel> packages;

  HomeTabModel({
    required this.banners,
    required this.categories,
    required this.packages,
  });

  HomeTabModel.fromJson(Map json)
      : banners = ((json['banners'] as List?) ?? []).map<HomeBannerItemModel>((e) {
          return HomeBannerItemModel.fromMap(e);
        }).toList(),
        categories = ((json['categories'] as List?) ?? []).map<OrderCategoryItemModel>((e) {
          return OrderCategoryItemModel.fromMap(e);
        }).toList(),
        packages = ((json['packages'] as List?) ?? []).map<PackageItemModel>((e) {
          return PackageItemModel.fromMap(e);
        }).toList();
}
