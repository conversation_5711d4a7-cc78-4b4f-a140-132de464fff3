import 'package:one_drop/models/order_model.dart';

class NotificationModel {
  NotificationModel({
    required this.id,
    required this.type,
    required this.title,
    required this.body,
    required this.orderId,
    required this.delegateId,
    required this.providerId,
    required this.status,
    required this.time,
    required this.order,
  });

  final String id;
  final String type;
  final String title;
  final String body;
  final String? orderId;
  final String? delegateId;
  final String? providerId;
  final String status;
  final String time;
  final OrderModel? order;

  NotificationModel copyWith({
    String? id,
    String? type,
    String? title,
    String? body,
    String? orderId,
    String? delegateId,
    String? providerId,
    String? status,
    String? time,
    OrderModel? order,
  }) =>
      NotificationModel(
        id: id ?? this.id,
        type: type ?? this.type,
        title: title ?? this.title,
        body: body ?? this.body,
        orderId: orderId ?? this.orderId,
        delegateId: delegateId ?? this.delegateId,
        providerId: providerId ?? this.providerId,
        status: status ?? this.status,
        time: time ?? this.time,
        order: order ?? this.order,
      );

  factory NotificationModel.fromMap(Map<String, dynamic> json) => NotificationModel(
        id: json["id"],
        type: json["type"] ?? '',
        title: json["title"] ?? '',
        body: json["body"] ?? '',
        orderId: json["order_id"],
        delegateId: json["delegate_id"],
        providerId: json["provider_id"],
        status: json["status"] ?? '',
        time: json["time"] ?? '',
        order: json['data'] != null && json['data']['id'] != null && json['data']['user'] != null ? OrderModel.fromMap(json['data']) : null,
      );

  Map<String, dynamic> toMap() => {
        "id": id,
        "type": type,
        "title": title,
        "body": body,
        "order_id": orderId,
        "delegate_id": delegateId,
        "provider_id": providerId,
        "status": status,
        "time": time,
        "data": order?.toMap(),
      };
}
