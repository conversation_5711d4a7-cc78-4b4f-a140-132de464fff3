class OrderCategoryItemModel {
  OrderCategoryItemModel({
    required this.id,
    required this.name,
    required this.desc,
    required this.image,
    required this.isActive,
  });

  final bool isActive;
  final int id;
  final String name;
  final String desc;
  final String image;

  OrderCategoryItemModel copyWith({
    int? id,
    String? name,
    String? desc,
    String? image,
    bool? isActive,
  }) =>
      OrderCategoryItemModel(
        id: id ?? this.id,
        name: name ?? this.name,
        desc: desc ?? this.desc,
        image: image ?? this.image,
        isActive: isActive ?? this.isActive,
      );

  factory OrderCategoryItemModel.fromMap(Map<String, dynamic> json) => OrderCategoryItemModel(
        id: json["id"],
        name: json["name"] ?? '',
        desc: json["desc"] ?? '',
        image: json["image"] ?? '',
        isActive: json["is_active"] == 1,
      );

  Map<String, dynamic> toMap() => {
        "id": id,
        "name": name,
        "desc": desc,
        "image": image,
        "is_active": isActive,
      };
}
