import 'car_type_model.dart';
import 'coupon_model.dart';
import 'delegate_model.dart';
import 'order_category_item_model.dart';
import 'user_model.dart';

class OrderModel {
  OrderModel({
    required this.id,
    required this.address,
    required this.paymentType,
    required this.paymentStatus,
    required this.status,
    required this.coupon,
    required this.costBeforeDiscount,
    required this.costAfterDiscount,
    required this.discount,
    required this.finalCost,
    required this.lat,
    required this.lng,
    required this.category,
    required this.carType,
    required this.user,
    required this.delegate,
    required this.orderNumber,
    required this.errorMessage,
  });

  final int id;
  final double costBeforeDiscount;
  final double costAfterDiscount;
  final double finalCost;
  final double? discount;
  final String address;
  final String lat;
  final String lng;
  final String? paymentType;
  final String? paymentStatus;
  final String? status;
  final String? orderNumber;
  final String? errorMessage;
  final CouponModel? coupon;
  final OrderCategoryItemModel? category;
  final CarTypeModel? carType;
  final UserModel? user;
  final DelegateModel? delegate;

  OrderModel copyWith({
    int? id,
    String? address,
    String? paymentType,
    String? paymentStatus,
    String? status,
    String? orderNumber,
    String? errorMessage,
    int? delegateId,
    double? costBeforeDiscount,
    double? costAfterDiscount,
    double? discount,
    double? finalCost,
    String? lat,
    String? lng,
    CouponModel? coupon,
    OrderCategoryItemModel? category,
    CarTypeModel? carType,
    UserModel? user,
    DelegateModel? delegate,
  }) =>
      OrderModel(
        id: id ?? this.id,
        address: address ?? this.address,
        paymentType: paymentType ?? this.paymentType,
        paymentStatus: paymentStatus ?? this.paymentStatus,
        status: status ?? this.status,
        coupon: coupon ?? this.coupon,
        costBeforeDiscount: costBeforeDiscount ?? this.costBeforeDiscount,
        costAfterDiscount: costAfterDiscount ?? this.costAfterDiscount,
        discount: discount ?? this.discount,
        finalCost: finalCost ?? this.finalCost,
        lat: lat ?? this.lat,
        lng: lng ?? this.lng,
        category: category ?? this.category,
        carType: carType ?? this.carType,
        user: user ?? this.user,
        delegate: delegate ?? this.delegate,
        orderNumber: orderNumber ?? this.orderNumber,
        errorMessage: errorMessage ?? this.errorMessage,
      );

  factory OrderModel.fromMap(Map<String, dynamic> json) => OrderModel(
        id: json["id"],
        address: json["address"],
        paymentType: json["payment_type"],
        paymentStatus: json["payment_status"] ?? 'pending',
        status: json["status"] ?? 'pending',
        costBeforeDiscount: (json["cost_before_discount"] ?? 0).toDouble(),
        costAfterDiscount: (json["cost_after_discount"] ?? 0).toDouble(),
        discount: (json["discount"] ?? 0).toDouble(),
        finalCost: (json["final_cost"] ?? 0).toDouble(),
        lat: json["lat"],
        lng: json["lng"],
        coupon: json["coupon"] == null ? null : CouponModel.fromMap(json["coupon"]),
        category: json['category'] == null ? null : OrderCategoryItemModel.fromMap(json['category']),
        carType: json['car'] == null
            ? null
            : CarTypeModel.fromMap(
                json['car'],
                price: json['price'] ?? 0,
                durationFrom: json['duration_from'] ?? '0.00',
                durationTo: json['duration_to'] ?? '0.00',
              ),
        user: json['user'] == null ? null : UserModel.fromMap(json['user']),
        delegate: json['delegate_id'] == null ? null : DelegateModel.fromMap(json['delegate_id']),
        orderNumber: json['order_num']?.toString(),
        errorMessage: json['message'],
      );

  double get calculateOrderTotalPrice {
    final discountAmount = coupon?.discount ?? 0;
    final discountPercentage = coupon?.percentage ?? 0;
    final carPrice = carType?.price ?? 0;
    double total;
    if (discountPercentage > 0) {
      final amount = carPrice * (discountPercentage / 100);
      total = carPrice - amount;
    } else if (discountAmount > 0) {
      total = carPrice - discountAmount;
    } else {
      total = carPrice;
    }
    return total;
  }

  Map<String, dynamic> toMap() => {
        "id": id,
        "address": address,
        "payment_type": paymentType,
        "payment_status": paymentStatus,
        "status": status,
        "coupon": coupon,
        "delegate_id": delegate?.toMap(),
        "cost_before_discount": costBeforeDiscount,
        "cost_after_discount": costAfterDiscount,
        "discount": discount,
        "final_cost": finalCost,
        "lat": lat,
        "lng": lng,
        "category": category?.toMap(),
        "carType": carType?.toMap(),
        "user": user?.toMap(),
        "order_num": orderNumber,
      };
}
