import 'delegate_model.dart';

class UserModel {
  UserModel({
    this.id = 0,
    required this.name,
    required this.email,
    required this.phoneCountryCode,
    required this.phone,
    required this.fullPhone,
    required this.image,
    required this.lang,
    this.isNotify = 0,
    required this.token,
    required this.cityId,
    required this.cityName,
    required this.delegate,
  });

  final int? id;
  final int? isNotify;
  final int? cityId;
  final String phoneCountryCode;
  final String phone;
  final String fullPhone;
  final String lang;
  final String? image;
  final String? name;
  final String? email;
  final String? token;
  final String? cityName;
  final DelegateModel? delegate;

  UserModel copyWith({
    int? id,
    String? name,
    String? email,
    String? phoneCountryCode,
    String? phone,
    String? fullPhone,
    String? image,
    String? lang,
    int? isNotify,
    String? token,
    int? cityId,
    String? cityName,
    DelegateModel? delegate,
  }) =>
      UserModel(
        id: id ?? this.id,
        name: name ?? this.name,
        email: email ?? this.email,
        phoneCountryCode: phoneCountryCode ?? this.phoneCountryCode,
        phone: phone ?? this.phone,
        fullPhone: fullPhone ?? this.fullPhone,
        image: image ?? this.image,
        lang: lang ?? this.lang,
        isNotify: isNotify ?? this.isNotify,
        token: token ?? this.token,
        cityId: cityId ?? this.cityId,
        cityName: cityName ?? this.cityName,
        delegate: delegate ?? this.delegate,
      );

  factory UserModel.fromMap(Map<String, dynamic> json) => UserModel(
        id: json["id"] as int? ?? 0,
        name: json["name"]?.toString() ?? '',
        email: json["email"]?.toString() ?? '',
        phoneCountryCode: json["country_code"]?.toString() ?? '',
        phone: json["phone"]?.toString() ?? '',
        fullPhone: json["full_phone"]?.toString() ?? '',
        image: json["image"]?.toString(),
        lang: json["lang"]?.toString() ?? 'ar',
        isNotify: json["is_notify"] as int? ?? 0,
        token: json["token"]?.toString(),
        cityId: json["city_id"] as int?,
        cityName: json["city_name"]?.toString() ?? '',
        delegate: json['delegate'] == null ? null : DelegateModel.fromMap(json['delegate'] as Map<String, dynamic>),
      );

  Map<String, dynamic> toMap() => {
        "id": id,
        "name": name,
        "email": email,
        "country_code": phoneCountryCode,
        "phone": phone,
        "full_phone": fullPhone,
        "image": image,
        "lang": lang,
        "is_notify": isNotify,
        "token": token,
        "city_id": cityId,
        "city_name": cityName,
        "delegate": delegate?.toMap(),
      };
}
