import 'package:one_drop/constants.dart';

class PaymentCardModel {
  final String cardNumber;
  final String expiryMonth;
  final String expiryYear;
  final String cvv;

  PaymentCardModel({
    required this.cardNumber,
    required this.expiryMonth,
    required this.expiryYear,
    required this.cvv,
  });

  factory PaymentCardModel.fromJson(Map json) {
    return PaymentCardModel(
      cardNumber: json['card_number'],
      cvv: json['cvv'],
      expiryMonth: json['expiry_month'],
      expiryYear: json['expiry_year'],
    );
  }

  Map<String, String> toJson() {
    return {
      'card_number': cardNumber,
      'cvv': cvv,
      'expiry_month': expiryMonth,
      'expiry_year': expiryYear,
    };
  }

  String? get imagePath {
    if (cardNumber.startsWith('4')) return 'assets/images/32.png';
    if (cardNumber.startsWith('5')) return 'assets/images/33.png';
    return null;
  }

  String get cardType {
    if (cardNumber.startsWith('4')) return K.paymentTypeVisa;
    if (cardNumber.startsWith('5')) return K.paymentTypeMaster;
    return K.paymentTypeMada;
  }

  String get concealedCardNumber {
    return '**** ${cardNumber.substring(12)}';
  }
}
