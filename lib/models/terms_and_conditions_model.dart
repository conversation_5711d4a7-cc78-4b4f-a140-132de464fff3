import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';

class TermsAndConditionsModel {
  final String ar;
  final String en;

  TermsAndConditionsModel({
    required this.ar,
    required this.en,
  });

  factory TermsAndConditionsModel.fromJson(Map json) {
    return TermsAndConditionsModel(
      ar: json['terms_ar'] ?? '',
      en: json['terms_en'] ?? '',
    );
  }

  String getLocalizedTerms(BuildContext context) {
    final currentLocale = EasyLocalization.of(context)?.locale.languageCode;
    if (currentLocale == 'ar') {
      return ar;
    }
    return en;
  }
}
