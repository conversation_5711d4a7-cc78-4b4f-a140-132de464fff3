import 'ap_payment_summary_item_model.dart';

class APPaymentRequestModel {
  final String merchantID;
  final String countryCode;
  final String currencyCode;
  final List<APPaymentSummaryItemModel> paymentSummaryItems;

  APPaymentRequestModel({
    required this.merchantID,
    required this.countryCode,
    required this.currencyCode,
    required this.paymentSummaryItems,
  });

  Map<String, dynamic> toMap() {
    return {
      'merchantID': merchantID,
      'countryCode': countryCode,
      'currencyCode': currencyCode,
      'paymentSummaryItems': paymentSummaryItems.map<Map<String, dynamic>>((e) => e.toMap()).toList()
    };
  }
}
