class SocialLinksModel {
  SocialLinksModel({
    required this.twitter,
    required this.facebook,
    required this.instagram,
  });

  final Twitter? twitter;
  final Facebook? facebook;
  final Instagram? instagram;

  SocialLinksModel copyWith({
    Twitter? twitter,
    Facebook? facebook,
    Instagram? instagram,
  }) =>
      SocialLinksModel(
        twitter: twitter ?? this.twitter,
        facebook: facebook ?? this.facebook,
        instagram: instagram ?? this.instagram,
      );

  factory SocialLinksModel.fromMap(Map<String, dynamic> json) => SocialLinksModel(
        twitter: Twitter.fromMap(json["twitter"]),
        facebook: Facebook.fromMap(json["facebook"]),
        instagram: Instagram.fromMap(json["instagram"]),
      );

  Map<String, dynamic> toMap() => {
        "twitter": twitter?.toMap(),
        "facebook": facebook?.toMap(),
        "instagram": instagram?.toMap(),
      };
}

class Facebook {
  Facebook({
    required this.facebook,
  });

  final String? facebook;

  Facebook copyWith({
    String? facebook,
  }) =>
      Facebook(
        facebook: facebook ?? this.facebook,
      );

  factory Facebook.fromMap(Map<String, dynamic> json) => Facebook(
        facebook: json["facebook"],
      );

  Map<String, dynamic> toMap() => {
        "facebook": facebook,
      };
}

class Instagram {
  Instagram({
    required this.instagram,
  });

  final String? instagram;

  Instagram copyWith({
    String? instagram,
  }) =>
      Instagram(
        instagram: instagram ?? this.instagram,
      );

  factory Instagram.fromMap(Map<String, dynamic> json) => Instagram(
        instagram: json["instagram"],
      );

  Map<String, dynamic> toMap() => {
        "instagram": instagram,
      };
}

class Twitter {
  Twitter({
    required this.twitter,
  });

  final String? twitter;

  Twitter copyWith({
    String? twitter,
  }) =>
      Twitter(
        twitter: twitter ?? this.twitter,
      );

  factory Twitter.fromMap(Map<String, dynamic> json) => Twitter(
        twitter: json["twitter"],
      );

  Map<String, dynamic> toMap() => {
        "twitter": twitter,
      };
}
