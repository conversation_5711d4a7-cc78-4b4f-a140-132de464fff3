class CarTypeModel {
  CarTypeModel({
    required this.id,
    required this.name,
    required this.desc,
    required this.image,
    required this.price,
    required this.durationFrom,
    required this.durationTo,
  });

  final int id;
  final double price;
  final String name;
  final String image;
  final String durationFrom;
  final String durationTo;
  final String? desc;

  CarTypeModel copyWith({
    int? id,
    double? price,
    String? name,
    String? desc,
    String? image,
    String? durationFrom,
    String? durationTo,
  }) =>
      CarTypeModel(
        id: id ?? this.id,
        name: name ?? this.name,
        desc: desc ?? this.desc,
        image: image ?? this.image,
        price: price ?? this.price,
        durationFrom: durationFrom ?? this.durationFrom,
        durationTo: durationTo ?? this.durationTo,
      );

  factory CarTypeModel.fromMap(
    Map<String, dynamic> json, {
    double? price,
    required String durationTo,
    required String durationFrom,
  }) =>
      CarTypeModel(
        id: json["id"],
        name: json["name"] ?? '',
        desc: json["desc"] ?? '',
        image: json["image"] ?? '',
        price: price ?? 0,
        durationFrom: durationFrom,
        durationTo: durationTo,
      );

  String get durationFromMinutes {
    return durationFrom.split('.')[0];
  }

  String get durationToMinutes {
    return durationTo.split('.')[0];
  }

  Map<String, dynamic> toMap() => {
        "id": id,
        "name": name,
        "desc": desc,
        "image": image,
        "price": price,
        "duration_from": durationFrom,
        "duration_to": durationTo,
      };
}
