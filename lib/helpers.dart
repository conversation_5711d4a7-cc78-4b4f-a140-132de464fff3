import 'dart:io';

import 'package:flutter/material.dart';
import 'package:one_drop/utils/format_utils.dart';
import 'package:one_drop/utils/image_utils.dart';
import 'package:one_drop/utils/notification_utils.dart';
import 'package:one_drop/utils/validation_utils.dart';

/// A facade class that provides easy access to all utility functions
/// This class maintains backward compatibility while using the new utility classes
@Deprecated(
    'Use specific utility classes instead: ValidationUtils, NotificationUtils, FormatUtils, ImageUtils')
mixin Helpers {
  static Future<File?> Function(BuildContext) showPickImageBottomSheet =
      ImageUtils.showPickImageBottomSheet;
  static String? Function(String?) validateName = ValidationUtils.validateName;
  static String Function(double) humanizeNumberWithFraction =
      FormatUtils.humanizeNumberWithFraction;
  static String Function(double) getNumbersAfterDecimalPoint =
      FormatUtils.getNumbersAfterDecimalPoint;
  static Duration Function(String) parseDuration = FormatUtils.parseDuration;
  static String Function(Duration) formatDuration = FormatUtils.formatDuration;
  static bool Function(int) isCapitalLetter = ValidationUtils.isCapitalLetter;
  static bool Function(int) isSmallLetter = ValidationUtils.isSmallLetter;
  static bool Function(int) isAlphabiticLetter =
      ValidationUtils.isAlphabiticLetter;
  static bool Function(int) isDigit = ValidationUtils.isDigit;
  static String? Function(String?) validateEmail =
      ValidationUtils.validateEmail;
  static void Function({String text, Duration? duration, Widget? title})
      showSuccessNotification = NotificationUtils.showSuccess;
  static void Function([String?]) showErrorNotification =
      NotificationUtils.showError;
  static void Function(String?) showNotification = NotificationUtils.show;
}
