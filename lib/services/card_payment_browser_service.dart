import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';

import '../constants.dart';

class CardPaymentBrowserService extends InAppBrowser {
  final _handledPayments = <String, bool>{};
  final BuildContext context;
  final void Function(PaymentCompletedModel) onPaymentCompleted;
  bool _isClosing = false;

  CardPaymentBrowserService({
    required this.context,
    required this.onPaymentCompleted,
  });

  @override
  Future onBrowserCreated() async {
    log("🌐 Browser Created!");
    _isClosing = false;
  }

  @override
  Future onLoadStart(Uri? url) async {
    if (_isClosing) return;
    log("🌐 Started loading: $url");
    
    if (url != null && url.toString().contains(K.paymentCallbackURL)) {
      await _handlePaymentCompleted(url);
    }
  }

  @override
  Future onLoadStop(Uri? url) async {
    if (_isClosing) return;
    log("🌐 Finished loading: $url");
  }

  @override
  void onLoadError(url, code, message) {
    if (_isClosing) return;
    log("❌ Load error: $url - Code: $code - Message: $message");
    
    if (url != null && url.toString().contains(K.paymentCallbackURL)) {
      _handlePaymentCompleted(Uri.parse(url.toString()));
    }
  }

  @override
  void onProgressChanged(progress) {
    if (_isClosing) return;
    log("🌐 Loading progress: $progress%");
  }

  @override
  Future<void> close() async {
    if (_isClosing) return;
    _isClosing = true;
    log("🌐 Closing browser...");
    
    try {
      // Force platform view disposal
      if (isOpened()) {
        await Future.delayed(const Duration(milliseconds: 100));
        await super.close();
      }
    } catch (e) {
      log("❌ Error closing browser: $e");
    } finally {
      _isClosing = false;
    }
  }

  @override
  void onExit() {
    log("🌐 Browser closed!");
    _isClosing = false;
  }

  Future<void> _handlePaymentCompleted(Uri url) async {
    final success = url.queryParameters[K.paymentStatusURLParamName] == 'true';
    final transactionID = url.queryParameters['paymentId'];
    
    if (_handledPayments[transactionID] ?? false) {
      log("🌐 Payment $transactionID already handled");
      return;
    }

    log("🌐 Processing payment completion - ID: $transactionID, Success: $success");
    
    if (transactionID != null) {
      _handledPayments[transactionID] = true;
    }

    // Close browser first
    await close();
    
    // Then notify completion
    onPaymentCompleted(
      PaymentCompletedModel(
        success: success,
        transactionID: transactionID,
      ),
    );
  }
}

class PaymentCompletedModel {
  final bool success;
  final String? transactionID;

  PaymentCompletedModel({
    required this.success,
    required this.transactionID,
  });
}
