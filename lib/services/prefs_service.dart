import 'dart:convert';
import 'dart:developer' as developer;

import 'package:flutter/material.dart';
import 'package:one_drop/constants.dart';
import 'package:one_drop/models/payment_card_model.dart';
import 'package:one_drop/providers/user_provider.dart';
import 'package:one_drop/services/service_locator.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:provider/provider.dart';

class PrefsService {
  final SharedPreferences _prefs;

  PrefsService({required SharedPreferences prefs}) : _prefs = prefs;

  Future<bool> deleteCard(PaymentCardModel card) async {
    developer.log(' Attempting to delete card: ${card.cardNumber}', name: 'PrefsService');
    final allCard = await getSavedPaymentCards();
    if (allCard?.isEmpty ?? true) {
      developer.log(' No cards found to delete', name: 'PrefsService');
      return true;
    }
    final updatedCardsList = allCard!.where((element) => element.cardNumber != card.cardNumber);
    final jsonData = updatedCardsList.map((e) => e.toJson()).toList();
    final result = await _prefs.setString(savedCardsKey, json.encode(jsonData));
    developer.log(' Card deletion ${result ? 'successful' : 'failed'}: ${card.cardNumber}', name: 'PrefsService');
    return result;
  }

  String get savedCardsKey {
    final context = sl.get<GlobalKey<NavigatorState>>().currentContext;
    final user = context?.read<UserProvider>().userModel;
    final savedCardsKey = '${user?.id}-${K.savedPaymentCardsKey}';
    return savedCardsKey;
  }

  Future<bool> savePaymentCard(PaymentCardModel card) async {
    developer.log(' Attempting to save card: ${card.cardNumber}', name: 'PrefsService');
    final savedCards = await getSavedPaymentCards();
    if (savedCards?.any((element) => element.cardNumber == card.cardNumber) ?? false) {
      developer.log(' Card already exists: ${card.cardNumber}', name: 'PrefsService');
      return true;
    }
    try {
      final cardsJson = _prefs.getString(savedCardsKey) ?? '[]';
      final List cardsDataList = json.decode(cardsJson);
      cardsDataList.add(card.toJson());
      final result = await _prefs.setString(savedCardsKey, json.encode(cardsDataList));
      developer.log(' Card saved ${result ? 'successfully' : 'failed'}: ${card.cardNumber}', name: 'PrefsService');
      return result;
    } catch (e) {
      developer.log(' Save payment card error', name: 'PrefsService');
      developer.log(e.toString(), name: 'PrefsService');
      return false;
    }
  }

  Future<List<PaymentCardModel>?> getSavedPaymentCards() async {
    developer.log(' Fetching saved payment cards', name: 'PrefsService');
    try {
      final cardsJson = _prefs.getString(savedCardsKey) ?? '[]';
      final List cardsDataList = json.decode(cardsJson);
      final cards = cardsDataList.map<PaymentCardModel>((e) {
        return PaymentCardModel.fromJson(e);
      }).toList();
      developer.log(' Retrieved ${cards.length} saved cards', name: 'PrefsService');
      return cards;
    } catch (e) {
      developer.log(' Get saved payment cards error', name: 'PrefsService');
      developer.log(e.toString(), name: 'PrefsService');
      return null;
    }
  }

  Future<bool> setBool(String key, bool value) {
    developer.log(' Setting bool preference: $key = $value', name: 'PrefsService');
    return _prefs.setBool(key, value);
  }

  bool? getBool(String key) {
    final value = _prefs.getBool(key);
    developer.log(' Getting bool preference: $key = $value', name: 'PrefsService');
    return value;
  }

  int? getInt(String key) {
    final value = _prefs.getInt(key);
    developer.log(' Getting int preference: $key = $value', name: 'PrefsService');
    return value;
  }

  String? getString(String key) {
    final value = _prefs.getString(key);
    developer.log(' Getting string preference: $key = $value', name: 'PrefsService');
    return value;
  }

  Future<bool> remove(String key) {
    developer.log(' Removing preference: $key', name: 'PrefsService');
    return _prefs.remove(key);
  }

  Future<bool> setString(String key, String value) {
    developer.log(' Setting string preference: $key = $value', name: 'PrefsService');
    return _prefs.setString(key, value);
  }

  Future<bool> setInt(String key, int? value) {
    developer.log(' Setting int preference: $key = $value', name: 'PrefsService');
    if (value == null) return Future.value(false);
    return _prefs.setInt(key, value);
  }

  Future<bool> clear() {
    developer.log(' Clearing all preferences', name: 'PrefsService');
    return _prefs.clear();
  }

  Future<bool> setAuthToken(String token) async {
    return await setString('auth_token', token);
  }

  Future<String?> getAuthToken() async {
    return getString('auth_token');
  }

  Future<bool> removeAuthToken() async {
    return await remove('auth_token');
  }
}
