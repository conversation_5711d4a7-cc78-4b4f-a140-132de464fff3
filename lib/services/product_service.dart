import 'package:one_drop/models/product.dart';
import 'package:one_drop/services/network_service.dart';
import 'package:one_drop/services/service_locator.dart';

class ProductService {
  final NetworkService _networkService = sl.get<NetworkService>();

  void _logError(String method, dynamic error, StackTrace stackTrace) {
    print('❌ Product Service Error in $method: $error\n$stackTrace');
  }

  /// Get product details by ID
  Future<Product> getProduct(int productId) async {
    try {
      final response = await _networkService.request<Map<String, dynamic>>(
        path: '/products/$productId',
        method: 'GET',
      );

      if (response == null) {
        throw NetworkException('Failed to fetch product - null response');
      }

      return Product.fromJson(response);
    } catch (e, stackTrace) {
      _logError('getProduct', e, stackTrace);
      rethrow;
    }
  }
}
