import 'dart:async';
import 'dart:developer' as devtools show log;
import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:one_drop/services/prefs_service.dart';
import 'package:one_drop/services/service_locator.dart';

class NetworkException implements Exception {
  final String message;
  final int? statusCode;
  final StackTrace? stackTrace;

  NetworkException(this.message, [this.statusCode, this.stackTrace]);

  @override
  String toString() =>
      'NetworkException: $message (Status Code: $statusCode)\nStack Trace: $stackTrace';
}

class NetworkService {
  final Dio _dio;

  // final UserProvider _userProvider;
  final Map<String, dynamic> _cache = {};

  NetworkService(
    this._dio,
  ) {
    _dio.interceptors.add(
      InterceptorsWrapper(
        onRequest: _logRequest,
        onResponse: _logResponse,
        onError: _logError,
      ),
    );
  }

  // Comprehensive request logging
  Future<void> _logRequest(
    RequestOptions options,
    RequestInterceptorHandler handler,
  ) async {
    try {
      final headers = await _prepareHeaders();
      debugPrint('Headers: $headers');
      debugPrint(
          'Headers: ===============================headers========================================================');
      debugPrint('options.headers: ${options.headers}');
      options.headers.addAll(headers);

      devtools.log(
        '🚀 Network Request',
        name: 'NetworkService',
        error: {
          'method': options.method,
          'path': options.path,
          'headers': options.headers,
          'queryParams': options.queryParameters,
          'data': options.data,
        },
      );

      handler.next(options);
    } catch (e, stackTrace) {
      devtools.log(
        '❌ Request Preparation Error',
        name: 'NetworkService',
        error: e,
        stackTrace: stackTrace,
      );
      handler.reject(
        DioException(
          requestOptions: options,
          error: e,
          stackTrace: stackTrace,
        ),
      );
    }
  }

  // Comprehensive response logging
  Future<void> _logResponse(
    Response response,
    ResponseInterceptorHandler handler,
  ) async {
    devtools.log(
      '✅ Network Response',
      name: 'NetworkService',
      error: {
        'method': response.requestOptions.method,
        'path': response.requestOptions.path,
        'statusCode': response.statusCode,
        'data': response.data,
      },
    );
    handler.next(response);
  }

  // Comprehensive error logging
  Future<void> _logError(
    DioException err,
    ErrorInterceptorHandler handler,
  ) async {
    devtools.log(
      '❌ Network Error',
      name: 'NetworkService',
      error: {
        'type': err.type,
        'message': err.message,
        'path': err.requestOptions.path,
        'method': err.requestOptions.method,
        'statusCode': err.response?.statusCode,
        'responseData': err.response?.data,
        'stackTrace': err.stackTrace,
      },
    );

    if (err.response?.statusCode == 401) {
      PrefsService prefsService = sl.get<PrefsService>();
      prefsService.clear();


    }
    handler.next(err);
  }

  // Prepare headers with authorization
  Future<Map<String, String>> _prepareHeaders() async {
    final headers = <String, String>{
      'Accept': 'application/json',
      'Content-Type': 'application/json',
    };
    PrefsService prefsService = sl.get<PrefsService>();

    String token = prefsService.getString("token") ?? "";
    print("$token${prefsService.getString("token")}");
    debugPrint('_prepareHeaders: $token');
    if (token.isNotEmpty) {
      headers['Authorization'] = 'Bearer $token';
    }
    debugPrint('_prepareHeaders: $headers');

    return headers;
  }

  // Generic request method with comprehensive error handling
  Future<T?> request<T>({
    required String path,
    required String method,
    dynamic data,
    Map<String, dynamic>? queryParameters,
    ProgressCallback? onSendProgress,
    int maxRetries = 3,
    int currentRetry = 0,
  }) async {
    try {
      final headers = await _prepareHeaders();
      Response<T> response;

      switch (method.toUpperCase()) {
        case 'GET':
          response = await _dio.get<T>(
            path,
            queryParameters: queryParameters,
            options: Options(headers: headers),
          );
          break;
        case 'POST':
          response = await _dio.post<T>(
            path,
            data: data,
            onSendProgress: onSendProgress,
            options: Options(headers: headers),
          );
          break;
        case 'PUT':
          response = await _dio.put<T>(
            path,
            data: data,
            onSendProgress: onSendProgress,
            options: Options(headers: headers),
          );
          break;
        case 'DELETE':
          response = await _dio.delete<T>(
            path,
            data: data,
            options: Options(headers: headers),
          );
          break;
        case 'PATCH':
          response = await _dio.patch<T>(
            path,
            data: data,
            queryParameters: queryParameters,
            options: Options(headers: headers),
          );
          break;
        default:
          throw NetworkException('Unsupported HTTP method: $method');
      }

      if (response.statusCode == 400) {
        final responseData = response.data;
        if (responseData is Map<String, dynamic> &&
            responseData['status'] == 'error' &&
            responseData['message'] != null) {
          throw NetworkException(responseData['message'], response.statusCode);
        }
      }
      return response.data;
    } on DioException catch (e) {
      // Handle rate limiting with exponential backoff
      if (e.response?.statusCode == 429 && currentRetry < maxRetries) {
        // Calculate delay with exponential backoff (2^retry * 1000ms)
        final delay = Duration(milliseconds: (1 << currentRetry) * 1000);
        debugPrint('Rate limited. Retrying after ${delay.inSeconds} seconds...');
        await Future.delayed(delay);
        
        // Retry the request with incremented retry count
        return request<T>(
          path: path,
          method: method,
          data: data,
          queryParameters: queryParameters,
          onSendProgress: onSendProgress,
          maxRetries: maxRetries,
          currentRetry: currentRetry + 1,
        );
      }
      
      if (e.response?.statusCode == 400) {
        final responseData = e.response?.data;
        if (responseData is Map<String, dynamic> &&
            responseData['status'] == 'error' &&
            responseData['message'] != null) {
          throw NetworkException(
              responseData['message'], e.response?.statusCode);
        }
      }
      throw NetworkException(
        e.message ?? 'Request failed',
        e.response?.statusCode,
        e.stackTrace,
      );
    } catch (e, stackTrace) {
      throw NetworkException(
        e.toString(),
        null,
        stackTrace,
      );
    }
  }

  // Cached request method
  Future<T?> requestWithCache<T>({
    required String path,
    required String method,
    dynamic data,
    Map<String, dynamic>? queryParameters,
    bool forceRefresh = false,
  }) async {
    final cacheKey =
        '$path$method${queryParameters?.toString()}${data?.toString()}';

    if (!forceRefresh && _cache.containsKey(cacheKey)) {
      final cachedItem = _cache[cacheKey];
      if (DateTime.now().difference(cachedItem['timestamp']).inMinutes < 5) {
        return cachedItem['data'] as T?;
      }
    }

    try {
      final result = await request<T>(
        path: path,
        method: method,
        data: data,
        queryParameters: queryParameters,
      );

      _cache[cacheKey] = {
        'data': result,
        'timestamp': DateTime.now(),
      };

      return result;
    } catch (e, stackTrace) {
      devtools.log(
        'Cached Request Failed',
        name: 'NetworkService',
        error: e,
        stackTrace: stackTrace,
      );
      rethrow;
    }
  }
}
