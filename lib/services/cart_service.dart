import 'package:one_drop/models/cart.dart';
import 'package:one_drop/models/product.dart';
import 'package:one_drop/services/network_service.dart';
import 'package:one_drop/services/service_locator.dart';

class CartService {
  final NetworkService _networkService = sl.get<NetworkService>();
  CartResponse? _cart;

  void _logError(String method, dynamic error, StackTrace stackTrace) {
    print('❌ Cart Service Error in $method: $error\n$stackTrace');
  }

  /// Get the active cart for the authenticated user
  Future<CartResponse> getCart() async {
    try {
      final response = await _networkService.request<Map<String, dynamic>>(
        path: '/cart',
        method: 'GET',
      );

      print('Cart API Response: $response'); // Debug print

      if (response == null) {
        throw NetworkException('Failed to fetch cart - null response');
      }

      _cart = CartResponse.fromJson(
          response['data']); // Make sure you're accessing the correct data path

      // Load full product details for each cart item
      await _loadFullProductDetails();

      return _cart!;
    } catch (e, stackTrace) {
      print('Error fetching cart: $e\n$stackTrace');

      // إذا كان الخطأ 404، نقوم بإنشاء سلة فارغة بدلاً من إعادة رمي الخطأ
      if (e is NetworkException && e.statusCode == 404) {
        print('Cart not found (404), creating empty cart');
        // إنشاء سلة فارغة
        _cart = CartResponse(
          id: 0,
          userId: 0,
          status: 'empty',
          items: [],
          total: 0,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );
        return _cart!;
      }

      // إعادة رمي الخطأ في الحالات الأخرى
      rethrow;
    }
  }

  /// Load full product details for all items in the cart
  Future<void> _loadFullProductDetails() async {
    if (_cart == null) return;

    try {
      for (var item in _cart!.items) {
        try {
          final productResponse = await _networkService.request<Map<String, dynamic>>(
            path: '/products/${item.productId}',
            method: 'GET',
          );

          if (productResponse != null) {
            print('Product API Response for ID ${item.productId}: $productResponse');
            final product = Product.fromJson(productResponse);
            item.fullProduct = product;
          }
        } catch (e) {
          print('Error loading product ${item.productId}: $e');
          // Continue loading other products even if one fails
          continue;
        }
      }
    } catch (e, stackTrace) {
      print('Error loading full product details: $e\n$stackTrace');
      // Don't rethrow - we want to return cart even with partial product details
    }
  }

  /// Add a product to the cart
  Future<void> addItem(int productId, {int quantity = 1}) async {
    try {
      await _networkService.request<Map<String, dynamic>>(
        path: '/cart',
        method: 'POST',
        data: {
          'product_id': productId,
          'quantity': quantity,
        },
      );
    } catch (e, stackTrace) {
      _logError('addItem', e, stackTrace);
      rethrow;
    }
  }

  /// Update the quantity of a product in the cart
  Future<void> updateItem({
    required int productId,
    required int cartId,
    required int quantity,
  }) async {
    try {
      print('📝 Updating cart item:');
      print('- Cart ID: $cartId');
      print('- Product ID: $productId');
      print('- Quantity: $quantity');

      final response = await _networkService.request<Map<String, dynamic>>(
        path: '/cart/$cartId',
        method: 'PUT',
        data: {
          'product_id': productId,
          'quantity': quantity,
        },
      );

      if (response == null) {
        throw NetworkException('Failed to update cart item - null response');
      }

      print('✅ Cart item updated successfully');
      print('Response: $response');
    } catch (e, stackTrace) {
      _logError('updateItem', e, stackTrace);
      rethrow;
    }
  }

  /// Remove a product from the cart
  Future<void> removeItem(int itemId) async {
    try {
      await _networkService.request<Map<String, dynamic>>(
        path: '/cart/$itemId',
        method: 'DELETE',
      );
    } catch (e, stackTrace) {
      _logError('removeItem', e, stackTrace);
      rethrow;
    }
  }

  /// Clear the cached cart data
  void clearCache() {
    _cart = null;
  }
}
