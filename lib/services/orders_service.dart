import 'package:rxdart/subjects.dart';
import 'dart:developer' as developer;

class OrderService {
  final _onOrderCreated = BehaviorSubject<int?>();

  Stream<int?> get onOrderCreated => _onOrderCreated.stream;

  void addOrderCreated(int? value) {
    developer.log('📦 Order Created: ${value ?? 'null'}', name: 'OrderService');
    _onOrderCreated.add(value);
  }

  void clean() {
    developer.log('🧹 Cleaning Order Service State', name: 'OrderService');
    addOrderCreated(null);
  }

  void dispose() {
    developer.log('🔄 Disposing Order Service', name: 'OrderService');
    _onOrderCreated.close();
  }
}
