import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:dio/dio.dart';
import 'package:one_drop/providers/user_provider.dart';
import 'package:one_drop/services/network_service.dart';

import 'app_lifecycle_service.dart';
import 'orders_service.dart';
import 'prefs_service.dart';
import 'store_service.dart';
import 'cart_service.dart';
import 'product_service.dart';

final sl = GetIt.instance;

mixin ServiceLocator {
  static Future<void> setupLocators() async {
    final prefs = await SharedPreferences.getInstance();
    sl.registerSingleton(PrefsService(prefs: prefs));
    sl.registerSingleton(AppLifecycleService());

    sl.registerLazySingleton(() => GlobalKey<NavigatorState>());

    // Core dependencies
    final dio = Dio()
      ..options.baseUrl = "http://qatrawahda.sa/api"
      ..options.connectTimeout = const Duration(seconds: 30)
      ..options.receiveTimeout = const Duration(seconds: 30)
      ..options.sendTimeout = const Duration(seconds: 30)
      ..interceptors.add(LogInterceptor(
        requestBody: true,
        responseBody: true,
        logPrint: (obj) => debugPrint(obj.toString()),
      ));
    sl.registerLazySingleton(() => dio);
    sl.registerLazySingleton(() => UserProvider());

    // Register NetworkService first as other services depend on it
    sl.registerLazySingleton<NetworkService>(() => NetworkService(sl()));

    // Register services that depend on NetworkService
    sl.registerLazySingleton<CartService>(() => CartService());
    sl.registerLazySingleton<ProductService>(() => ProductService());
    sl.registerLazySingleton<StoreService>(() => StoreService());
    sl.registerLazySingleton<OrderService>(() => OrderService());
  }
}
