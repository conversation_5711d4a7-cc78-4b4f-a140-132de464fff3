import 'dart:convert';
import 'dart:io';

import 'package:bot_toast/bot_toast.dart';
import 'package:flutter/services.dart';

import '../models/ap_payment_request_model.dart';
import '../models/ap_payment_summary_item_model.dart';

class ApplePayService {
  static const platform = MethodChannel('com.ammar.oneDrop/applePay');
  void Function(APPaymentAuthorizedModel)? onPaymentAuthorized;

  ApplePayService() {
    platform.setMethodCallHandler((call) {
      if (call.method == 'userDidAuthorizePayment') {
        return _userDidAuthorizePayment(call.arguments);
      } else if (call.method == 'didFinishPayment') {
        return _didFinishPayment();
      }
      return Future.value();
    });
  }

  Future<void> _didFinishPayment() async {
    // print('--- _didFinishPayment');
  }

  Future<void> _userDidAuthorizePayment(dynamic args) async {
    // print("--- _userDidAuthorizePayment $args");
    // print('--- ${args['token']} - ${args['transactionID']}');
    if (onPaymentAuthorized != null) {
      final model = APPaymentAuthorizedModel(token: args['token'], transactionID: args['transactionID']);
      onPaymentAuthorized!(model);
    }
  }

  Future<bool> canMakeApplePayments() async {
    if (!Platform.isIOS) return false;
    final canMakePayments = await platform.invokeMethod<bool>('canMakePayments');
    return canMakePayments ?? false;
  }

  void startApplePay({
    required List<APPaymentSummaryItemModel> paymentItems,
  }) async {
    BotToast.showLoading();
    final canMakePayments = await canMakeApplePayments();
    // print('--- canMakePayments $canMakePayments');
    if (!canMakePayments) {
      BotToast.closeAllLoading();
      return;
    }
    // final setCountry = await platform.invokeMethod<bool>('setCountryCode', 'AE');
    // print('--- setCountry $setCountry');
    // final setCurrency = await platform.invokeMethod<bool>('setCurrencyCode', 'AED');
    // print('--- setCurrency $setCurrency');
    // final paymentSummaryItems = <APPaymentSummaryItemModel>[
    //   APPaymentSummaryItemModel(amount: 10, label: 'X'),
    //   APPaymentSummaryItemModel(amount: 20, label: 'Y'),
    //   APPaymentSummaryItemModel(amount: 30, label: 'OneDrop'),
    // ];
    // final success = await platform.invokeMethod<bool>(
    //   'setPaymentSummaryItems',
    //   json.encode(paymentSummaryItems),
    // );
    // print('--- setPaymentSummaryItems $success');
    await platform.invokeMethod(
      'initApplePay',
      json.encode(APPaymentRequestModel(
        countryCode: 'SA',
        currencyCode: 'SAR',
        merchantID: 'merchant.com.ammar.oneDrop',
        paymentSummaryItems: paymentItems,
      ).toMap()),
    );
    BotToast.closeAllLoading();
  }
}

class APPaymentAuthorizedModel {
  final String? token;
  final String? transactionID;

  APPaymentAuthorizedModel({required this.token, required this.transactionID});

  factory APPaymentAuthorizedModel.fromJson(Map json) {
    return APPaymentAuthorizedModel(
      token: json['token'],
      transactionID: json['transactionID'],
    );
  }
}
