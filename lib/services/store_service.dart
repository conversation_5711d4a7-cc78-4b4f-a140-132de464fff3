import 'dart:developer' as devtools show log;
import 'package:one_drop/models/cart.dart';
import 'package:one_drop/models/product.dart';
import 'package:one_drop/models/brand.dart';
import 'package:one_drop/services/network_service.dart';
import 'package:one_drop/services/service_locator.dart';

class StoreService {
  static const String baseUrl = 'http://qatrawahda.sa/api';
  final NetworkService _networkService = sl.get<NetworkService>();

  void _logError(String method, dynamic error, StackTrace stackTrace) {
    devtools.log(
      '❌ Store Service Error',
      name: 'StoreService.$method',
      error: error,
      stackTrace: stackTrace,
    );
  }

  Future<List<Product>> getProducts({
    String? search,
    int? brandId,
    int? id,
    String? sortBy,
    String? sortOrder,
    int limit = 10,
  }) async {
    try {
      final queryParams = <String, dynamic>{
        if (search != null) 'search': search,
        if (brandId != null) 'brand_id': brandId,
        if (id != null) 'id': id,
        if (sortBy != null) 'sort_by': sortBy,
        if (sortOrder != null) 'sort_order': sortOrder,
        'limit': limit.clamp(1, 100),
      };

      final response = await _networkService.request<Map<String, dynamic>>(
        path: '/products',
        method: 'GET',
        queryParameters: queryParams,
      );

      if (response == null || response['status'] != 'success') {
        throw Exception(response?['message'] ?? 'Failed to fetch products');
      }

      final List<dynamic> productsData = response['data'] as List<dynamic>;
      return productsData
          .map((json) => Product.fromJson(json as Map<String, dynamic>))
          .toList();
    } catch (e, stackTrace) {
      _logError('getProducts', e, stackTrace);
      rethrow;
    }
  }

  Future<Product> getProductDetails(int id) async {
    try {
      final response = await _networkService.request<Map<String, dynamic>>(
        path: '/products/$id',
        method: 'GET',
      );

      if (response == null || response['status'] != 'success') {
        throw Exception(
            response?['message'] ?? 'Failed to fetch product details');
      }

      return Product.fromJson(response['data'] as Map<String, dynamic>);
    } catch (e, stackTrace) {
      _logError('getProductDetails', e, stackTrace);
      rethrow;
    }
  }

  Future<CartResponse> getCart() async {
    try {
      final response = await _networkService.request<Map<String, dynamic>>(
        path: '/cart',
        method: 'GET',
      );

      if (response != null) {
        return CartResponse.fromJson(response);
      } else {
        throw NetworkException('Failed to fetch cart');
      }
    } catch (error, stackTrace) {
      _logError('getCart', error, stackTrace);
      rethrow;
    }
  }

  Future<CartResponse> addToCart(int productId, int quantity) async {
    try {
      final response = await _networkService.request<Map<String, dynamic>>(
        path: '/cart',
        method: 'POST',
        data: {
          'product_id': productId,
          'quantity': quantity,
        },
      );

      if (response != null) {
        return CartResponse.fromJson(response);
      } else {
        throw NetworkException('Failed to add item to cart');
      }
    } catch (error, stackTrace) {
      _logError('addToCart', error, stackTrace);
      if (error is NetworkException && error.statusCode == 400) {
        rethrow;
      }
      throw NetworkException('Failed to add item to cart. Please try again.');
    }
  }

  Future<CartResponse> updateCartItem(int productId, int quantity) async {
    try {
      final response = await _networkService.request<Map<String, dynamic>>(
        path: '/cart',
        method: 'POST',
        data: {
          'product_id': productId,
          'quantity': quantity,
        },
      );

      if (response != null) {
        return CartResponse.fromJson(response);
      } else {
        throw NetworkException('Failed to update cart item');
      }
    } catch (error, stackTrace) {
      _logError('updateCartItem', error, stackTrace);
      rethrow;
    }
  }

  Future<void> removeFromCart(int productId) async {
    try {
      await _networkService.request<Map<String, dynamic>>(
        path: '/cart',
        method: 'POST',
        data: {
          'product_id': productId,
          'action': 'remove',
        },
      );
    } catch (error, stackTrace) {
      _logError('removeFromCart', error, stackTrace);
      rethrow;
    }
  }

  Future<void> removeAllFromCart() async {
    try {
      final cart = await getCart();
      if (cart.items.isEmpty) {
        return;
      }

      // Remove each item from the cart
      for (final item in cart.items) {
        try {
          await removeFromCart(item.productId);
        } catch (error, stackTrace) {
          _logError('removeAllFromCart - single item', error, stackTrace);
          // Continue with next item even if one fails
          continue;
        }
      }
    } catch (error, stackTrace) {
      _logError('removeAllFromCart', error, stackTrace);
      rethrow;
    }
  }

  Future<List<Brand>> getBrands() async {
    try {
      final response = await _networkService.request<Map<String, dynamic>>(
        path: '/brands',
        method: 'GET',
      );

      if (response != null && response['data'] != null) {
        final List<dynamic> brandsJson = response['data'] as List<dynamic>;
        return brandsJson
            .map((json) => Brand.fromJson(json as Map<String, dynamic>))
            .toList();
      }
      return [];
    } catch (error, stackTrace) {
      _logError('getBrands', error, stackTrace);
      rethrow;
    }
  }
}
