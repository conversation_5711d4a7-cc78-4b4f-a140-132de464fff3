import 'dart:async';

class CartUpdateService {
  static final CartUpdateService _instance = CartUpdateService._internal();
  factory CartUpdateService() => _instance;
  CartUpdateService._internal();

  final _cartUpdateController = StreamController<bool>.broadcast();
  Stream<bool> get onCartUpdate => _cartUpdateController.stream;

  void notifyCartUpdated() {
    _cartUpdateController.add(true);
  }

  void dispose() {
    _cartUpdateController.close();
  }
}
