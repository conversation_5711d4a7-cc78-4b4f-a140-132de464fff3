import 'package:flutter/material.dart';

class AppLifecycleService extends ChangeNotifier {
  bool _isAppInBackground = false;
  bool get isAppInBackground => _isAppInBackground;

  void appDetached() {
    _isAppInBackground = true;
    notifyListeners();
  }

  void appInactive() {
    _isAppInBackground = true;
    notifyListeners();
  }

  void appResumed() {
    _isAppInBackground = false;
    notifyListeners();
  }

  void appPaused() {
    _isAppInBackground = true;
    notifyListeners();
  }

  @override
  void dispose() {
    super.dispose();
  }
}
