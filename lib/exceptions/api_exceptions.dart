/// Base class for all API exceptions
 class ApiException implements Exception {
  final String message;
  final int? statusCode;

  const ApiException(this.message, {this.statusCode});

  @override
  String toString() => 'ApiException: $message${statusCode != null ? ' (Status: $statusCode)' : ''}';
}

/// Exception thrown when a request requires authentication but the user is not authenticated
class UnauthorizedException extends ApiException {
  const UnauthorizedException({
    String message = 'Unauthorized',
    int statusCode = 401,
  }) : super(message, statusCode: statusCode);
}

/// Exception for resource not found scenarios
class NotFoundException extends ApiException {
  const NotFoundException({
    String message = 'Resource not found',
    int statusCode = 404,
  }) : super(message, statusCode: statusCode);
}

/// Exception for invalid request parameters
class BadRequestException extends ApiException {
  const BadRequestException({
    String message = 'Bad request',
    int statusCode = 400,
  }) : super(message, statusCode: statusCode);
}

/// Exception for server-side errors
class ServerException extends ApiException {
  const ServerException({
    String message = 'Internal server error',
    int statusCode = 500,
  }) : super(message, statusCode: statusCode);
}