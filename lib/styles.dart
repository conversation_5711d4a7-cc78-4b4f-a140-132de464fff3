import 'package:flutter/material.dart';

mixin Styles {
  // Brand Colors
  static const primaryColor = Color(0xff00cdc8);
  static const secondaryColor = Color(0xff0a374a);
  static const accentColor = Color(0xFFFF6B6B);
  
  // Neutral Colors
  static const backgroundColor = Colors.white;
  static const surfaceColor = Colors.white;
  static final lightGrey = Color.fromARGB((255 * (32 / 100)).round(), 204, 210, 227);
  static const textPrimaryColor = Color(0xFF2D3142);
  static const textSecondaryColor = Color(0xFF9098B1);
  
  // Semantic Colors
  static const successColor = Color(0xFF4CAF50);
  static const warningColor = Color(0xFFFFA726);
  static const errorColor = Color(0xFFEF5350);
  static const infoColor = Color(0xFF2196F3);

  // Spacing
  static const spacing2 = 2.0;
  static const spacing4 = 4.0;
  static const spacing8 = 8.0;
  static const spacing12 = 12.0;
  static const spacing16 = 16.0;
  static const spacing24 = 24.0;
  static const spacing32 = 32.0;
  static const spacing48 = 48.0;

  // Border Radius
  static final borderRadiusSmall = BorderRadius.circular(4);
  static final borderRadiusMedium = BorderRadius.circular(8);
  static final borderRadiusLarge = BorderRadius.circular(12);
  static final borderRadiusXLarge = BorderRadius.circular(16);
  static final borderRadiusCircular = BorderRadius.circular(999);

  // Shadows
  static final shadowSmall = BoxShadow(
    color: Colors.black.withOpacity(0.04),
    blurRadius: 4,
    offset: const Offset(0, 2),
  );
  static final shadowMedium = BoxShadow(
    color: Colors.black.withOpacity(0.08),
    blurRadius: 8,
    offset: const Offset(0, 4),
  );
  static final shadowLarge = BoxShadow(
    color: Colors.black.withOpacity(0.12),
    blurRadius: 16,
    offset: const Offset(0, 8),
  );

  // Animation Durations
  static const durationFast = Duration(milliseconds: 200);
  static const durationNormal = Duration(milliseconds: 300);
  static const durationSlow = Duration(milliseconds: 500);
}

// Button Styles
final linkButtonStyle = ButtonStyle(
  backgroundColor: MaterialStateProperty.all(Colors.transparent),
  foregroundColor: MaterialStateProperty.all(Styles.primaryColor),
  padding: MaterialStateProperty.all(const EdgeInsets.all(0)),
  overlayColor: MaterialStateProperty.all(Styles.primaryColor.withOpacity(0.1)),
);

// Input Styles
final kInputOutlineBorder = OutlineInputBorder(
  borderRadius: Styles.borderRadiusLarge,
  borderSide: BorderSide(color: Colors.grey[300]!),
);

final kInputOutlineFocusedBorder = kInputOutlineBorder.copyWith(
  borderSide: const BorderSide(color: Styles.primaryColor, width: 2),
);

// Common Styles
const kAppBarItemsPadding = EdgeInsets.only(bottom: Styles.spacing16);
final kBoxShadow = Styles.shadowMedium;
